# -*- coding: utf-8 -*-
"""
Created on Fri Jul 15 14:43:00 2022

@author: zzw

生成 T0 母单
"""

import os, sys
import time
import datetime
from loguru import logger
import pandas as pd
import numpy as np
from tqdm import tqdm
import sshtunnel
import json


# 将项目路径添加到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.abspath(os.path.join(current_dir, ".."))  # 通过..返回上一级目录
sys.path.append(project_dir)


from misc.Readstockfile import read_remote_file, write_file
# from misc.ssh_conn import sftp_clent_work
# from misc.zzw_tun import tun_zhongxinapi_dict, tun_zhongxinliangrong_dict
from data_utils.trading_calendar import Calendar
from misc.tools import get_stock_close, find_latest_remote_file_on_date, get_account_sftp
from misc.ssh_conn import sftp_clent_inner, sftp_clent_dav
from misc.utils import nowtime_to_tradedate, compare_diff
from misc import tools
from misc import Account
from misc.feishu import msgBot


from misc import email
from misc import utils
from misc.Readstockfile import read_file
# from accounts_config.kf_accounts_basic import get_account_sftp


execute_email_host = 'imap.feishu.cn'
execute_email_addr = '<EMAIL>'
execute_email_password = 'V4VwSVVGCK8xanGT'


def read_zheshang_t0_ticker_list_file(trade_date):
    search_start = Calendar.last_trading_day(trade_date).strftime('%Y%m%d')
    search_end = trade_date
    today = nowtime_to_tradedate(bytime='235959')
    files_path = email.download_excel_attachments(
        server=execute_email_host,
        email_user=execute_email_addr,
        email_pass=execute_email_password,
        folder='浙商证券皓兴T0券池',
        start_date=search_start,
        end_date=search_end,
        title_reg=f'{trade_date}',    # f'{trade_date} - Haoxing T0股票票池',
        attachment_reg='T0_StockList.csv',
    )
    if len(files_path) == 0:
        raise FileNotFoundError(f' {trade_date} 没有找到附件')
    elif len(files_path) > 1:
        files_path.sort(reverse=True)
        logger.error(f'{trade_date} 找到多个邮件附件')
        print(files_path)
    file_path = files_path[0]
    df = read_file(file_path, src_type='local')
    os.remove(file_path)
    return df   # ['symbol', 'lable_date']

# def read_feitu_t0_ticker_list_file(account_name, trade_date):
#     file = f'{trade_date}.csv'
#     path = os.path.join(account_name, 'account', file)
#     df = read_remote_file(path, src_type='dav')
#     return df[['股票代码']].rename(columns={'股票代码':'symbol'})




def generate_atx_t0_order(account_name, trade_date, hold, ticker_list, **kwargs):
    account_cn = kwargs.get('account_cn')
    algorithm = kwargs.get('algorithm')
    start_time = kwargs.get('start_time', '093000')
    default_endtime = kwargs.get('default_endtime', '145700')
    preference = kwargs.get('preference', '保守')
    now_str_short = datetime.datetime.now().strftime('%H%M')
    algorithm_params = kwargs.get('algorithm_params', {})
    algorithm_params.update({'风险偏好': preference, '篮子编号': now_str_short})
    algorithm_params = ':'.join([f'{key}={value}' for key, value in algorithm_params.items()])
    
    target = tools.get_target_by_date(account_name, trade_date)
    t0 = pd.merge(hold, target, on='ticker', how='outer', suffixes=('', '_target'))
    t0 = t0.fillna(0)
    t0['t0_volume'] = t0[['available_volume', 'volume_target']].min(axis=1)
    t0['t0_volume'] = t0['t0_volume'].div(100).astype('int').mul(100)
    # 筛选 688 股票股数小于 200 的不要
    t0['t0_volume'] = np.where((t0['ticker'] >= 688000) & (t0['t0_volume'] < 200), 0, t0['t0_volume'])
    t0 = t0[t0['t0_volume'] > 0][['ticker', 't0_volume']]
    # t0['volume'] = t0['volume'].astype(int)
    # t0['available_volume'] = t0['volume']
    
    
    t0 = t0[t0['ticker'].astype(int).isin(ticker_list['symbol'])]
    cols = [
        '算法类型',
        '账户名称',
        '算法实例',
        '证券代码',
        '任务数量',
        '买入方向',
        '卖出方向',
        '开始时间',
        '结束时间',
        '涨跌停设置',
        '过期后执行',
        '其他参数',
        '交易市场',
    ]
    order = pd.DataFrame(columns=cols)
    order['证券代码'] = t0['ticker'].apply(utils.symbol_with_market).tolist()
    order['任务数量'] = t0['t0_volume'].tolist()
    order['买入方向'] = '买入'
    order['卖出方向'] = '卖出'
    order['算法类型'] = 'T0'
    order['账户名称'] = account_cn
    order['算法实例'] = algorithm
    order['开始时间'] = '{}T{}000'.format(trade_date, start_time)
    order['结束时间'] = '{}T{}000'.format(trade_date, default_endtime)
    order['涨跌停设置'] = '涨停不买跌停不卖'
    order['过期后执行'] = '否'
    order['其他参数'] = algorithm_params
    order['交易市场'] = order['证券代码'].apply(utils.symbol_to_exchange_cn)
    return order
    
def send_order(account_name, order, **kwargs):
    date = datetime.datetime.now().strftime('%Y%m%d')
    order_dir = 'order'
    order_file_suffix = kwargs.get('order_file_suffix', '')
    order_file = f'{account_name}_{date}_T0{order_file_suffix}.csv'
    order_path = os.path.join(account_name, order_dir, order_file)
    
    # sftp_type = get_account_sftp(account_name)['ftp_type']
    write_file(order, file_type='csv', dest_path=order_path, dest_type='dav', index=False)
    
    
    
    
    
if __name__ == '__main__':
    trade_date = nowtime_to_tradedate(bytime='235959')
    account_args = {
        # '远航安心中性2号_浙商' : {
        #     'account_cn' : '宽辅远航安心市场中性2号',
        #     'algorithm'  : 'HX_SMART_T0',
        #     'preference' : '保守',
        #     'order_file_suffix' : '',
        #     'start_time' : '101000',
        # },
        # # '宽辅金品中国专享' : {
        # #     'account_cn' : '********',
        # #     'algorithm'  : 'FT_T0',
        # #     'preference' : '保守',
        # #     'order_file_suffix' : '',
        #     # 'start_time' : '101000',
        # # },
        # '远航安心中性6号_国君' : {
        #     'account_cn' : '宽辅远航安心市场中性6号私募（DMA）',
        #     'algorithm'  : 'JINGLE_T0',
        #     # 'algorithm'  : 'kf_t0',
        #     'preference' : '保守',
        #     'order_file_suffix' : '',
        #     'start_time' : '093000',
        # },
        '宽辅臻好专享_国君' : {
            'account_cn' : '宽辅臻好专享证券',
            'algorithm'  : 'ld_t0',
            'preference' : '保守',
            'order_file_suffix' : '',
            'start_time' : '093000',
        },
    }
    
    # TODO
    for account_name in account_args.keys():
        order_args = account_args[account_name]
        hold = tools.get_latest_hold(account_name, trade_date, config={})

        # =============== ticker list 券池
        # if account_name == '远航安心中性2号_浙商':
        #     ticker_list = read_zheshang_t0_ticker_list_file(trade_date)
        # ===============
        if account_name in [
            '宽辅金品中国专享', 
            '远航安心中性6号_国君',
            '远航安心中性2号_浙商',
            '宽辅臻好专享_国君',
        ]:
            ticker_list = hold[['ticker']].rename(columns={'ticker': 'symbol'})
        # ===============
    
        order =generate_atx_t0_order(account_name, trade_date, hold, ticker_list, **order_args)
        
        # if account_name in ['远航安心中性6号_国君']:
        #     order = order.iloc[0:50]
        
        
        print(f'{account_name} {trade_date} T0 order:')
        print(order.head())
        print(order.shape)
        send_order(account_name, order, **order_args)
