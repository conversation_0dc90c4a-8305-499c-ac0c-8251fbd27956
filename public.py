#%%
import pandas as pd
import tempfile
import os
import sshtunnel
import pymysql
import sqlalchemy
from sqlalchemy import create_engine
from urllib.parse import quote_plus, quote

from misc.Readstockfile import read_file
from misc.standard_table import (
    standardize_hold, 
    standardize_deal, 
    standardize_order, 
    standardize_marginhold,
    standardize_margindeal,
    standardize_marginaccount,
    standardize_futureaccount,
    standardize_stockaccount
)

from misc.ssh_conn import sftp_clent_dav
# from misc.zzw_tun import tun_datayes_dict
from data_utils.trading_calendar import Calendar

from misc.tools import get_datayes_dbconn
from misc.utils import re_find, cffex_multiplier
#%%

# query_mkt_limit="""
# SELECT * 
# FROM mkt_limit

# WHERE TRADE_DATE="{}"
# """.format(date)

def get_stock_close(date):
    query_close="""
    SELECT * 
    FROM mkt_equd
    WHERE TRADE_DATE="{}"
    """
    # with sshtunnel.SSHTunnelForwarder(**tun_datayes_dict) as tun:
    # conn = pymysql.connect(host="**************", database="datayes", user="datayesRO",
    #                     password="datayesRO", port=3306)
    # engine = create_engine('mysql+pymysql://datayesRO:datayesRO@**************:3306/datayes')
    engine = get_datayes_dbconn()
    with engine.connect() as conn:
        close = pd.read_sql(query_close.format(date), conn)
    close = close[['TICKER_SYMBOL', 'CLOSE_PRICE', 'TRADE_DATE']]
    close.columns = ['ticker', 'close', 'date']
        # close['symbol'] = close['symbol'].astype(int)
    return close

def get_stock_adj_close(date):
    # engine = create_engine('mysql+pymysql://datayesRO:datayesRO@**************:3306/datayes')
    engine = get_datayes_dbconn()
    query="""
    SELECT 
        TICKER_SYMBOL as ticker,
        CLOSE_PRICE_1 as close,
        TRADE_DATE as date
    FROM mkt_equd_adj

    WHERE TRADE_DATE="{}"
    """.format(date)
    with engine.connect() as conn:
        close=pd.read_sql(query,conn)
    # close['ticker'] = close['ticker'].astype(int)
    return close 

def get_future_price(date):
    query_close = """
    SELECT * 
    FROM mkt_futd
    WHERE TRADE_DATE="{}"
    """
    # with sshtunnel.SSHTunnelForwarder(**tun_datayes_dict) as tun:
    # conn = pymysql.connect(host="**************", database="datayes", user="datayesRO",
    #                     password="datayesRO", port=3306)
    # engine = create_engine('mysql+pymysql://datayesRO:datayesRO@**************:3306/datayes')
    engine = get_datayes_dbconn()
    with engine.connect() as conn:
        close = pd.read_sql(query_close.format(date), conn)
    close = close[['TICKER_SYMBOL', 'PRE_SETTL_PRICE', 'PRE_CLOSE_PRICE', 'SETTL_PRICE', 'CLOSE_PRICE', 'TRADE_DATE']]
    close.columns = ['ticker', 'pre_settle', 'pre_close', 'settle', 'close', 'date']
    return close

def get_index_close(index, date):
    # query="""
    # SELECT 
    #                 index_code as ticker,
    #                 trade_date as date,
    #                 tclose as index_close
                    
    #             FROM suntime_up.qt_idx_daily
    #             WHERE index_code IN ('{}')
    #                     AND exchange IN ('001002','001001')
    #                     AND trade_date = "{}" 
    # """.format(index, date)
    # conn = pymysql.connect(host="*************", user="usr4",password="a&Y07uo*@9gg!CLU", port=33105)
    query_close = """
    SELECT 
        TICKER_SYMBOL as ticker,
        CLOSE_INDEX as index_close
    FROM mkt_idxd
    WHERE 
    TICKER_SYMBOL IN ('{}')
    AND
    TRADE_DATE="{}"
    """.format(index, date)
    # engine = create_engine('mysql+pymysql://datayesRO:datayesRO@**************:3306/datayes')
    engine = get_datayes_dbconn()
    with engine.connect() as conn:
        close = pd.read_sql(query_close, conn)
    return close


def get_main_index_close(date):
    query_close = """
    SELECT 
        TICKER_SYMBOL as ticker,
        TRADE_DATE as date,
        CLOSE_INDEX as close,
        PRE_CLOSE_INDEX as pre_close
    FROM mkt_idxd
    WHERE 
    TICKER_SYMBOL IN ('000300', '000016', '000852', '000905')
    AND
    TRADE_DATE="{}"
    """.format(date)
    # engine = create_engine('mysql+pymysql://datayesRO:datayesRO@**************:3306/datayes')
    engine = get_datayes_dbconn()
    with engine.connect() as conn:
        close = pd.read_sql(query_close, conn)
    close['ticker'] = close['ticker'].replace({'000300': '沪深300', '000016': '上证50', '000852': '中证1000', '000905': '中证500'})
    return close

#%%


class Portfolio:
    def __init__(self, source_type=None, source_path=None, date=None, data=None, **kwargs):
        self.source_type = source_type
        self.source_path = source_path
        self.date = date
        self.sftp_methods = {'dav': sftp_clent_dav, 
                             'inner': sftp_clent_inner,
                             'ninner': sftp_clent_ninner,
                             'trade': sftp_clent_trade
                             }
        self.data = data
        self.stock = None
        self.workingdir = 'tmp'
        self.fullcols = kwargs.get('fullcols', False)
        self.standardized = kwargs.get('standardized', False)
        self.client_type = kwargs.get('client_type')
        self.close = kwargs.get('close')
        self.future_price = kwargs.get('future_price')
        self.pre_close = kwargs.get('pre_close')
        if not os.path.exists(self.workingdir):
            os.makedirs(self.workingdir)
        self.tempfile = os.path.join(self.workingdir, 'tmpfile')
        # self.update_data()

    def _get_data(self, **kwargs):
        if self.source_type == 'df':
            if not isinstance(self.data, pd.DataFrame):
                raise ValueError("data input should be a DataFrame")
            self.data = self.data
        
        elif self.source_type == 'local':
            self.data = read_file(self.source_path, 
                                #   encoding='gbk',
                                  dtype={'合同编号':str, 
                                    '成交编号':str, 
                                    '委托编号':str,
                                    '委托序号':str, 
                                    '成交序号':str,
                                    'OrderId':str,
                                    'DealId':str,
                                    'BizId':str,
                                    '意向编号':str,
                                    'order_ref':str,
                                    'deal_ref':str,},
                                  **kwargs)

        else:
            sftp = self.sftp_methods[self.source_type]
            extension = os.path.splitext(self.source_path)[-1].lower()
            if extension in ['.xls', '.xlsx', '.csv', '.dbf']:
                with tempfile.NamedTemporaryFile(dir='tmp', delete=True, suffix=extension) as temp_file:
                    sftp.get(self.source_path, temp_file.name)
                    self.data = read_file(temp_file.name, 
                                        #   encoding='gbk',
                                    dtype={'合同编号':str, 
                                        '成交编号':str, 
                                        '委托编号':str,
                                        '委托序号':str, 
                                        '成交序号':str,
                                        'OrderId':str,
                                        'BizId':str,
                                        '意向编号':str,
                                        'order_ref':str,
                                        'deal_ref':str,},
                                        **kwargs)
            # sftp.get(self.source_path, self.tempfile)
            # self.data = read_file(self.tempfile, dtype={'合同编号':str, 
            #                             '成交编号':str, 
            #                             '委托编号':str,
            #                             '委托序号':str, 
            #                             '成交序号':str
            #                             'OrderId':str,
            #                             'BizId':str}
            #                             )

    def _standardize(self):
        pass
    
    def update_data(self):
        self._get_data()
        self._standardize()
        if self.stock is not None and self.stock['ticker'].dtype == 'int':
            self.stock['ticker'] = self.stock['ticker'].apply(lambda x: str(x).zfill(6))
  
        if self.data is not None and self.data['ticker'].dtype == 'int':
            self.data['ticker'] = self.data['ticker'].apply(lambda x: str(x).zfill(6))
        
    def __positionSubHold(self):
        pass
                
class Hold(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, **kwargs):
        super().__init__(source_type, source_path, date, **kwargs)
    
    def _standardize(self):
        self.data = standardize_hold(self.data, client_type=self.client_type)
        self.data = self.data[['代码', '持仓量']]
        self.data.columns = ['ticker', 'volume']
    
    def summarize(self):
        self.attach_close()
        self.data['hold_value'] = self.data['volume'] * self.data['close']
        self.total_volume = self.data['volume'].abs().sum()
        self.total_value = self.data['hold_value'].abs().sum()
        
    def attach_close(self):
        if self.close is not None:
            close = self.close
        else:
            close = get_stock_close(self.date)
        close = close[pd.to_datetime(close['date']) == self.date]
        self.data = self.data.merge(close, how='left', left_on='ticker', right_on='ticker')

        
    # def __sub__(self, other):
    #     if isinstance(other, Position):
    #         combined_data = pd.concat([self.data.set_index('ticker'),
    #                                    other.data.set_index('ticker')], 
    #                                     axis=1, join='outer')
    #         combined_data = combined_data.fillna(0)
    #         combined_data['volume'] = combined_data['target_volume'] - combined_data['volume']
    #         combined_data = combined_data[combined_data['volume'] != 0].reset_index()[['ticker', 'volume']]
    #         combined_data.sort_values('ticker', inplace=True, ignore_index=True)
    #         return Delta(data=combined_data, delta_type='target')
    #     else:
    #         raise ValueError("Unsupported operand type for -")

    
class MarginHold(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, **kwargs):
        super().__init__(source_type, source_path, date, **kwargs)
    
    def _standardize(self):
        self.data = standardize_marginhold(self.data, client_type=self.client_type)
        self.data = self.data[['代码', '多空状态', '持仓量', '可用数量']]
        self.data.columns = ['ticker', 'longshort', 'volume', 'available_volume']
    
    def update_data(self):
        self._get_data()
        self._standardize()
        if self.data['ticker'].dtype == 'int':
            self.data['ticker'] = self.data['ticker'].apply(lambda x: str(x).zfill(6))
        self.sep_stock_future()
        
    def sep_stock_future(self):
        is_future_mask = self.data['ticker'].apply(lambda x: re_find('^IC|^IF|^IH|^IM|^中证500$|^中证1000$|^沪深300$|^上证50$', x)).astype(bool)
        self.stock = self.data[~is_future_mask]
        self.future = self.data[is_future_mask]
        self.future = self.future[['ticker', 'longshort', 'volume']]
        self.data = None


    def summarize(self):
        self.attach_close()
        self.stock_volume = self.stock['volume'].abs().sum()
        self.stock['hold_value'] = self.stock['volume'] * self.stock['close']
        self.stock_value = self.stock['hold_value'].sum()
        
        # future
        self.future['multiplier'] = self.future['ticker'].apply(cffex_multiplier)
        self.future_value_long_close = \
            (self.future['volume'] * self.future['multiplier'] * self.future['close'])[self.future['longshort'] == '多头'].sum()
        self.future_value_long_settle = \
            (self.future['volume'] * self.future['multiplier'] * self.future['settle'])[self.future['longshort'] == '多头'].sum()
        
        # 空头市值为负
        self.future_value_short_close = \
            -(self.future['volume'] * self.future['multiplier'] * self.future['close'])[self.future['longshort'] == '空头'].sum()
        self.future_value_short_settle = \
            -(self.future['volume'] * self.future['multiplier'] * self.future['settle'])[self.future['longshort'] == '空头'].sum()
            
        self.future_volume_long = self.future['volume'][self.future['longshort'] == '多头'].sum()
        self.future_volume_short = self.future['volume'][self.future['longshort'] == '空头'].sum()
        
        # if 'hold_value' in self.stock.columns:
        #     self.total_stock_value = self.stock['hold_value'].sum()

        # elif 'last_price' in self.stock.columns:
        #     self.stock['hold_value'] = self.stock['hold_volume'] * self.stock['last_price']
        #     self.total_stock_value = self.stock['hold_value'].sum()
        # else:

    def attach_close(self):
        if self.close is not None:
            close = self.close
        else:
            close = get_stock_close(self.date)
            
        if self.future_price is not None:
            future_price = self.future_price
        else:
            future_price = get_future_price(self.date)
            main_future_close = get_main_index_close(self.date)
            main_future_close['pre_settle'] = main_future_close['pre_close']
            main_future_close['settle'] = main_future_close['close']
            future_price = pd.concat([main_future_close, future_price])

        close = close[pd.to_datetime(close['date']) == self.date]
        future_price = future_price[pd.to_datetime(future_price['date']) == self.date]
        self.stock = self.stock.merge(close, how='left', left_on='ticker', right_on='ticker')
        self.future = self.future.merge(future_price, how='left', left_on='ticker', right_on='ticker')
        
    
class FutureHold(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, **kwargs):
        super().__init__(source_type, source_path, date, **kwargs)
    
    def _standardize(self):
        self.future = standardize_marginhold(self.data, client_type=self.client_type)
        self.future = self.future[['代码', '多空状态', '持仓量' ]]
        self.future.columns = ['ticker', 'longshort', 'volume']
        self.data = None
        
    def summarize(self):
        self.attach_close()

        # future
        self.future['multiplier'] = self.future['ticker'].apply(cffex_multiplier)
        self.future_value_long_close = \
            (self.future['volume'] * self.future['multiplier'] * self.future['close'])[self.future['longshort'] == '多头'].sum()
        self.future_value_long_settle = \
            (self.future['volume'] * self.future['multiplier'] * self.future['settle'])[self.future['longshort'] == '多头'].sum()
        
        # 空头市值为负
        self.future_value_short_close = \
            -(self.future['volume'] * self.future['multiplier'] * self.future['close'])[self.future['longshort'] == '空头'].sum()
        self.future_value_short_settle = \
            -(self.future['volume'] * self.future['multiplier'] * self.future['settle'])[self.future['longshort'] == '空头'].sum()
            
        self.future_volume_long = self.future['volume'][self.future['longshort'] == '多头'].sum()
        self.future_volume_short = self.future['volume'][self.future['longshort'] == '空头'].sum()
        
    def attach_close(self):
        if self.future_price is not None:
            future_price = self.future_price
        else:    
            future_price = get_future_price(self.date)
            main_future_close = get_main_index_close(self.date)
            main_future_close['pre_settle'] = main_future_close['pre_close']
            main_future_close['settle'] = main_future_close['close']
            future_price = pd.concat([main_future_close, future_price])
            # print(future_price.head())
        future_price = future_price[pd.to_datetime(future_price['date']) == self.date]
        self.future = self.future.merge(future_price, how='left', left_on='ticker', right_on='ticker')
    

class Deal(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, stock_commission_rate=0.00012, stock_tax_rate=0.0005, **kwargs):
        # self.date = date
        super().__init__(source_type, source_path, date, **kwargs)
        self.stock_commission_rate = stock_commission_rate if stock_commission_rate is not None else 0.00012
        self.stock_tax_rate = stock_tax_rate if stock_tax_rate is not None else 0.0005
    
    def _standardize(self):
        if self.standardized == False:
            self.data = standardize_deal(self.data, self.date, client_type=self.client_type)
        # self.data = standardize_deal(self.data, self.date)
        self.data = self.data[['代码', '买卖标记', '成交均价', '成交数量']]
        self.data.columns = ['ticker', 'BS_flag', 'price', 'volume']
        
    def summarize(self):
        self.data['deal_value'] = self.data['price'] * self.data['volume']
        self.total_value = self.data['deal_value'].abs().sum()
        self.total_volume = self.data['volume'].sum()
        self.buy_volume = (self.data['volume'][self.data['BS_flag'] == '买入']).sum()
        self.sell_volume = (self.data['volume'][self.data['BS_flag'] == '卖出']).sum()
        self.buy_value = (self.data['deal_value'][self.data['BS_flag'] == '买入']).sum()
        self.sell_value = (self.data['deal_value'][self.data['BS_flag'] == '卖出']).sum()
        self.stock_commission = self.total_value * self.stock_commission_rate
        self.stock_tax = self.sell_value * self.stock_tax_rate
        self.data = self.data[['ticker', 'BS_flag', 'volume', 'price', 'deal_value']]        
        # self.data['tmp_bs'] = self.data['BS_flag'].apply(lambda x: 1 if x=='买入' else -1) * self.data['volume']  # +/-

        # self.data['volume'] = self.data['volume'].abs()
        
        # self.data = self.data[['ticker', 'volume', 'BS_flag', 'deal_value', 'buy_volume', 'sell_volume']].groupby(['ticker']).sum().reset_index()
        # self.data['price'] = self.data['deal_value'] / self.data['volume']
    # def summarize(self):
        # self.total_shares = self.data['volume'].abs().sum()
        # self.total_value = self.data['deal_value'].abs().sum()
        
        
class MarginDeal(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, stock_commission_rate=0.00012, stock_tax_rate=0.0005, future_commission_rate=0.000025, **kwargs):
        # self.date = date
        super().__init__(source_type=source_type, source_path=source_path, date=date, **kwargs)
        self.stock_commission_rate = stock_commission_rate
        self.stock_tax_rate = stock_tax_rate
        self.future_commission_rate = future_commission_rate
    
    def adapt_cols(self, df):
        if self.fullcols == True:
            self._fullcols = ['date', 'time', 'ticker', 'operation', 'longshort', 'price', 'volume', 'order_ref', 'deal_ref']
            df = df[self._fullcols]
        else:
            df = df['ticker', 'operation', 'longshort', 'price', 'volume']
        return df
    
    def _standardize(self):
        self.data = standardize_margindeal(self.data, self.date, client_type=self.client_type)
        self.data = self.data[["成交日期", '成交时间', '合同编号', '成交编号', '代码', '开平方向', '多空状态', '成交均价', '成交数量']]
        self.data = self.data.rename(columns={
            '成交日期': 'date',
            '成交时间': 'time',
            '代码': 'ticker',
            '开平方向': 'operation',
            '多空状态': 'longshort',
            '成交数量': 'volume',
            '成交均价': 'price',
            '合同编号': 'order_ref',
            '成交编号': 'deal_ref',
        })
        self.data = self.adapt_cols(self.data)
    
    def update_data(self):
        self._get_data()
        self._standardize()
        if self.data['ticker'].dtype == 'int':
            self.data['ticker'] = self.data['ticker'].apply(lambda x: str(x).zfill(6))
        self.sep_stock_future()
    
    def sep_stock_future(self):
        is_future_mask = self.data['ticker'].apply(lambda x: re_find('^IC|^IF|^IH|^IM|^中证500$|^中证1000$|^沪深300$|^上证50$', x)).astype(bool)
        self.stock = self.data[~is_future_mask]
        self.stock.reset_index(drop=True, inplace=True)
        self.future = self.data[is_future_mask]
        self.future = self.adapt_cols(self.future)
        self.future.reset_index(drop=True, inplace=True)
        self.data = None
        
    def summarize(self):
        self.future['multiplier'] = self.future['ticker'].apply(cffex_multiplier)
        self.future['deal_value'] = self.future['price'] * self.future['volume'] * self.future['multiplier']
        self.future_deal_value_long = \
            self.future['deal_value'][self.future['longshort'] == '多头'][self.future['operation'] == '平仓'].sum() - \
            self.future['deal_value'][self.future['longshort'] == '多头'][self.future['operation'] == '开仓'].sum()
        self.future_deal_value_short = \
            -(
            self.future['deal_value'][self.future['longshort'] == '空头'][self.future['operation'] == '平仓'].sum() - \
            self.future['deal_value'][self.future['longshort'] == '空头'][self.future['operation'] == '开仓'].sum()
            )
        self.future_deal_volume_long = self.future['volume'][self.future['longshort'] == '多头'].sum()
        self.future_deal_volume_short = self.future['volume'][self.future['longshort'] == '空头'].sum()
        self.future_commission = (self.future_deal_value_long + self.future_deal_value_short) * self.future_commission_rate
        self.future = self.adapt_cols(self.future)
        # self.future = self.future[['ticker', 'operation', 'longshort', 'price', 'volume']]
        
        # summarize
        self.stock['deal_value'] = self.stock['price'] * self.stock['volume']
        self.stock_volume = self.stock['volume'].abs().sum()
        self.stock_value = self.stock['deal_value'].abs().sum()
        self.stock_buy_volume = self.stock['volume'][self.stock['operation']=='开仓'].sum()
        self.stock_sell_volume = self.stock['volume'][self.stock['operation']=='平仓'].sum()
        self.stock_buy_value = self.stock['deal_value'][self.stock['operation']=='开仓'].abs().sum()
        self.stock_sell_value = self.stock['deal_value'][self.stock['operation']=='平仓'].abs().sum()
        self.stock_commission = self.stock_value * self.stock_commission_rate
        self.stock_tax = self.stock_sell_value * self.stock_tax_rate
        self.stock = self.adapt_cols(self.stock)
        # self.stock = self.stock[['ticker', 'operation', 'longshort', 'price', 'volume']]


class FutureDeal(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, data=None, future_commission_rate=0.000025, **kwargs):
        super().__init__(source_type, source_path, date, data, **kwargs)
        self.future_commission_rate = future_commission_rate
        
    def adapt_cols(self, df):
        if self.fullcols == True:
            self._fullcols = ['date', 'time', 'ticker', 'operation', 'longshort', 'price', 'volume', 'order_ref', 'deal_ref']
            df = df[self._fullcols]
        else:
            df = df['ticker', 'operation', 'longshort', 'price', 'volume']
        return df
    
    def _standardize(self):
        self.future = standardize_margindeal(self.data, self.date)
        self.future = self.future[['代码', '开平方向', '多空状态', '成交均价', '成交数量']]
        self.future.columns = ['ticker', 'operation', 'longshort', 'price', 'volume']
        if self.future['ticker'].dtype == 'int':
            self.future['ticker'] = self.future['ticker'].apply(lambda x: str(x).zfill(6))
        is_future_mask = self.future['ticker'].apply(lambda x: re_find('^IC|^IF|^IH|^IM|^中证500$|^中证1000$|^沪深300$|^上证50$', x)).astype(bool)
        self.future = self.future[is_future_mask]
        self.data = None
        
    def summarize(self):
        self.future['multiplier'] = self.future['ticker'].apply(cffex_multiplier)
        self.future['deal_value'] = self.future['price'] * self.future['volume'] * self.future['multiplier']
        self.future_deal_value_long = \
            self.future['deal_value'][self.future['longshort'] == '多头'][self.future['operation'] == '平仓'].sum() - \
            self.future['deal_value'][self.future['longshort'] == '多头'][self.future['operation'] == '开仓'].sum()
        self.future_deal_value_short = \
            -(
            self.future['deal_value'][self.future['longshort'] == '空头'][self.future['operation'] == '平仓'].sum() - \
            self.future['deal_value'][self.future['longshort'] == '空头'][self.future['operation'] == '开仓'].sum()
            )
        self.future_commission = \
            (self.future_deal_value_long + self.future_deal_value_short) * self.future_commission_rate
            
        self.future_deal_volume_long = self.future['volume'][self.future['longshort'] == '多头'].sum()
        self.future_deal_volume_short = self.future['volume'][self.future['longshort'] == '空头'].sum()
        self.future = self.future[['ticker', 'operation', 'longshort', 'price', 'volume']]

# class for universal margin account info
class MarginAccount(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, data=None, **kwargs):
        super().__init__(source_type, source_path, date, data, **kwargs)
        
    def _standardize(self):
        self.data = standardize_marginaccount(self.data, self.date)
        self.data = self.data[['日期', '可用保证金', '可用资金']]
        self.data.columns = ['date', 'available_margin', 'available_fund']
    
    def update_data(self):
        self._get_data()
        self._standardize()

class StockAccount(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, data=None, **kwargs):
        super().__init__(source_type, source_path, date, data, **kwargs)
        
    def _standardize(self):
        self.data = standardize_stockaccount(self.data, self.date, client_type=self.client_type)
        self.data = self.data[['日期', '净资产', '股票多头市值', '可用资金', '今日盈亏']]
        self.data.columns = ['date', 'net_asset', 'stock_value', 'available_fund', 'pnl(client)']
        
    def update_data(self):
        self._get_data()
        self._standardize()
    
# class for future account info
class FutureAccount(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, data=None, **kwargs):
        super().__init__(source_type, source_path, date, data, **kwargs)
        
    def _standardize(self):
        self.data = standardize_futureaccount(self.data, self.date)
        self.data = self.data[['日期', '可用资金', '保证金', '动态权益']]
        self.data.columns = ['date', 'available', 'margin', 'dynamic_rights']
        
    def update_data(self):
        self._get_data()
        self._standardize()
        

    # def __sub__(self, other):
    #     if isinstance(other, Delta):
    #         if other.delta_type == 'target':
    #             filltarget = pd.DataFrame(columns=['ticker', 'inplan_buyshares', 'inplan_sellshares', 'outplan_buyshares', 'outplan_sellshares']).set_index('ticker')
    #             deal = self.data[['ticker', 'buy_shares', 'sell_shares']].set_index('ticker')
    #             target = other.data[['ticker', 'buy_shares', 'sell_shares']].set_index('ticker')
    #             # drop 0 buy shares or sell shares
    #             target_buy = target[target['buy_shares'] != 0][['buy_shares']]
    #             target_sell = target[target['sell_shares'] != 0][['sell_shares']]
    #             print(target_buy)
    #             print(target_sell)

    #             intarget_buy_mask = deal.index.isin(target_buy.index)
    #             intarget_sell_mask = deal.index.isin(target_sell.index)
    #             filltarget['inplan_buyshares'] = deal[intarget_buy_mask]['buy_shares']
    #             filltarget['inplan_sellshares'] = deal[intarget_sell_mask]['sell_shares']
                
    #             print(filltarget)
    #             filltarget['outplan_buyshares'] = deal[~intarget_buy_mask]['buy_shares']
    #             filltarget['outplan_sellshares'] = deal[~intarget_sell_mask]['sell_shares']
    #             filltarget = filltarget.fillna(0).sort_index().reset_index()
    #             print(filltarget)
                
    #             return Delta(data=filltarget, delta_type='filltarget')
    #     else:
    #         raise ValueError("Unsupported operand type for -")
                
                


class Position(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, **kwargs):
        # self.date = date
        super().__init__(source_type, source_path, date, **kwargs)
        
    def _get_data(self):
        super()._get_data(header=None, names=['ticker', 'target_volume'])
                
        # if self.source_type == 'local':
        #     self.data = read_file(self.source_path, header=None, names=['ticker', 'target_volume'])
        # else:
        #     sftp = self.sftp_methods[self.source_type]
        #     with tempfile.NamedTemporaryFile(dir='tmp', delete=True) as temp_file:
        #         sftp.get(self.source_path, temp_file.name)
        #         self.data = read_file(temp_file.name, header=None, names=['ticker', 'target_volume'])
            # sftp.get(self.source_path, self.tempfile)
            # self.data = read_file(self.tempfile, header=None, names=['ticker', 'target_volume'])

    def summarize(self):
        self.attach_close()
        self.data.rename(columns={'volume': 'target_volume'}, inplace=True)
        self.data['target_pre_value'] = self.data['target_volume'] * self.data['close']
        self.total_volume = self.data['target_volume'].sum()
        self.total_target_pre_value = self.data['target_pre_value'].sum()
        
    def attach_close(self):
        pre_date = Calendar.last_trading_day(self.date).strftime('%Y%m%d')
        if self.pre_close is not None:
            pre_close = self.pre_close
        else:
            pre_close = get_stock_close(pre_date)
        pre_close = pre_close[pd.to_datetime(pre_close['date']) == pre_date]
        self.data = self.data.merge(pre_close, how='left', left_on='ticker', right_on='ticker')
    
    def toTarget(self, other):
        if isinstance(other, Hold):
            return Target(self, other)


class Delta(Portfolio):
    def __init__(self, source_type=None, source_path=None, date=None, data=None, delta_type=None, origin=None, dest=None, **kwargs):
        super().__init__(source_type, source_path, date, **kwargs)
        self.data = data
        self.delta_type = delta_type
        
    # def summarize(self):
    #     if self.delta_type == 'target':
    #         self.total_volume = self.data['shares'].abs().sum()
            
            
class Target(Delta):
    def __init__(self, origin, dest, delta_type='target'):
        super().__init__(origin=origin, dest=dest, delta_type=delta_type)
        
        combined_data = pd.concat([origin.data.set_index('ticker'),
                                    dest.data.set_index('ticker')], 
                                    axis=1, join='outer')
        combined_data = combined_data.fillna(0)
        combined_data['volume'] = combined_data['target_volume'] - combined_data['volume']
        combined_data = combined_data[combined_data['volume'] != 0].reset_index()[['ticker', 'volume']]
        combined_data['buy_volume'] = combined_data['volume'][combined_data['volume'] > 0]
        combined_data['sell_volume'] = combined_data['volume'][combined_data['volume'] <= 0]
        combined_data = combined_data.fillna(0)
        for col in ['volume', 'buy_volume', 'sell_volume']:
            combined_data[col] = combined_data[col].astype(int)
        
        combined_data.sort_values('ticker', inplace=True, ignore_index=True)
        self.data = combined_data
        
    def summarize(self):
        self.total_volume = self.data['volume'].abs().sum()
        self.total_buy_volume = self.data['buy_volume'].sum()
        self.total_sell_volume = self.data['sell_volume'].abs().sum()
        # self.data = self.data[['ticker', 'volume', 'buy_volume', 'sell_volume']]
        
    def attach(self, other):
        # if not isinstance(other, Deal):
        #     raise ValueError("Unsupported operand type for -")
        if isinstance(other, Deal):
            deal = other.data            
            deal['volume'] = deal['BS_flag'].apply(lambda x: 1 if x=='买入' else -1) * deal['volume']  # +/-
            deal['buy_volume'] = deal['volume'][deal['volume'] > 0]
            deal['sell_volume'] = deal['volume'][deal['volume'] <= 0]
            deal = deal[['ticker', 'buy_volume', 'sell_volume']].groupby(['ticker']).sum()
            # print(deal)
                        
            target = self.data[['ticker', 'buy_volume', 'sell_volume']].set_index('ticker')
            self.filltarget = pd.DataFrame(
                                        #    columns=['inplan_buyshares', 'inplan_sellshares', 'outplan_buyshares', 'outplan_sellshares'],
                                           index=target.index.union(deal.index)
                                           )
            # drop 0 buy shares or sell shares
            target_buy = target[target['buy_volume'] != 0][['buy_volume']]
            target_sell = target[target['sell_volume'] != 0][['sell_volume']]
            # print(target_buy)
            # print(target_sell)

            intarget_buy_mask = deal.index.isin(target_buy.index)
            intarget_sell_mask = deal.index.isin(target_sell.index)
            # print(len(intarget_buy_mask))
            # print(len(intarget_sell_mask))
            
            self.filltarget['buy_volume'] = target_buy
            self.filltarget['sell_volume'] = target_sell

            self.filltarget['inplan_buyvolume'] = deal[intarget_buy_mask]['buy_volume']
            self.filltarget['inplan_sellvolume'] = deal[intarget_sell_mask]['sell_volume']
            
            # print(deal[intarget_buy_mask]['buy_shares'])
            # print(filltarget)
            self.filltarget['outplan_buyvolume'] = deal[~intarget_buy_mask]['buy_volume']
            self.filltarget['outplan_sellvolume'] = deal[~intarget_sell_mask]['sell_volume']
            # 超出target的买卖部分
            # self.filltarget['outplan_buyvolume'] += 
            # print((self.filltarget['inplan_buyvolume'] - self.filltarget['buy_volume']).apply(lambda x: min(x,0)))
            # self.filltarget['outplan_sellvolume'] += 
            # print((self.filltarget['inplan_sellvolume'] - self.filltarget['sell_volume']).apply(lambda x: max(x,0)))
            
            self.filltarget = self.filltarget.fillna(0).sort_index().reset_index()
            
            self.target_buyvolume = self.filltarget['buy_volume'].sum()
            self.target_sellvolume = self.filltarget['sell_volume'].sum()
            self.inplan_buyvolume = self.filltarget['inplan_buyvolume'].sum()
            self.inplan_sellvolume = self.filltarget['inplan_sellvolume'].sum()
            self.outplan_buyvolume = self.filltarget['outplan_buyvolume'].sum()
            self.outplan_sellvolume = self.filltarget['outplan_sellvolume'].sum()
            
            

    
        