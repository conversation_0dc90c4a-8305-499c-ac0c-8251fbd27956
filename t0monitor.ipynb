{"cells": [{"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import os\n", "import paramiko\n", "import leveldb\n", "import datetime\n", "import json\n", "import pandas as pd\n", "import os\n", "from data import data_reader\n", "from utils import trading_calendar\n", "import adata\n", "from pqdm.processes import pqdm\n", "\n", "def get_klines(syms):\n", "    results=pqdm([[_] for _ in syms],adata.stock.market.get_market_min,n_jobs=10,argument_type='args')\n", "    df=pd.concat(results)\n", "    df=df.rename(columns={'stock_code':'symbol','amount':'amt','volume':'qty','trade_time':'time'})\n", "    df['amt']=df['amt'].astype(float)\n", "    df['qty']=df['qty'].astype(float)\n", "    df['time']=pd.to_datetime(df['time'])\n", "    return df\n", "\n", "def download_directory(sftp, remote_dir, local_dir):\n", "    \"\"\"递归下载远程目录到本地\"\"\"\n", "    # 创建本地目录\n", "    if not os.path.exists(local_dir):\n", "        os.makedirs(local_dir)\n", "        os.chmod(local_dir, 0o757)\n", "    # 遍历远程目录\n", "    for item in sftp.listdir_attr(remote_dir):\n", "        remote_path = os.path.join(remote_dir, item.filename)\n", "        local_path = os.path.join(local_dir, item.filename)\n", "        if item.st_mode & 0o40000:  # 是目录\n", "            download_directory(sftp, remote_path, local_path)\n", "        else:\n", "            try:\n", "                sftp.get(remote_path, local_path)\n", "                os.chmod(local_path, 0o757)\n", "                print(f\"下载: {remote_path} -> {local_path}\")\n", "                # remote_size = sftp.stat(remote_path).st_size\n", "                # local_size = os.path.getsize(local_path)\n", "                # if local_size == remote_size:\n", "                #     print(f\"下载完成: {remote_path} -> {local_path}\")\n", "                #     return True\n", "                # else:\n", "                #     print(f\"下载不完整: 远程大小={remote_size}, 本地大小={local_size}\")\n", "            except Exception as e:\n", "                print(f\"下载失败 {remote_path}: {str(e)}\")\n", "\n", "def ftpDownloadRemote(work_date):\n", "    target_path = \"/list/10.99.9.109_sftp/daily_after/t0/\"\n", "    target_path2 = \"/list/10.99.9.109_sftp/daily_after/vwap/\"\n", "    dir_name = \"parentorder_{}.ldb\".format(work_date)\n", "    dir_name2 = \"order_{}.ldb\".format(work_date)\n", "    try:\n", "        ssh = paramiko.SSHClient()\n", "        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())\n", "        private_key = paramiko.RSAKey.from_private_key_file('/home/<USER>/py/stk_py/test/intraday/chaolz2go')\n", "        ssh.connect(hostname='************', port=16888, username='chaolz2go', pkey=private_key)\n", "        sftp = ssh.open_sftp()\n", "        download_directory(sftp, target_path+dir_name, \"./\"+dir_name)\n", "        download_directory(sftp, target_path+dir_name2, \"./\"+dir_name2)\n", "        # download_directory(sftp, target_path2+dir_name, \"./\"+dir_name)\n", "        # download_directory(sftp, target_path2+dir_name2, \"./\"+dir_name2)\n", "        sftp.close()\n", "        ssh.close()\n", "    except Exception as e:\n", "        print(f\"连接失败: {e}\")\n", "        return False\n", "    return True\n", "\n", "\n", "\n", "def read_lvldb_data(p):\n", "    ldb=leveldb.LevelDB(p)\n", "    l=[]\n", "    datas=list(ldb.RangeIter())\n", "    # true='true'\n", "    pos=[]\n", "    params=[]\n", "    for k,v in datas:\n", "        d=json.loads(v)\n", "        pos.append(d) \n", "    po_df=pd.DataFrame(pos)\n", "    ldb=None\n", "    return po_df \n", "\n", "\n", "def open_position(slc):\n", "    p={}\n", "    p['side']=slc['side']\n", "    p['id']=slc['id']\n", "    p['pid']=slc['pid']\n", "    p['price']=slc['filled_price']\n", "    p['qty']=slc['filled_quantity']\n", "    p['sym']=slc['symbol']\n", "    p['time']=slc['last_upd_time']\n", "    p['account_id']=slc['account_id']\n", "    return p\n", "\n", "def close_position(p,slc):\n", "    t={}\n", "    t['open_side']=p['side']\n", "    t['open_id']=p['id']\n", "    t['pid']=p['pid']\n", "    t['account_id']=p['account_id']\n", "    t['open_price']=p['price']\n", "    t['sym']=p['sym']\n", "    t['open_time']=p['time']\n", "    t['close_side']=slc['side']\n", "    t['close_id']=slc['id']\n", "    t['close_price']=slc['filled_price']\n", "    t['close_time']=slc['last_upd_time']\n", "    if p['qty']==slc['filled_quantity']:\n", "        t['qty']=p['qty']\n", "        return t,None,None\n", "    if p['qty']>slc['filled_quantity']:\n", "        t['qty']=slc['filled_quantity']\n", "        p['qty']=p['qty']-slc['filled_quantity']\n", "        return t,p,None\n", "    if p['qty']<slc['filled_quantity']:\n", "        t['qty']=p['qty']\n", "        slc['filled_quantity']=slc['filled_quantity']-p['qty']\n", "        return t,None,slc\n", "\n", "def get_trds(slcs):\n", "    slcs=slcs[slcs['filled_quantity']>0]\n", "    pos=[]\n", "    trds=[]\n", "    for slc in slcs.to_dict('records'):\n", "        if len(pos)==0:\n", "            pos.append(open_position(slc))\n", "        elif pos[0]['side']==slc['side']:\n", "            pos.append(open_position(slc))\n", "        else:\n", "            for i,p in enumerate(pos) :\n", "                t,p,slc=close_position(p,slc)\n", "                trds.append(t)\n", "                if p is not None:\n", "                    pos[i]=p\n", "                    break\n", "                else:\n", "                    pos[i]=None\n", "                if slc is not None:\n", "                    pos[i]=None\n", "                else:\n", "                    break\n", "            if slc is not None:\n", "                pos.append(open_position(slc))\n", "        pos=[_ for _ in pos if _ is not None]\n", "    return pos,trds\n", "\n", "def get_fee_with_position(pos,commission_dict):\n", "    commission=commission_dict.get(pos['account_id'],1.2)/10000\n", "    if pos['side']==1:\n", "        return (pos['price']*pos['qty'])*commission+(pos['midpx']*pos['qty'])*(0.0005+commission)\n", "    else:\n", "        return (pos['midpx']*pos['qty'])*commission+(pos['price']*pos['qty'])*(0.0005+commission)\n", "    \n", "def get_fee_with_trd(trd,commission_dict):\n", "    commission=commission_dict.get(trd['account_id'],1.2)/10000\n", "    if trd['open_side']==1:\n", "        return (trd['open_price']*trd['qty'])*commission + (trd['close_price']*trd['qty'])*(0.0005+commission)\n", "    else:\n", "        return (trd['open_price']*trd['qty'])*(0.0005+commission) + (trd['close_price']*trd['qty'])*commission\n", "\n", "\n", "def intraday_trading_stats(p,slcs):\n", "    if len(slcs)==0:\n", "        cancel_rate=0\n", "        error_rate=0\n", "    else:\n", "        cancel_rate=len(slcs[slcs['status']=='CANCELED'])/len(slcs)\n", "        error_rate=len(slcs[slcs['status']=='ERROR'])/len(slcs)\n", "    holding_amt=(p['price']*p['quantity']).sum()\n", "    trading_amt=(p['amt_b']+p['amt_s']+p['diff_amt'].abs()).sum()\n", "    profit_without_fee=p['profit'].sum()\n", "    profit_after_fee=p['profit'].sum()-p['fee'].sum()\n", "    holding_ret=profit_after_fee/holding_amt\n", "    trading_ret=profit_after_fee/trading_amt\n", "    position=p['diff_amt'].abs().sum()\n", "    turnover=((p['filledQuantity_b']+p['filledQuantity_s'])*p['price']).sum()/(p['price']*p['quantity']).sum()\n", "    d={'holding_amt':holding_amt,'trading_amt':trading_amt,'profit_without_fee':profit_without_fee,'profit_after_fee':profit_after_fee,'holding_ret':holding_ret,'trading_ret':trading_ret,'turnover':turnover,'cancel_rate':cancel_rate,'error_rate':error_rate,'position':position}\n", "    return d\n", "\n", "def stats(pords,slcs,pos,trds):\n", "    if len(slcs)==0:\n", "        cancel_rate=0\n", "        error_rate=0\n", "    else:\n", "        cancel_rate=len(slcs[slcs['status']==110])/len(slcs[slcs['status']!=120])\n", "        error_rate=len(slcs[slcs['status']==120])/len(slcs)\n", "    holding_amt=(pords['price']*pords['quantity']).sum()\n", "    trading_amt=slcs['amt'].sum()\n", "    profit_without_fee=trds['profit'].sum()+pos['profit'].sum()\n", "    profit_after_fee=(trds['profit'].sum()-trds['fee'].sum())+(pos['profit'].sum()-pos['fee'].sum())\n", "    position=(pos['price']*pos['qty']).sum()\n", "    holding_ret=profit_after_fee/holding_amt\n", "    trading_ret=profit_after_fee/trading_amt\n", "    turnover=trading_amt/holding_amt\n", "    win_ratio_before_fee=len(trds[(trds['profit'])>0])/len(trds)\n", "    win_ratio_after_fee= len(trds[(trds['profit']-trds['fee'])>0])/len(trds)\n", "    d={'holding_amt':holding_amt,'trading_amt':trading_amt,'profit_without_fee':profit_without_fee,'profit_after_fee':profit_after_fee,'holding_ret':holding_ret,'trading_ret':trading_ret,'turnover':turnover,'cancel_rate':cancel_rate,'error_rate':error_rate,'position':position,'win_ratio_before_fee':win_ratio_before_fee,'win_ratio_after_fee':win_ratio_after_fee}\n", "    return d\n", "\n", "def pct_data_process(x):\n", "    return round(x*100,1)\n", "\n", "def display(l):\n", "    df=pd.DataFrame(l)\n", "    df['holding_ret']=df['holding_ret']*10000\n", "    df['trading_ret']=df['trading_ret']*10000\n", "    df['turnover']=df['turnover'].apply(pct_data_process)\n", "    df['error_rate']=df['error_rate'].apply(pct_data_process)\n", "    df['cancel_rate']=df['cancel_rate'].apply(pct_data_process)\n", "    df['win_ratio_before_fee']=df['win_ratio_before_fee'].apply(pct_data_process)\n", "    df['win_ratio_after_fee']=df['win_ratio_after_fee'].apply(pct_data_process)\n", "    df = df.applymap(lambda x: f\"{x:.2f}\" if isinstance(x, float) else x)\n", "    df=df.rename(columns={'holding_amt':'持仓金额(元)', 'trading_amt':'交易金额(元)','profit_without_fee':'费前总收益(元)','profit_after_fee':'费后总收益(元)','holding_ret':'底仓收益率(bps)','trading_ret':'开仓收益率(bps)','turnover':'换手%','cancel_rate':'撤单率%','error_rate':'废单率%','win_ratio_before_fee':'费前胜率%','win_ratio_after_fee':'费后胜率%','position':'未平仓位(元)','group_name':'分组'})\n", "    return df\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["下载: /list/10.99.9.109_sftp/daily_after/t0/parentorder_20250624.ldb/000001.log -> ./parentorder_20250624.ldb/000001.log\n", "下载: /list/10.99.9.109_sftp/daily_after/t0/parentorder_20250624.ldb/CURRENT -> ./parentorder_20250624.ldb/CURRENT\n", "下载: /list/10.99.9.109_sftp/daily_after/t0/parentorder_20250624.ldb/LOCK -> ./parentorder_20250624.ldb/LOCK\n", "下载: /list/10.99.9.109_sftp/daily_after/t0/parentorder_20250624.ldb/LOG -> ./parentorder_20250624.ldb/LOG\n", "下载: /list/10.99.9.109_sftp/daily_after/t0/parentorder_20250624.ldb/MANIFEST-000000 -> ./parentorder_20250624.ldb/MANIFEST-000000\n", "下载: /list/10.99.9.109_sftp/daily_after/t0/order_20250624.ldb/000001.log -> ./order_20250624.ldb/000001.log\n", "下载: /list/10.99.9.109_sftp/daily_after/t0/order_20250624.ldb/CURRENT -> ./order_20250624.ldb/CURRENT\n", "下载: /list/10.99.9.109_sftp/daily_after/t0/order_20250624.ldb/LOCK -> ./order_20250624.ldb/LOCK\n", "下载: /list/10.99.9.109_sftp/daily_after/t0/order_20250624.ldb/LOG -> ./order_20250624.ldb/LOG\n", "下载: /list/10.99.9.109_sftp/daily_after/t0/order_20250624.ldb/MANIFEST-000000 -> ./order_20250624.ldb/MANIFEST-000000\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>持仓金额(元)</th>\n", "      <th>交易金额(元)</th>\n", "      <th>费前总收益(元)</th>\n", "      <th>费后总收益(元)</th>\n", "      <th>底仓收益率(bps)</th>\n", "      <th>开仓收益率(bps)</th>\n", "      <th>换手%</th>\n", "      <th>撤单率%</th>\n", "      <th>废单率%</th>\n", "      <th>未平仓位(元)</th>\n", "      <th>费前胜率%</th>\n", "      <th>费后胜率%</th>\n", "      <th>分组</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11840564.00</td>\n", "      <td>6317967.50</td>\n", "      <td>2283.50</td>\n", "      <td>-224.22</td>\n", "      <td>-0.19</td>\n", "      <td>-0.35</td>\n", "      <td>53.40</td>\n", "      <td>29.90</td>\n", "      <td>18.50</td>\n", "      <td>457360.99</td>\n", "      <td>49.70</td>\n", "      <td>45.40</td>\n", "      <td>total</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>11840564.00</td>\n", "      <td>6317967.50</td>\n", "      <td>2283.50</td>\n", "      <td>-224.22</td>\n", "      <td>-0.19</td>\n", "      <td>-0.35</td>\n", "      <td>53.40</td>\n", "      <td>29.90</td>\n", "      <td>18.50</td>\n", "      <td>457360.99</td>\n", "      <td>49.70</td>\n", "      <td>45.40</td>\n", "      <td>************</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1400370.00</td>\n", "      <td>2392268.50</td>\n", "      <td>-1823.50</td>\n", "      <td>-2711.18</td>\n", "      <td>-19.36</td>\n", "      <td>-11.33</td>\n", "      <td>170.80</td>\n", "      <td>23.10</td>\n", "      <td>8.80</td>\n", "      <td>63517.99</td>\n", "      <td>44.40</td>\n", "      <td>38.90</td>\n", "      <td>short</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2416817.00</td>\n", "      <td>3747357.00</td>\n", "      <td>4340.00</td>\n", "      <td>2989.47</td>\n", "      <td>12.37</td>\n", "      <td>7.98</td>\n", "      <td>155.10</td>\n", "      <td>24.20</td>\n", "      <td>20.00</td>\n", "      <td>29441.00</td>\n", "      <td>53.20</td>\n", "      <td>49.50</td>\n", "      <td>long</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       持仓金额(元)     交易金额(元)  费前总收益(元)  费后总收益(元) 底仓收益率(bps) 开仓收益率(bps)     换手%  \\\n", "0  11840564.00  6317967.50   2283.50   -224.22      -0.19      -0.35   53.40   \n", "1  11840564.00  6317967.50   2283.50   -224.22      -0.19      -0.35   53.40   \n", "2   1400370.00  2392268.50  -1823.50  -2711.18     -19.36     -11.33  170.80   \n", "3   2416817.00  3747357.00   4340.00   2989.47      12.37       7.98  155.10   \n", "\n", "    撤单率%   废单率%    未平仓位(元)  费前胜率%  费后胜率%            分组  \n", "0  29.90  18.50  457360.99  49.70  45.40         total  \n", "1  29.90  18.50  457360.99  49.70  45.40  ************  \n", "2  23.10   8.80   63517.99  44.40  38.90         short  \n", "3  24.20  20.00   29441.00  53.20  49.50          long  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["date=\"20250624\"\n", "prev_date=trading_calendar.get_prev_trade_date(date)\n", "ftpDownloadRemote(date)\n", "commission_dict={'************':1.2}\n", "pords=read_lvldb_data(r\"/home/<USER>/py/stk_py/test/intraday/parentorder_{}.ldb\".format(date))\n", "slcs=read_lvldb_data(r\"/home/<USER>/py/stk_py/test/intraday/order_{}.ldb\".format(date))\n", "pxs=data_reader.get_close_pxs_by_date_from_file(prev_date)\n", "slcs['side']=slcs['operation'].apply(lambda x:-1 if x==110 else 1)\n", "slcs['amt']=slcs['filled_price']*slcs['filled_quantity']\n", "pords['price']=pords['symbol'].apply(lambda x:pxs.get(x,0))\n", "slcs=slcs.sort_values('create_time')\n", "poslist=[]\n", "trdlist=[]\n", "for pid,g in slcs.groupby('pid'):\n", "    pos,trds=get_trds(g)\n", "    if len(pos)>0:\n", "        poslist=poslist+pos\n", "    if len(trds)>0:\n", "        trdlist=trdlist+trds\n", "pos=pd.Data<PERSON>rame(poslist)\n", "trds=pd.<PERSON><PERSON><PERSON>e(trdlist)\n", "mktpx={_['stock_code']:float(_['price']) for _ in  adata.stock.market.list_market_current(code_list=pos['sym'].unique()).to_dict('records')}\n", "pos['midpx']=pos['sym'].apply(lambda x:mktpx.get(x,0))\n", "pos['profit']=(pos['price']-pos['midpx'])*pos['qty']*-pos['side']\n", "pos['fee']=pos.apply(lambda x:get_fee_with_position(x,commission_dict),axis=1)\n", "trds['fee']=trds.apply(lambda x:get_fee_with_trd(x,commission_dict),axis=1)\n", "trds['profit']=(trds['open_price']-trds['close_price'])*trds['qty']*-trds['open_side']\n", "l=[]\n", "ret=stats(pords,slcs,pos,trds)\n", "ret['group_name']='total'\n", "l.append(ret)\n", "for name,g in pords.groupby('account_id'):\n", "    ret=stats(g,slcs[slcs['account_id']==name],pos[pos['account_id']==name],trds[trds['account_id']==name])\n", "    ret['group_name']=name\n", "    l.append(ret)\n", "for name,g in trds.groupby('open_side'):\n", "    ret=stats(pords[pords['id'].isin(g['pid'])],slcs[slcs['pid'].isin(g['pid'])],pos[pos['pid'].isin(g['pid'])],g)\n", "    ret['group_name']='long' if name==1 else 'short'\n", "    l.append(ret)\n", "display(l)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'301365': 16.42,\n", " '688025': 66.28,\n", " '601860': 3.04,\n", " '601003': 3.5,\n", " '603390': 14.09,\n", " '002807': 4.91,\n", " '600871': 1.96,\n", " '002349': 7.16,\n", " '600252': 2.53,\n", " '000698': 3.86,\n", " '603167': 10.56,\n", " '600025': 9.71,\n", " '000533': 6.84,\n", " '600583': 5.31,\n", " '001332': 35.49,\n", " '600663': 8.87,\n", " '001330': 5.11,\n", " '603328': 9.47,\n", " '002545': 8.62,\n", " '603628': 11.91,\n", " '600558': 5.05,\n", " '000959': 3.33}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["mktpx"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a02a1d3012594a4f9859a2106c2636bc", "version_major": 2, "version_minor": 0}, "text/plain": ["QUEUEING TASKS | :   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7b8f088688af4eaa82f174c1974e8695", "version_major": 2, "version_minor": 0}, "text/plain": ["PROCESSING TASKS | :   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dba90d780cae46d5bce8275785fa2c35", "version_major": 2, "version_minor": 0}, "text/plain": ["COLLECTING RESULTS | :   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>time</th>\n", "      <th>price</th>\n", "      <th>change</th>\n", "      <th>change_pct</th>\n", "      <th>qty</th>\n", "      <th>avg_price</th>\n", "      <th>amt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:15:00</td>\n", "      <td>46.16</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:16:00</td>\n", "      <td>46.99</td>\n", "      <td>0.83</td>\n", "      <td>1.80</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:17:00</td>\n", "      <td>46.99</td>\n", "      <td>0.83</td>\n", "      <td>1.80</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:18:00</td>\n", "      <td>46.99</td>\n", "      <td>0.83</td>\n", "      <td>1.80</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:19:00</td>\n", "      <td>46.99</td>\n", "      <td>0.83</td>\n", "      <td>1.80</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:20:00</td>\n", "      <td>46.99</td>\n", "      <td>0.83</td>\n", "      <td>1.80</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:21:00</td>\n", "      <td>46.99</td>\n", "      <td>0.83</td>\n", "      <td>1.80</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:22:00</td>\n", "      <td>46.50</td>\n", "      <td>0.34</td>\n", "      <td>0.74</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:23:00</td>\n", "      <td>46.18</td>\n", "      <td>0.02</td>\n", "      <td>0.04</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:24:00</td>\n", "      <td>46.21</td>\n", "      <td>0.05</td>\n", "      <td>0.11</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:25:00</td>\n", "      <td>46.10</td>\n", "      <td>-0.06</td>\n", "      <td>-0.13</td>\n", "      <td>0.0</td>\n", "      <td>46.160</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:26:00</td>\n", "      <td>46.00</td>\n", "      <td>-0.16</td>\n", "      <td>-0.35</td>\n", "      <td>26200.0</td>\n", "      <td>46.000</td>\n", "      <td>1205200.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:27:00</td>\n", "      <td>46.00</td>\n", "      <td>-0.16</td>\n", "      <td>-0.35</td>\n", "      <td>0.0</td>\n", "      <td>46.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:28:00</td>\n", "      <td>46.00</td>\n", "      <td>-0.16</td>\n", "      <td>-0.35</td>\n", "      <td>0.0</td>\n", "      <td>46.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:29:00</td>\n", "      <td>46.00</td>\n", "      <td>-0.16</td>\n", "      <td>-0.35</td>\n", "      <td>0.0</td>\n", "      <td>46.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:30:00</td>\n", "      <td>46.00</td>\n", "      <td>-0.16</td>\n", "      <td>-0.35</td>\n", "      <td>0.0</td>\n", "      <td>46.000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:31:00</td>\n", "      <td>44.60</td>\n", "      <td>-1.56</td>\n", "      <td>-3.38</td>\n", "      <td>106900.0</td>\n", "      <td>45.207</td>\n", "      <td>4811868.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:32:00</td>\n", "      <td>44.86</td>\n", "      <td>-1.30</td>\n", "      <td>-2.82</td>\n", "      <td>72000.0</td>\n", "      <td>45.094</td>\n", "      <td>3231772.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:33:00</td>\n", "      <td>45.12</td>\n", "      <td>-1.04</td>\n", "      <td>-2.25</td>\n", "      <td>96900.0</td>\n", "      <td>45.027</td>\n", "      <td>4349235.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:34:00</td>\n", "      <td>44.50</td>\n", "      <td>-1.66</td>\n", "      <td>-3.60</td>\n", "      <td>87300.0</td>\n", "      <td>44.956</td>\n", "      <td>3903135.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:35:00</td>\n", "      <td>44.45</td>\n", "      <td>-1.71</td>\n", "      <td>-3.70</td>\n", "      <td>116300.0</td>\n", "      <td>44.879</td>\n", "      <td>5189816.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:36:00</td>\n", "      <td>44.72</td>\n", "      <td>-1.44</td>\n", "      <td>-3.12</td>\n", "      <td>39800.0</td>\n", "      <td>44.855</td>\n", "      <td>1772827.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:37:00</td>\n", "      <td>44.77</td>\n", "      <td>-1.39</td>\n", "      <td>-3.01</td>\n", "      <td>53400.0</td>\n", "      <td>44.839</td>\n", "      <td>2385556.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>603373</td>\n", "      <td>2025-06-24 09:38:00</td>\n", "      <td>45.06</td>\n", "      <td>-1.10</td>\n", "      <td>-2.38</td>\n", "      <td>22300.0</td>\n", "      <td>44.842</td>\n", "      <td>1001957.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    symbol                time  price  change  change_pct       qty  \\\n", "0   603373 2025-06-24 09:15:00  46.16    0.00        0.00       0.0   \n", "1   603373 2025-06-24 09:16:00  46.99    0.83        1.80       0.0   \n", "2   603373 2025-06-24 09:17:00  46.99    0.83        1.80       0.0   \n", "3   603373 2025-06-24 09:18:00  46.99    0.83        1.80       0.0   \n", "4   603373 2025-06-24 09:19:00  46.99    0.83        1.80       0.0   \n", "5   603373 2025-06-24 09:20:00  46.99    0.83        1.80       0.0   \n", "6   603373 2025-06-24 09:21:00  46.99    0.83        1.80       0.0   \n", "7   603373 2025-06-24 09:22:00  46.50    0.34        0.74       0.0   \n", "8   603373 2025-06-24 09:23:00  46.18    0.02        0.04       0.0   \n", "9   603373 2025-06-24 09:24:00  46.21    0.05        0.11       0.0   \n", "10  603373 2025-06-24 09:25:00  46.10   -0.06       -0.13       0.0   \n", "11  603373 2025-06-24 09:26:00  46.00   -0.16       -0.35   26200.0   \n", "12  603373 2025-06-24 09:27:00  46.00   -0.16       -0.35       0.0   \n", "13  603373 2025-06-24 09:28:00  46.00   -0.16       -0.35       0.0   \n", "14  603373 2025-06-24 09:29:00  46.00   -0.16       -0.35       0.0   \n", "15  603373 2025-06-24 09:30:00  46.00   -0.16       -0.35       0.0   \n", "16  603373 2025-06-24 09:31:00  44.60   -1.56       -3.38  106900.0   \n", "17  603373 2025-06-24 09:32:00  44.86   -1.30       -2.82   72000.0   \n", "18  603373 2025-06-24 09:33:00  45.12   -1.04       -2.25   96900.0   \n", "19  603373 2025-06-24 09:34:00  44.50   -1.66       -3.60   87300.0   \n", "20  603373 2025-06-24 09:35:00  44.45   -1.71       -3.70  116300.0   \n", "21  603373 2025-06-24 09:36:00  44.72   -1.44       -3.12   39800.0   \n", "22  603373 2025-06-24 09:37:00  44.77   -1.39       -3.01   53400.0   \n", "23  603373 2025-06-24 09:38:00  45.06   -1.10       -2.38   22300.0   \n", "\n", "    avg_price        amt  \n", "0      46.160        0.0  \n", "1      46.160        0.0  \n", "2      46.160        0.0  \n", "3      46.160        0.0  \n", "4      46.160        0.0  \n", "5      46.160        0.0  \n", "6      46.160        0.0  \n", "7      46.160        0.0  \n", "8      46.160        0.0  \n", "9      46.160        0.0  \n", "10     46.160        0.0  \n", "11     46.000  1205200.0  \n", "12     46.000        0.0  \n", "13     46.000        0.0  \n", "14     46.000        0.0  \n", "15     46.000        0.0  \n", "16     45.207  4811868.0  \n", "17     45.094  3231772.0  \n", "18     45.027  4349235.0  \n", "19     44.956  3903135.0  \n", "20     44.879  5189816.0  \n", "21     44.855  1772827.0  \n", "22     44.839  2385556.0  \n", "23     44.842  1001957.0  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["get_klines(['603373'])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>side</th>\n", "      <th>id</th>\n", "      <th>pid</th>\n", "      <th>price</th>\n", "      <th>qty</th>\n", "      <th>sym</th>\n", "      <th>time</th>\n", "      <th>account_id</th>\n", "      <th>midpx</th>\n", "      <th>profit</th>\n", "      <th>fee</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-1</td>\n", "      <td>62ab901f-67a8-4735-8922-60069146f5ea</td>\n", "      <td>e157e110-5099-11f0-b0a8-dfa15ff0334a</td>\n", "      <td>45.150000</td>\n", "      <td>500.0</td>\n", "      <td>603373</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>46.16</td>\n", "      <td>-505.00</td>\n", "      <td>16.766100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-1</td>\n", "      <td>61e36c67-2966-4848-ad2a-a99d53d93f12</td>\n", "      <td>e157e110-5099-11f0-b0a8-dfa15ff0334a</td>\n", "      <td>44.810000</td>\n", "      <td>300.0</td>\n", "      <td>603373</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>46.16</td>\n", "      <td>-405.00</td>\n", "      <td>9.996420</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-1</td>\n", "      <td>2b991fbf-107f-4e4a-9312-ec536362aa33</td>\n", "      <td>e157e110-5099-11f0-b0a8-dfa15ff0334a</td>\n", "      <td>44.800000</td>\n", "      <td>100.0</td>\n", "      <td>603373</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>46.16</td>\n", "      <td>-136.00</td>\n", "      <td>3.331520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-1</td>\n", "      <td>06d8f793-d951-49c2-8de9-3c820bf64437</td>\n", "      <td>e157e110-5099-11f0-b0a8-dfa15ff0334a</td>\n", "      <td>44.450000</td>\n", "      <td>200.0</td>\n", "      <td>603373</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>46.16</td>\n", "      <td>-342.00</td>\n", "      <td>6.619640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-1</td>\n", "      <td>62a2943e-9c3b-4e29-b841-715800da73a0</td>\n", "      <td>e15800bb-5099-11f0-962f-dfa15ff0334a</td>\n", "      <td>16.270000</td>\n", "      <td>1200.0</td>\n", "      <td>301365</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>16.18</td>\n", "      <td>108.00</td>\n", "      <td>14.434800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>-1</td>\n", "      <td>894c0fa3-77e3-40f5-889a-41c1523b06b7</td>\n", "      <td>e1582fa5-5099-11f0-8abb-dfa15ff0334a</td>\n", "      <td>66.674950</td>\n", "      <td>400.0</td>\n", "      <td>688025</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>66.66</td>\n", "      <td>5.98</td>\n", "      <td>19.735068</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1</td>\n", "      <td>ab7a6824-0d94-48d3-9983-778745aa9621</td>\n", "      <td>e1583377-5099-11f0-a5d8-dfa15ff0334a</td>\n", "      <td>28.760000</td>\n", "      <td>100.0</td>\n", "      <td>603655</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>28.16</td>\n", "      <td>-60.00</td>\n", "      <td>2.091040</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1</td>\n", "      <td>299d1259-b80a-4518-8dc2-ee13f829dbe6</td>\n", "      <td>e158374c-5099-11f0-b16e-dfa15ff0334a</td>\n", "      <td>11.382000</td>\n", "      <td>500.0</td>\n", "      <td>603089</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>11.27</td>\n", "      <td>-56.00</td>\n", "      <td>4.176620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>-1</td>\n", "      <td>b1370cf1-2947-4855-a183-fcb2628f92f4</td>\n", "      <td>e1587200-5099-11f0-8e9c-dfa15ff0334a</td>\n", "      <td>25.430000</td>\n", "      <td>100.0</td>\n", "      <td>001266</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>25.30</td>\n", "      <td>13.00</td>\n", "      <td>1.880260</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>1</td>\n", "      <td>d32d276e-5dda-44ff-ab14-41ffc641a173</td>\n", "      <td>e15892c1-5099-11f0-8132-dfa15ff0334a</td>\n", "      <td>3.080000</td>\n", "      <td>5300.0</td>\n", "      <td>601860</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>3.07</td>\n", "      <td>-53.00</td>\n", "      <td>12.046900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>1</td>\n", "      <td>07689000-b925-41d7-8294-91fb4d0060df</td>\n", "      <td>e158a852-5099-11f0-96b8-dfa15ff0334a</td>\n", "      <td>25.080000</td>\n", "      <td>100.0</td>\n", "      <td>300940</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>24.27</td>\n", "      <td>-81.00</td>\n", "      <td>1.805700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>-1</td>\n", "      <td>5f9e80d5-7237-4840-a844-1ba048978ffb</td>\n", "      <td>e158aa3d-5099-11f0-aa40-dfa15ff0334a</td>\n", "      <td>3.540000</td>\n", "      <td>10300.0</td>\n", "      <td>601003</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>3.57</td>\n", "      <td>-309.00</td>\n", "      <td>27.018960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>-1</td>\n", "      <td>268a2b6f-ce9b-450a-a397-58ea304fa1d7</td>\n", "      <td>e158bfad-5099-11f0-904d-dfa15ff0334a</td>\n", "      <td>14.090000</td>\n", "      <td>2000.0</td>\n", "      <td>603390</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>12.81</td>\n", "      <td>2560.00</td>\n", "      <td>20.546000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>1</td>\n", "      <td>4d5a03ee-d044-4d4c-a08f-0a9e3db9b849</td>\n", "      <td>e158d0ae-5099-11f0-9560-dfa15ff0334a</td>\n", "      <td>21.380000</td>\n", "      <td>2000.0</td>\n", "      <td>300331</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>21.33</td>\n", "      <td>-100.00</td>\n", "      <td>31.580400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>-1</td>\n", "      <td>f44ac6b6-a5b7-4de6-b790-62a61ff1b6c3</td>\n", "      <td>e158dbdd-5099-11f0-b06f-dfa15ff0334a</td>\n", "      <td>4.970000</td>\n", "      <td>4500.0</td>\n", "      <td>002807</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>4.96</td>\n", "      <td>45.00</td>\n", "      <td>16.544700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>1</td>\n", "      <td>0ae71857-85ed-4ced-aa17-eadb24dfc15d</td>\n", "      <td>e158df75-5099-11f0-b382-dfa15ff0334a</td>\n", "      <td>19.910000</td>\n", "      <td>200.0</td>\n", "      <td>300815</td>\n", "      <td>1750728610559</td>\n", "      <td>************</td>\n", "      <td>19.26</td>\n", "      <td>-130.00</td>\n", "      <td>2.866080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>1</td>\n", "      <td>625c3267-7719-4763-ae61-d98636faf6a0</td>\n", "      <td>e158df75-5099-11f0-b382-dfa15ff0334a</td>\n", "      <td>19.970000</td>\n", "      <td>200.0</td>\n", "      <td>300815</td>\n", "      <td>1750728618901</td>\n", "      <td>************</td>\n", "      <td>19.26</td>\n", "      <td>-142.00</td>\n", "      <td>2.867520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>1</td>\n", "      <td>66384c3c-26d5-42eb-9195-f02b7a356cef</td>\n", "      <td>e158df75-5099-11f0-b382-dfa15ff0334a</td>\n", "      <td>20.000000</td>\n", "      <td>1400.0</td>\n", "      <td>300815</td>\n", "      <td>1750728626355</td>\n", "      <td>************</td>\n", "      <td>19.26</td>\n", "      <td>-1036.00</td>\n", "      <td>20.077680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>-1</td>\n", "      <td>3e21bd6f-c5cc-4070-88d8-2ca520dca6da</td>\n", "      <td>e158f5ef-5099-11f0-a88d-dfa15ff0334a</td>\n", "      <td>1.950000</td>\n", "      <td>6100.0</td>\n", "      <td>600871</td>\n", "      <td>1750728620864</td>\n", "      <td>************</td>\n", "      <td>2.07</td>\n", "      <td>-732.00</td>\n", "      <td>8.890140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>-1</td>\n", "      <td>d8c15f82-93ec-4742-9080-edef77478673</td>\n", "      <td>e158f7c1-5099-11f0-8ead-dfa15ff0334a</td>\n", "      <td>7.140000</td>\n", "      <td>200.0</td>\n", "      <td>002349</td>\n", "      <td>1750728768025</td>\n", "      <td>************</td>\n", "      <td>7.12</td>\n", "      <td>4.00</td>\n", "      <td>1.056240</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>1</td>\n", "      <td>5c84c315-2184-4571-838d-35f392ee1466</td>\n", "      <td>e1591cff-5099-11f0-8f76-dfa15ff0334a</td>\n", "      <td>2.540000</td>\n", "      <td>8800.0</td>\n", "      <td>600252</td>\n", "      <td>1750728621946</td>\n", "      <td>************</td>\n", "      <td>2.51</td>\n", "      <td>-264.00</td>\n", "      <td>16.376800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>1</td>\n", "      <td>c4f1968a-f6c9-41ed-89ef-80bcd2959d12</td>\n", "      <td>e1591ed6-5099-11f0-a1ce-dfa15ff0334a</td>\n", "      <td>3.930000</td>\n", "      <td>5300.0</td>\n", "      <td>000698</td>\n", "      <td>1750728601760</td>\n", "      <td>************</td>\n", "      <td>4.04</td>\n", "      <td>583.00</td>\n", "      <td>15.774920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>-1</td>\n", "      <td>2138350b-b92a-4e73-9279-413749e2006b</td>\n", "      <td>e15922a5-5099-11f0-aac3-dfa15ff0334a</td>\n", "      <td>10.480000</td>\n", "      <td>2000.0</td>\n", "      <td>603167</td>\n", "      <td>1750728621568</td>\n", "      <td>************</td>\n", "      <td>10.71</td>\n", "      <td>-460.00</td>\n", "      <td>15.565600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>1</td>\n", "      <td>81de46c3-1050-4923-9855-2d817382381f</td>\n", "      <td>e1593507-5099-11f0-989c-dfa15ff0334a</td>\n", "      <td>9.800000</td>\n", "      <td>3200.0</td>\n", "      <td>600025</td>\n", "      <td>1750728621836</td>\n", "      <td>************</td>\n", "      <td>9.76</td>\n", "      <td>-128.00</td>\n", "      <td>23.127040</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>-1</td>\n", "      <td>643239b7-584e-4577-8d1e-952be578cfc3</td>\n", "      <td>e1593cb5-5099-11f0-aad6-dfa15ff0334a</td>\n", "      <td>21.530000</td>\n", "      <td>400.0</td>\n", "      <td>688510</td>\n", "      <td>1750728646784</td>\n", "      <td>************</td>\n", "      <td>22.12</td>\n", "      <td>-236.00</td>\n", "      <td>6.401200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>-1</td>\n", "      <td>b4962de1-4f83-4b19-a4ad-e5709f9eb67a</td>\n", "      <td>e159510f-5099-11f0-866f-dfa15ff0334a</td>\n", "      <td>5.280000</td>\n", "      <td>3800.0</td>\n", "      <td>600583</td>\n", "      <td>1750728610335</td>\n", "      <td>************</td>\n", "      <td>5.53</td>\n", "      <td>-950.00</td>\n", "      <td>14.961360</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>1</td>\n", "      <td>d0956429-290b-4534-8111-d8e5a50e011c</td>\n", "      <td>e159585a-5099-11f0-bc0d-dfa15ff0334a</td>\n", "      <td>2.100000</td>\n", "      <td>13700.0</td>\n", "      <td>000981</td>\n", "      <td>1750728602234</td>\n", "      <td>************</td>\n", "      <td>2.07</td>\n", "      <td>-411.00</td>\n", "      <td>21.034980</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>1</td>\n", "      <td>9c299b03-63b3-4f94-a139-789fb44480be</td>\n", "      <td>e1599385-5099-11f0-bace-dfa15ff0334a</td>\n", "      <td>12.888182</td>\n", "      <td>1100.0</td>\n", "      <td>300981</td>\n", "      <td>1750728643898</td>\n", "      <td>************</td>\n", "      <td>12.85</td>\n", "      <td>-42.00</td>\n", "      <td>10.464940</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>-1</td>\n", "      <td>50b221cf-eff8-4b87-a7a1-589d033074bc</td>\n", "      <td>e159a0e0-5099-11f0-8c46-dfa15ff0334a</td>\n", "      <td>13.740000</td>\n", "      <td>1200.0</td>\n", "      <td>301507</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>13.68</td>\n", "      <td>72.00</td>\n", "      <td>12.192480</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>1</td>\n", "      <td>0c275be4-7e53-4027-998a-721e209793af</td>\n", "      <td>e159f37b-5099-11f0-ba97-dfa15ff0334a</td>\n", "      <td>35.100000</td>\n", "      <td>500.0</td>\n", "      <td>001332</td>\n", "      <td>1750728601068</td>\n", "      <td>************</td>\n", "      <td>35.45</td>\n", "      <td>175.00</td>\n", "      <td>13.095500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>-1</td>\n", "      <td>cb1d02d0-c6f5-4e1c-a50b-cbd3361f9d15</td>\n", "      <td>e15a3a60-5099-11f0-be98-dfa15ff0334a</td>\n", "      <td>21.070000</td>\n", "      <td>800.0</td>\n", "      <td>001206</td>\n", "      <td>1750728603188</td>\n", "      <td>************</td>\n", "      <td>20.82</td>\n", "      <td>200.00</td>\n", "      <td>12.449440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>-1</td>\n", "      <td>2d7e9b54-77a6-440f-ab7c-2f29ae46395c</td>\n", "      <td>e15a3f69-5099-11f0-a88a-dfa15ff0334a</td>\n", "      <td>20.070000</td>\n", "      <td>700.0</td>\n", "      <td>301055</td>\n", "      <td>1750728648335</td>\n", "      <td>************</td>\n", "      <td>19.76</td>\n", "      <td>217.00</td>\n", "      <td>10.370220</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>1</td>\n", "      <td>4d9cf641-97c4-42a5-a31a-620cb45b8eb9</td>\n", "      <td>e15a6312-5099-11f0-864f-dfa15ff0334a</td>\n", "      <td>8.829545</td>\n", "      <td>2200.0</td>\n", "      <td>600663</td>\n", "      <td>1750728609002</td>\n", "      <td>************</td>\n", "      <td>8.80</td>\n", "      <td>-65.00</td>\n", "      <td>14.334200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>1</td>\n", "      <td>aef6cfb1-ebc2-4a57-b3b0-1283afa196b1</td>\n", "      <td>e15a64bc-5099-11f0-8566-dfa15ff0334a</td>\n", "      <td>4.990000</td>\n", "      <td>5900.0</td>\n", "      <td>001330</td>\n", "      <td>1750728682966</td>\n", "      <td>************</td>\n", "      <td>4.93</td>\n", "      <td>-354.00</td>\n", "      <td>21.566860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>1</td>\n", "      <td>bb87d7b7-ef16-427b-9414-41ae360c62df</td>\n", "      <td>e15a7656-5099-11f0-a350-dfa15ff0334a</td>\n", "      <td>9.319024</td>\n", "      <td>4100.0</td>\n", "      <td>603328</td>\n", "      <td>1750728602628</td>\n", "      <td>************</td>\n", "      <td>9.31</td>\n", "      <td>-37.00</td>\n", "      <td>28.250980</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>-1</td>\n", "      <td>4b6c504f-10d3-4781-99d4-307ae580a2b4</td>\n", "      <td>e15a807e-5099-11f0-b26f-dfa15ff0334a</td>\n", "      <td>8.560000</td>\n", "      <td>1400.0</td>\n", "      <td>002545</td>\n", "      <td>1750728619993</td>\n", "      <td>************</td>\n", "      <td>8.62</td>\n", "      <td>-84.00</td>\n", "      <td>8.878240</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>-1</td>\n", "      <td>05b709d9-cb9d-46a3-a751-49d17bdfe37b</td>\n", "      <td>e15a83ce-5099-11f0-8169-dfa15ff0334a</td>\n", "      <td>11.860000</td>\n", "      <td>500.0</td>\n", "      <td>603628</td>\n", "      <td>1750728634364</td>\n", "      <td>************</td>\n", "      <td>11.87</td>\n", "      <td>-5.00</td>\n", "      <td>4.388800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>1</td>\n", "      <td>e90cdfb4-ecbf-452f-95c3-4063b64ff761</td>\n", "      <td>e15aaa67-5099-11f0-ad1a-dfa15ff0334a</td>\n", "      <td>5.010000</td>\n", "      <td>1500.0</td>\n", "      <td>600558</td>\n", "      <td>1750728602139</td>\n", "      <td>************</td>\n", "      <td>5.01</td>\n", "      <td>-0.00</td>\n", "      <td>5.561100</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    side                                    id  \\\n", "0     -1  62ab901f-67a8-4735-8922-60069146f5ea   \n", "1     -1  61e36c67-2966-4848-ad2a-a99d53d93f12   \n", "2     -1  2b991fbf-107f-4e4a-9312-ec536362aa33   \n", "3     -1  06d8f793-d951-49c2-8de9-3c820bf64437   \n", "4     -1  62a2943e-9c3b-4e29-b841-715800da73a0   \n", "5     -1  894c0fa3-77e3-40f5-889a-41c1523b06b7   \n", "6      1  ab7a6824-0d94-48d3-9983-778745aa9621   \n", "7      1  299d1259-b80a-4518-8dc2-ee13f829dbe6   \n", "8     -1  b1370cf1-2947-4855-a183-fcb2628f92f4   \n", "9      1  d32d276e-5dda-44ff-ab14-41ffc641a173   \n", "10     1  07689000-b925-41d7-8294-91fb4d0060df   \n", "11    -1  5f9e80d5-7237-4840-a844-1ba048978ffb   \n", "12    -1  268a2b6f-ce9b-450a-a397-58ea304fa1d7   \n", "13     1  4d5a03ee-d044-4d4c-a08f-0a9e3db9b849   \n", "14    -1  f44ac6b6-a5b7-4de6-b790-62a61ff1b6c3   \n", "15     1  0ae71857-85ed-4ced-aa17-eadb24dfc15d   \n", "16     1  625c3267-7719-4763-ae61-d98636faf6a0   \n", "17     1  66384c3c-26d5-42eb-9195-f02b7a356cef   \n", "18    -1  3e21bd6f-c5cc-4070-88d8-2ca520dca6da   \n", "19    -1  d8c15f82-93ec-4742-9080-edef77478673   \n", "20     1  5c84c315-2184-4571-838d-35f392ee1466   \n", "21     1  c4f1968a-f6c9-41ed-89ef-80bcd2959d12   \n", "22    -1  2138350b-b92a-4e73-9279-413749e2006b   \n", "23     1  81de46c3-1050-4923-9855-2d817382381f   \n", "24    -1  643239b7-584e-4577-8d1e-952be578cfc3   \n", "25    -1  b4962de1-4f83-4b19-a4ad-e5709f9eb67a   \n", "26     1  d0956429-290b-4534-8111-d8e5a50e011c   \n", "27     1  9c299b03-63b3-4f94-a139-789fb44480be   \n", "28    -1  50b221cf-eff8-4b87-a7a1-589d033074bc   \n", "29     1  0c275be4-7e53-4027-998a-721e209793af   \n", "30    -1  cb1d02d0-c6f5-4e1c-a50b-cbd3361f9d15   \n", "31    -1  2d7e9b54-77a6-440f-ab7c-2f29ae46395c   \n", "32     1  4d9cf641-97c4-42a5-a31a-620cb45b8eb9   \n", "33     1  aef6cfb1-ebc2-4a57-b3b0-1283afa196b1   \n", "34     1  bb87d7b7-ef16-427b-9414-41ae360c62df   \n", "35    -1  4b6c504f-10d3-4781-99d4-307ae580a2b4   \n", "36    -1  05b709d9-cb9d-46a3-a751-49d17bdfe37b   \n", "37     1  e90cdfb4-ecbf-452f-95c3-4063b64ff761   \n", "\n", "                                     pid      price      qty     sym  \\\n", "0   e157e110-5099-11f0-b0a8-dfa15ff0334a  45.150000    500.0  603373   \n", "1   e157e110-5099-11f0-b0a8-dfa15ff0334a  44.810000    300.0  603373   \n", "2   e157e110-5099-11f0-b0a8-dfa15ff0334a  44.800000    100.0  603373   \n", "3   e157e110-5099-11f0-b0a8-dfa15ff0334a  44.450000    200.0  603373   \n", "4   e15800bb-5099-11f0-962f-dfa15ff0334a  16.270000   1200.0  301365   \n", "5   e1582fa5-5099-11f0-8abb-dfa15ff0334a  66.674950    400.0  688025   \n", "6   e1583377-5099-11f0-a5d8-dfa15ff0334a  28.760000    100.0  603655   \n", "7   e158374c-5099-11f0-b16e-dfa15ff0334a  11.382000    500.0  603089   \n", "8   e1587200-5099-11f0-8e9c-dfa15ff0334a  25.430000    100.0  001266   \n", "9   e15892c1-5099-11f0-8132-dfa15ff0334a   3.080000   5300.0  601860   \n", "10  e158a852-5099-11f0-96b8-dfa15ff0334a  25.080000    100.0  300940   \n", "11  e158aa3d-5099-11f0-aa40-dfa15ff0334a   3.540000  10300.0  601003   \n", "12  e158bfad-5099-11f0-904d-dfa15ff0334a  14.090000   2000.0  603390   \n", "13  e158d0ae-5099-11f0-9560-dfa15ff0334a  21.380000   2000.0  300331   \n", "14  e158dbdd-5099-11f0-b06f-dfa15ff0334a   4.970000   4500.0  002807   \n", "15  e158df75-5099-11f0-b382-dfa15ff0334a  19.910000    200.0  300815   \n", "16  e158df75-5099-11f0-b382-dfa15ff0334a  19.970000    200.0  300815   \n", "17  e158df75-5099-11f0-b382-dfa15ff0334a  20.000000   1400.0  300815   \n", "18  e158f5ef-5099-11f0-a88d-dfa15ff0334a   1.950000   6100.0  600871   \n", "19  e158f7c1-5099-11f0-8ead-dfa15ff0334a   7.140000    200.0  002349   \n", "20  e1591cff-5099-11f0-8f76-dfa15ff0334a   2.540000   8800.0  600252   \n", "21  e1591ed6-5099-11f0-a1ce-dfa15ff0334a   3.930000   5300.0  000698   \n", "22  e15922a5-5099-11f0-aac3-dfa15ff0334a  10.480000   2000.0  603167   \n", "23  e1593507-5099-11f0-989c-dfa15ff0334a   9.800000   3200.0  600025   \n", "24  e1593cb5-5099-11f0-aad6-dfa15ff0334a  21.530000    400.0  688510   \n", "25  e159510f-5099-11f0-866f-dfa15ff0334a   5.280000   3800.0  600583   \n", "26  e159585a-5099-11f0-bc0d-dfa15ff0334a   2.100000  13700.0  000981   \n", "27  e1599385-5099-11f0-bace-dfa15ff0334a  12.888182   1100.0  300981   \n", "28  e159a0e0-5099-11f0-8c46-dfa15ff0334a  13.740000   1200.0  301507   \n", "29  e159f37b-5099-11f0-ba97-dfa15ff0334a  35.100000    500.0  001332   \n", "30  e15a3a60-5099-11f0-be98-dfa15ff0334a  21.070000    800.0  001206   \n", "31  e15a3f69-5099-11f0-a88a-dfa15ff0334a  20.070000    700.0  301055   \n", "32  e15a6312-5099-11f0-864f-dfa15ff0334a   8.829545   2200.0  600663   \n", "33  e15a64bc-5099-11f0-8566-dfa15ff0334a   4.990000   5900.0  001330   \n", "34  e15a7656-5099-11f0-a350-dfa15ff0334a   9.319024   4100.0  603328   \n", "35  e15a807e-5099-11f0-b26f-dfa15ff0334a   8.560000   1400.0  002545   \n", "36  e15a83ce-5099-11f0-8169-dfa15ff0334a  11.860000    500.0  603628   \n", "37  e15aaa67-5099-11f0-ad1a-dfa15ff0334a   5.010000   1500.0  600558   \n", "\n", "             time    account_id  midpx   profit        fee  \n", "0   *************  ************  46.16  -505.00  16.766100  \n", "1   *************  ************  46.16  -405.00   9.996420  \n", "2   *************  ************  46.16  -136.00   3.331520  \n", "3   *************  ************  46.16  -342.00   6.619640  \n", "4   *************  ************  16.18   108.00  14.434800  \n", "5   *************  ************  66.66     5.98  19.735068  \n", "6   *************  ************  28.16   -60.00   2.091040  \n", "7   *************  ************  11.27   -56.00   4.176620  \n", "8   *************  ************  25.30    13.00   1.880260  \n", "9   *************  ************   3.07   -53.00  12.046900  \n", "10  *************  ************  24.27   -81.00   1.805700  \n", "11  *************  ************   3.57  -309.00  27.018960  \n", "12  *************  ************  12.81  2560.00  20.546000  \n", "13  *************  ************  21.33  -100.00  31.580400  \n", "14  *************  ************   4.96    45.00  16.544700  \n", "15  1750728610559  ************  19.26  -130.00   2.866080  \n", "16  1750728618901  ************  19.26  -142.00   2.867520  \n", "17  1750728626355  ************  19.26 -1036.00  20.077680  \n", "18  1750728620864  ************   2.07  -732.00   8.890140  \n", "19  1750728768025  ************   7.12     4.00   1.056240  \n", "20  1750728621946  ************   2.51  -264.00  16.376800  \n", "21  1750728601760  ************   4.04   583.00  15.774920  \n", "22  1750728621568  ************  10.71  -460.00  15.565600  \n", "23  1750728621836  ************   9.76  -128.00  23.127040  \n", "24  1750728646784  ************  22.12  -236.00   6.401200  \n", "25  1750728610335  ************   5.53  -950.00  14.961360  \n", "26  1750728602234  ************   2.07  -411.00  21.034980  \n", "27  1750728643898  ************  12.85   -42.00  10.464940  \n", "28  *************  ************  13.68    72.00  12.192480  \n", "29  1750728601068  ************  35.45   175.00  13.095500  \n", "30  1750728603188  ************  20.82   200.00  12.449440  \n", "31  1750728648335  ************  19.76   217.00  10.370220  \n", "32  1750728609002  ************   8.80   -65.00  14.334200  \n", "33  1750728682966  ************   4.93  -354.00  21.566860  \n", "34  1750728602628  ************   9.31   -37.00  28.250980  \n", "35  1750728619993  ************   8.62   -84.00   8.878240  \n", "36  1750728634364  ************  11.87    -5.00   4.388800  \n", "37  1750728602139  ************   5.01    -0.00   5.561100  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["pos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'603373': '45.220',\n", " '301365': '16.41',\n", " '688025': '66.640',\n", " '603655': '28.370',\n", " '603089': '11.490',\n", " '001266': '25.74',\n", " '601860': '3.060',\n", " '300940': '25.30',\n", " '601003': '3.510',\n", " '603390': '14.090',\n", " '300331': '21.73',\n", " '002807': '4.93',\n", " '300815': '19.82',\n", " '600871': '1.960',\n", " '002349': '7.15',\n", " '600252': '2.520',\n", " '000698': '3.84',\n", " '603167': '10.540',\n", " '600025': '9.720',\n", " '688510': '21.690',\n", " '600583': '5.330',\n", " '000981': '2.09',\n", " '300981': '12.89',\n", " '301507': '13.78',\n", " '001332': '35.46',\n", " '001206': '21.14',\n", " '301055': '20.15',\n", " '600663': '8.860',\n", " '001330': '5.08',\n", " '603328': '9.450',\n", " '002545': '8.62',\n", " '603628': '11.920',\n", " '600558': '5.040'}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>side</th>\n", "      <th>id</th>\n", "      <th>pid</th>\n", "      <th>price</th>\n", "      <th>qty</th>\n", "      <th>sym</th>\n", "      <th>time</th>\n", "      <th>account_id</th>\n", "      <th>midpx</th>\n", "      <th>profit</th>\n", "      <th>fee</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>d9af38e5-95d4-48d2-ad3e-748af6a8fbc2</td>\n", "      <td>1cadc88f-4fd0-11f0-946d-27f1eca2d9d5</td>\n", "      <td>7.010000</td>\n", "      <td>6800.0</td>\n", "      <td>300299</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>7.00</td>\n", "      <td>-68.0</td>\n", "      <td>35.23216</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>60fc40c0-c997-4833-9296-8a5dc07d983e</td>\n", "      <td>1cae2817-4fd0-11f0-816c-27f1eca2d9d5</td>\n", "      <td>19.440000</td>\n", "      <td>400.0</td>\n", "      <td>300493</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>19.68</td>\n", "      <td>96.0</td>\n", "      <td>5.81376</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-1</td>\n", "      <td>b41e7bfc-9256-4a86-9330-5168ad2c74c4</td>\n", "      <td>1cae8064-4fd0-11f0-982a-27f1eca2d9d5</td>\n", "      <td>29.350000</td>\n", "      <td>300.0</td>\n", "      <td>300130</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>29.11</td>\n", "      <td>72.0</td>\n", "      <td>6.50706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-1</td>\n", "      <td>8c235daa-85f7-4565-8d89-77503a912596</td>\n", "      <td>1cae8064-4fd0-11f0-982a-27f1eca2d9d5</td>\n", "      <td>29.380000</td>\n", "      <td>600.0</td>\n", "      <td>300130</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>29.11</td>\n", "      <td>162.0</td>\n", "      <td>13.02528</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-1</td>\n", "      <td>890ec32c-3c1f-4a92-aa61-b4bf6f5fc1b6</td>\n", "      <td>1cae8064-4fd0-11f0-982a-27f1eca2d9d5</td>\n", "      <td>29.407778</td>\n", "      <td>900.0</td>\n", "      <td>300130</td>\n", "      <td>*************</td>\n", "      <td>************</td>\n", "      <td>29.11</td>\n", "      <td>268.0</td>\n", "      <td>19.55342</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   side                                    id  \\\n", "0     1  d9af38e5-95d4-48d2-ad3e-748af6a8fbc2   \n", "1     1  60fc40c0-c997-4833-9296-8a5dc07d983e   \n", "2    -1  b41e7bfc-9256-4a86-9330-5168ad2c74c4   \n", "3    -1  8c235daa-85f7-4565-8d89-77503a912596   \n", "4    -1  890ec32c-3c1f-4a92-aa61-b4bf6f5fc1b6   \n", "\n", "                                    pid      price     qty     sym  \\\n", "0  1cadc88f-4fd0-11f0-946d-27f1eca2d9d5   7.010000  6800.0  300299   \n", "1  1cae2817-4fd0-11f0-816c-27f1eca2d9d5  19.440000   400.0  300493   \n", "2  1cae8064-4fd0-11f0-982a-27f1eca2d9d5  29.350000   300.0  300130   \n", "3  1cae8064-4fd0-11f0-982a-27f1eca2d9d5  29.380000   600.0  300130   \n", "4  1cae8064-4fd0-11f0-982a-27f1eca2d9d5  29.407778   900.0  300130   \n", "\n", "            time    account_id  midpx  profit       fee  \n", "0  *************  ************   7.00   -68.0  35.23216  \n", "1  *************  ************  19.68    96.0   5.81376  \n", "2  *************  ************  29.11    72.0   6.50706  \n", "3  *************  ************  29.11   162.0  13.02528  \n", "4  *************  ************  29.11   268.0  19.55342  "]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["pos"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>pid</th>\n", "      <th>create_time</th>\n", "      <th>last_upd_time</th>\n", "      <th>symbol</th>\n", "      <th>account_id</th>\n", "      <th>price</th>\n", "      <th>quantity</th>\n", "      <th>filled_price</th>\n", "      <th>filled_quantity</th>\n", "      <th>order_type</th>\n", "      <th>operation</th>\n", "      <th>status</th>\n", "      <th>order_id</th>\n", "      <th>remark</th>\n", "      <th>remark3</th>\n", "      <th>market</th>\n", "      <th>error_msg</th>\n", "      <th>side</th>\n", "      <th>amt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>264</th>\n", "      <td>9083c8f4-10b7-49af-9116-12c5f609629b</td>\n", "      <td>1cadc425-4fd0-11f0-a20b-27f1eca2d9d5</td>\n", "      <td>*************</td>\n", "      <td>*************</td>\n", "      <td>300047</td>\n", "      <td>************</td>\n", "      <td>13.95</td>\n", "      <td>4000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>100</td>\n", "      <td>120</td>\n", "      <td>720766178238137373</td>\n", "      <td>open</td>\n", "      <td>54</td>\n", "      <td>2</td>\n", "      <td>Available capital in account is insufficient.</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>0878170b-2ee7-43e1-8c7d-b88a10be93bb</td>\n", "      <td>1cadc425-4fd0-11f0-a20b-27f1eca2d9d5</td>\n", "      <td>*************</td>\n", "      <td>*************</td>\n", "      <td>300047</td>\n", "      <td>************</td>\n", "      <td>14.53</td>\n", "      <td>4000</td>\n", "      <td>14.51</td>\n", "      <td>4000.0</td>\n", "      <td>1</td>\n", "      <td>100</td>\n", "      <td>100</td>\n", "      <td>720766178238137818</td>\n", "      <td>open</td>\n", "      <td>404</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>58040.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>354cf4de-b62d-4cd7-b537-bffb80d28ff3</td>\n", "      <td>1cadc425-4fd0-11f0-a20b-27f1eca2d9d5</td>\n", "      <td>*************</td>\n", "      <td>*************</td>\n", "      <td>300047</td>\n", "      <td>************</td>\n", "      <td>14.42</td>\n", "      <td>4000</td>\n", "      <td>14.42</td>\n", "      <td>4000.0</td>\n", "      <td>1</td>\n", "      <td>110</td>\n", "      <td>100</td>\n", "      <td>720766178238137831</td>\n", "      <td>close</td>\n", "      <td>417</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>-1</td>\n", "      <td>57680.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                       id  \\\n", "264  9083c8f4-10b7-49af-9116-12c5f609629b   \n", "18   0878170b-2ee7-43e1-8c7d-b88a10be93bb   \n", "85   354cf4de-b62d-4cd7-b537-bffb80d28ff3   \n", "\n", "                                      pid    create_time  last_upd_time  \\\n", "264  1cadc425-4fd0-11f0-a20b-27f1eca2d9d5  *************  *************   \n", "18   1cadc425-4fd0-11f0-a20b-27f1eca2d9d5  *************  *************   \n", "85   1cadc425-4fd0-11f0-a20b-27f1eca2d9d5  *************  *************   \n", "\n", "     symbol    account_id  price  quantity  filled_price  filled_quantity  \\\n", "264  300047  ************  13.95      4000           NaN              NaN   \n", "18   300047  ************  14.53      4000         14.51           4000.0   \n", "85   300047  ************  14.42      4000         14.42           4000.0   \n", "\n", "     order_type  operation  status            order_id remark  remark3  \\\n", "264           1        100     120  720766178238137373   open       54   \n", "18            1        100     100  720766178238137818   open      404   \n", "85            1        110     100  720766178238137831  close      417   \n", "\n", "     market                                      error_msg  side      amt  \n", "264       2  Available capital in account is insufficient.     1      NaN  \n", "18        2                                            NaN     1  58040.0  \n", "85        2                                            NaN    -1  57680.0  "]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["slcs[slcs['symbol']=='300047']"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['open', 'close'], dtype=object)"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["slcs['remark'].unique()"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["s2=slcs[slcs['remark']=='close']"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.****************"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["len(s2[s2['status']==110])/len(s2[s2['status']!=120])"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.33695652173913043"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["s3=slcs[slcs['remark']=='open']\n", "len(s3[s3['status']==110])/len(s3[s3['status']!=120])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 2}