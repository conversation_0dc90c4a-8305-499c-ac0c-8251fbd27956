{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/trade/t0_backtest/intraday\n", "['/home/<USER>/miniconda3/lib/python312.zip', '/home/<USER>/miniconda3/lib/python3.12', '/home/<USER>/miniconda3/lib/python3.12/lib-dynload', '', '/home/<USER>/miniconda3/lib/python3.12/site-packages']\n"]}, {"ename": "ModuleNotFoundError", "evalue": "No module named 'tca'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 27\u001b[39m\n\u001b[32m     24\u001b[39m \u001b[38;5;66;03m# sys.path.insert(0, str(notebook_dir.parent))\u001b[39;00m\n\u001b[32m     26\u001b[39m \u001b[38;5;28mprint\u001b[39m(sys.path)\n\u001b[32m---> \u001b[39m\u001b[32m27\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[34;01mbacktest\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m backtest \u001b[38;5;28;01mas\u001b[39;00m bt\n\u001b[32m     28\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[34;01mtca\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m tca\n\u001b[32m     29\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[34;01mtca\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m stk_tools\n", "\u001b[36mFile \u001b[39m\u001b[32m~/trade/t0_backtest/intraday/backtest/backtest.py:7\u001b[39m\n\u001b[32m      5\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[34;01muuid\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[34;01mdatetime\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[34;01mtca\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m tca\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[34;01<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[34mcreate_order\u001b[39m(sym,pid,price,qty,side,time,account_id):\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'tca'"]}], "source": ["\n", "# from test.intraday.backtest import backtest as bt\n", "# from intraday.tca import tca\n", "# import pandas as pd\n", "# from data import data_reader\n", "# from joblib import Parallel, delayed\n", "# import os\n", "# from pqdm.processes import pqdm\n", "# import datetime\n", "# from utils import stk_tools\n", "# import pickle\n", "\n", "\n", "from joblib import Parallel, delayed\n", "from pqdm.processes import pqdm\n", "import os, sys\n", "import pandas as pd\n", "import datetime\n", "import pickle\n", "from pathlib import Path\n", "\n", "\n", "notebook_dir = Path.cwd() \n", "print(str(notebook_dir.parent))\n", "# sys.path.insert(0, str(notebook_dir.parent))\n", "\n", "print(sys.path)\n", "from backtest import backtest as bt\n", "from tca import tca\n", "from tca import stk_tools\n", "\n", "sys.path.remove(str(notebook_dir.parent))\n", "\n", "# from data import data_reader"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/trade/t0_backtest/intraday\n"]}], "source": ["\n", "notebook_dir = Path.cwd() \n", "print(str(notebook_dir.parent))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pickle_dump(data,pt):\n", "    with open(pt, 'wb') as file:\n", "        pickle.dump(data, file)\n", "\n", "def pickle_load(pt):\n", "    with open(pt, 'rb') as file:\n", "        return pickle.load(file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_rongshuhai_inventory(date,pords):\n", "    try:\n", "        open_th=0.004\n", "        ret=bt.run_by_date(date,open_th,pords,isbt=True,rt_raw_tca=True,filt_sig_by_time=True)\n", "        return ret\n", "    except Exception as e:\n", "        print(\"run {} failed {}\".format(date,e))\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 回测数据格式\n", "-  dict= {'********':[{'id': 'ccf48cd5-83a9-41cf-b0d6-dea305d42635',  'symbol': '000002',  'quantity': 36400,  'account_id': 'test_account_w3',  'start_time': datetime.datetime(2024, 1, 2, 9, 35),  'end_time': datetime.datetime(2024, 1, 2, 14, 57)}, {'id': 'fe52a5b4-c0d8-4097-b88e-1fc2fdd045e0',  'symbol': '000063',  'quantity': 33000,  'account_id': 'test_account_w3',  'start_time': datetime.datetime(2024, 1, 2, 9, 35),  'end_time': datetime.datetime(2024, 1, 2, 14, 57)},]}\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# demo1\n", "pords=bt.read_positions(\"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/A500_rmtop20%_2024.csv\",date,\"sym\",\"qty\",account='test_account_w3',filt_type='csv')\n", "# demo2\n", "pords=bt.read_positions(\"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/daiwenzheng_inventory_processed.csv\",date,\"symbol\",\"qty\",account='test_account_w3',filt_type='csv')\n", "# demo3\n", "df=pd.read_csv(r\"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/zz2000_pos_0940.csv\")\n", "df.rename(columns={\"Unnamed: 0\": \"date\"}, inplace=True)\n", "d={}\n", "for data in df.to_dict('records'):\n", "    date=data['date'][:10].replace('-', '')\n", "    if date<\"********\" or date>\"********\":\n", "        continue\n", "    st=datetime.datetime.strptime(data['date'], '%Y-%m-%d %H:%M:%S')\n", "    \n", "    print(date)\n", "    l=[]\n", "    for k,v in data.items():\n", "        if k=='date':\n", "            continue\n", "        if v==0:\n", "            continue\n", "        po=bt.make_parentorder(k.split(\".\")[0],int(v),\"test_account_w3\",date,start_time=st) \n", "        l.append(po)\n", "    d[date]=l\n", "stk_tools.pickle_dump(d,\"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/zz2000_pos_0940.pkl\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datas=stk_tools.pickle_load(r\"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/hs300_pos_0935.pkl\")\n", "args=[]\n", "for k,v in datas.items():\n", "    args.append([k,pd.DataFrame(v)])\n", "results=pqdm(args,run_rongshuhai_inventory,50,argument_type='args')\n", "l=[_ for _ in results if _ is not None]\n", "stk_tools.pickle_dump(l,\"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_hs300_pos_0935.pkl\")\n", "pt=r\"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_hs300_pos_0935.pkl\"\n", "perf_daily,perf_annually=bt.get_perf(pt)\n", "perf_daily.to_csv(r\"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_perf_daily_hs300_pos_0935.csv\",index=False)\n", "perf_annually.to_csv(r\"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_perf_annually_hs300_pos_0935.csv\",index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}