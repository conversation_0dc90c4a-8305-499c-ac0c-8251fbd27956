#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样本数据集处理脚本
先从完整数据集中抽取position=100000的样本数据进行测试
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import subprocess
import time
from datetime import datetime

def create_sample_from_full_dataset(input_file, position_value=100000, sample_size=10000, output_file=None):
    """
    从完整数据集中创建样本
    """
    print(f"=== 从完整数据集创建样本 ===")
    print(f"输入文件: {input_file}")
    print(f"筛选条件: position = {position_value}")
    print(f"样本大小: {sample_size}")
    
    try:
        # 读取数据
        print("正在读取数据...")
        if input_file.endswith('.fea') or input_file.endswith('.feather'):
            df = pd.read_feather(input_file)
        else:
            df = pd.read_csv(input_file)
        
        print(f"✅ 原始数据形状: {df.shape}")
        
        # 筛选指定position的数据
        df_filtered = df[df['position'] == position_value].copy()
        print(f"✅ position={position_value}的数据形状: {df_filtered.shape}")
        
        if len(df_filtered) == 0:
            print(f"❌ 没有找到position={position_value}的数据")
            return None
        
        # 抽样
        if len(df_filtered) > sample_size:
            print(f"数据量较大，进行抽样...")
            # 分层抽样：按日期分层
            dates = df_filtered['date'].unique()
            sample_per_date = max(1, sample_size // len(dates))
            
            sampled_dfs = []
            for date in dates:
                date_df = df_filtered[df_filtered['date'] == date]
                if len(date_df) <= sample_per_date:
                    sampled_dfs.append(date_df)
                else:
                    sampled_dfs.append(date_df.sample(n=sample_per_date, random_state=42))
            
            df_sample = pd.concat(sampled_dfs, ignore_index=True)
            print(f"✅ 抽样完成，样本形状: {df_sample.shape}")
        else:
            df_sample = df_filtered.copy()
            print(f"✅ 数据量适中，使用全部数据")
        
        # 显示样本信息
        print(f"✅ 样本数据期间: {df_sample['date'].min()} - {df_sample['date'].max()}")
        print(f"✅ 样本标的数量: {df_sample['code'].nunique()}")
        print(f"✅ 样本交易日数: {df_sample['date'].nunique()}")
        print(f"✅ 样本有交易记录数: {len(df_sample[df_sample['round_num'] > 0])}")
        
        # 保存样本数据
        if output_file:
            print(f"保存样本数据到: {output_file}")
            # 确保输出目录存在
            output_dir = Path(output_file).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            df_sample.to_csv(output_file, index=False)
            print(f"✅ 样本数据已保存")
        
        return df_sample
        
    except Exception as e:
        print(f"❌ 创建样本时出错: {e}")
        return None

def run_sample_stats_analysis(input_file, output_dir, position_value=100000):
    """
    运行样本统计分析
    """
    print(f"\n=== 运行样本统计分析 ===")
    print(f"输入文件: {input_file}")
    print(f"输出目录: {output_dir}")
    
    # 确保输出目录存在
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 定义输出文件
    output_files = {
        'daily': f"{output_dir}/perf_daily_sample_{position_value}.csv",
        'symbol': f"{output_dir}/perf_symbol_sample_{position_value}.csv", 
        'annual': f"{output_dir}/perf_annually_sample_{position_value}.csv",
        'rolling': f"{output_dir}/perf_symbol_roll_20_sample_{position_value}.xlsx"
    }
    
    # 统计分析脚本
    scripts = {
        'daily': 'daily_stats_analysis.py',
        'symbol': 'symbol_stats_analysis.py',
        'annual': 'annual_stats_analysis.py',
        'rolling': 'rolling_stats_analysis.py'
    }
    
    # 执行各种统计分析
    results = {}
    total_time = 0
    
    for analysis_type, script_name in scripts.items():
        print(f"\n--- 执行{analysis_type}统计分析 ---")
        start_time = time.time()
        
        try:
            # 运行脚本
            cmd = ['python', script_name, input_file, output_files[analysis_type]]
            if analysis_type == 'rolling':
                cmd.append('20')  # 滚动窗口大小
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                cwd='/home/<USER>/trade/t0_backtest/intraday',
                timeout=600  # 10分钟超时
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            total_time += execution_time
            
            if result.returncode == 0:
                print(f"✅ {analysis_type}统计完成 (耗时: {execution_time:.1f}秒)")
                
                # 检查输出文件
                if os.path.exists(output_files[analysis_type]):
                    file_size = os.path.getsize(output_files[analysis_type])
                    print(f"✅ 输出文件: {output_files[analysis_type]} ({file_size:,} bytes)")
                    
                    # 读取并显示结果摘要
                    try:
                        if output_files[analysis_type].endswith('.csv'):
                            df_result = pd.read_csv(output_files[analysis_type])
                            print(f"✅ 记录数: {len(df_result)}, 列数: {len(df_result.columns)}")
                        elif output_files[analysis_type].endswith('.xlsx'):
                            # 读取Excel文件的所有sheet
                            excel_file = pd.ExcelFile(output_files[analysis_type])
                            sheet_count = len(excel_file.sheet_names)
                            print(f"✅ Excel文件包含 {sheet_count} 个sheet")
                    except Exception as e:
                        print(f"⚠️  读取结果文件时出错: {e}")
                    
                    results[analysis_type] = {
                        'success': True,
                        'time': execution_time,
                        'file': output_files[analysis_type]
                    }
                else:
                    print(f"❌ 输出文件未生成: {output_files[analysis_type]}")
                    results[analysis_type] = {'success': False, 'time': execution_time}
            else:
                print(f"❌ {analysis_type}统计失败")
                print(f"错误信息: {result.stderr}")
                results[analysis_type] = {'success': False, 'time': execution_time}
                
        except subprocess.TimeoutExpired:
            print(f"❌ {analysis_type}统计超时")
            results[analysis_type] = {'success': False, 'time': 600}
        except Exception as e:
            print(f"❌ {analysis_type}统计出错: {e}")
            results[analysis_type] = {'success': False, 'time': 0}
    
    # 统计总结
    print(f"\n=== 样本统计分析总结 ===")
    success_count = sum(1 for r in results.values() if r['success'])
    total_count = len(results)
    
    print(f"总分析数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    print(f"总耗时: {total_time:.1f} 秒")
    
    for analysis_type, result in results.items():
        status = "✅" if result['success'] else "❌"
        print(f"{status} {analysis_type}统计 (耗时: {result['time']:.1f}秒)")
    
    return results

def main():
    """
    主函数
    """
    print("=== 样本数据集统计分析 ===")
    
    # 配置参数
    input_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/signal_merged.fea"
    position_value = 100000
    sample_size = 50000  # 5万条样本
    output_dir = f"/home/<USER>/trade/t0_backtest/intraday/results/sample_position_{position_value}"
    sample_data_file = f"{output_dir}/sample_data_position_{position_value}.csv"
    
    print(f"输入文件: {input_file}")
    print(f"筛选条件: position = {position_value}")
    print(f"样本大小: {sample_size}")
    print(f"输出目录: {output_dir}")
    
    # 第一步：创建样本
    df_sample = create_sample_from_full_dataset(input_file, position_value, sample_size, sample_data_file)
    
    if df_sample is None:
        print("❌ 样本创建失败，程序终止")
        return False
    
    # 第二步：运行统计分析
    results = run_sample_stats_analysis(sample_data_file, output_dir, position_value)
    
    # 最终总结
    success_count = sum(1 for r in results.values() if r['success'])
    total_count = len(results)
    
    if success_count == total_count:
        print(f"\n🎉 所有样本统计分析完成！结果保存在: {output_dir}")
        print(f"💡 如果样本结果正常，可以运行完整数据集处理")
    else:
        print(f"\n⚠️  部分统计分析失败，请检查错误信息")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
