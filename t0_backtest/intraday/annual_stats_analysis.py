#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按年统计分析脚本
基于tca.stats_agg_annually函数，创建按年统计脚本
输出类似perf_annually_weipan_100000_w3.csv的结果
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path

# 添加tca模块路径
sys.path.append('/home/<USER>/trade/t0_backtest/intraday/tca')
import tca

def prepare_daily_stats_for_annual(df):
    """
    先计算按日统计，然后用于年度统计
    """
    print("准备按日统计数据用于年度汇总...")
    
    # 导入按日统计脚本的函数
    sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
    from daily_stats_analysis import transform_data_for_daily_stats, calculate_basic_daily_stats
    
    # 转换数据格式
    transformed_df = transform_data_for_daily_stats(df)
    
    # 按日期分组计算统计指标
    daily_stats = []
    
    for date, group in transformed_df.groupby('date'):
        print(f"处理日期: {date}")
        stats = calculate_basic_daily_stats(group)
        stats['date'] = date
        daily_stats.append(stats)
    
    daily_stats_df = pd.DataFrame(daily_stats)
    print(f"生成 {len(daily_stats_df)} 条日统计记录")
    
    return daily_stats_df

def calculate_annual_stats(daily_stats_df):
    """
    基于日统计数据计算年度统计
    """
    print("计算年度统计指标...")
    
    try:
        # 使用tca.stats_agg_annually函数
        annual_stats = tca.stats_agg_annually(daily_stats_df)
        return annual_stats
    except Exception as e:
        print(f"使用tca函数计算年度统计时出错: {e}")
        # 手动计算年度统计
        return calculate_basic_annual_stats(daily_stats_df)

def calculate_basic_annual_stats(daily_stats_df):
    """
    手动计算基础的年度统计指标
    """
    print("手动计算年度统计指标...")
    
    stats = {}
    
    # 基础指标
    stats['交易天数'] = len(daily_stats_df)
    stats['平均底仓金额（万元）'] = daily_stats_df['hold_amt'].mean() / 10000  # 转换为万元
    stats['平均交易金额（万元）'] = daily_stats_df['trd_amt'].mean() / 10000  # 转换为万元
    stats['费用'] = daily_stats_df['fee'].sum()
    stats['盈利'] = daily_stats_df['profit'].sum()
    stats['费后盈利'] = daily_stats_df['profit_ac'].sum()
    
    # 换手率相关
    stats['平均换手%'] = daily_stats_df['turnover'].mean() * 100
    stats['年化换手（倍）'] = daily_stats_df['turnover'].mean() * 250
    
    # 收益率相关
    stats['底仓收益(bps)'] = daily_stats_df['ret2hold_ac'].sum() * 10000  # 转换为bps
    stats['年化底仓收益%'] = daily_stats_df['ret2hold_ac'].mean() * 250 * 100
    
    # 胜率
    stats['日胜率%'] = len(daily_stats_df[daily_stats_df['profit_ac'] > 0]) / stats['交易天数'] * 100
    
    # 敞口相关
    if 'max_exposure_rate' in daily_stats_df.columns:
        stats['最大敞口%'] = daily_stats_df['max_exposure_rate'].max() * 100
    else:
        stats['最大敞口%'] = 0
    
    if 'max_long_exposure_rate' in daily_stats_df.columns:
        stats['最大资金占用%'] = daily_stats_df['max_long_exposure_rate'].max() * 100
    else:
        stats['最大资金占用%'] = 0
    
    # 转换为DataFrame
    annual_stats_df = pd.DataFrame([stats])
    
    # 格式化数值
    annual_stats_df['费用'] = annual_stats_df['费用'].round(1)
    annual_stats_df['盈利'] = annual_stats_df['盈利'].round(1)
    annual_stats_df['费后盈利'] = annual_stats_df['费后盈利'].round(1)
    annual_stats_df['平均换手%'] = annual_stats_df['平均换手%'].round(1)
    annual_stats_df['年化换手（倍）'] = annual_stats_df['年化换手（倍）'].round(1)
    annual_stats_df['底仓收益(bps)'] = annual_stats_df['底仓收益(bps)'].round(1)
    annual_stats_df['年化底仓收益%'] = annual_stats_df['年化底仓收益%'].round(1)
    annual_stats_df['日胜率%'] = annual_stats_df['日胜率%'].round(1)
    annual_stats_df['最大敞口%'] = annual_stats_df['最大敞口%'].round(1)
    annual_stats_df['最大资金占用%'] = annual_stats_df['最大资金占用%'].round(1)
    
    return annual_stats_df

def save_annual_stats(annual_stats_df, output_file):
    """
    保存年度统计结果
    """
    print(f"保存结果到: {output_file}")
    
    # 确保输出目录存在
    output_dir = Path(output_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存CSV文件
    annual_stats_df.to_csv(output_file, index=False)
    print(f"已保存年度统计记录")

def main(input_file, output_file):
    """
    主函数
    """
    print(f"=== 按年统计分析 ===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    # 读取数据
    print("读取数据...")
    df = pd.read_csv(input_file)
    print(f"原始数据形状: {df.shape}")
    
    # 准备按日统计数据
    daily_stats_df = prepare_daily_stats_for_annual(df)
    
    # 计算年度统计
    annual_stats_df = calculate_annual_stats(daily_stats_df)
    
    # 保存结果
    save_annual_stats(annual_stats_df, output_file)
    
    print("=== 按年统计分析完成 ===")
    return annual_stats_df

if __name__ == "__main__":
    # 默认参数
    input_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/sample2.csv"
    output_file = "/home/<USER>/trade/t0_backtest/intraday/results/annual_stats_sample.csv"
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    # 执行分析
    annual_stats_df = main(input_file, output_file)
