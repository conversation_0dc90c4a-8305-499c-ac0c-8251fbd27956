import pandas as pd
from joblib import Parallel, delayed
from data.data_reader import get_stk_price_from_file
import os
import uuid
import datetime

from tca import tca


# date = '20250618'
# df = get_stk_price_from_file(date)

# print(df.tail(2))
# print(f'shape: {df.shape}')
# print(f'columns: {df.columns}')
# print(f'dtypes: {df.dtypes}')


file = './signal_merged.fea'

# df = pd.read_feather(file)
# print(df.tail())
# print(f'shape: {df.shape}')
# print(f'columns: {df.columns}')
# print(f'dtypes: {df.dtypes}')

# df = df.sort_values(by=['date'])
# # sample = df.sample(100)

# # sample.to_csv('sample.csv', index=False)


# head = df.head(50)
# tail = df.tail(50)

# sample = pd.concat([head, tail])
# sample.to_csv('sample2.csv', index=False)

sample = pd.read_csv('sample2.csv', dtype={'date':str, 'code':str})
print(f'code: {sample["code"]}')

sample.rename(columns={'code':'symbol'
                       
                       }, inplace=True)


r = tca.stats_agg_by_sym_window(sample)