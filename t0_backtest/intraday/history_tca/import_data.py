# csv_file_path = 'data.csv'
chunk_size = 5000
chunks_processed = 0

# 准备插入 SQL 语句
insert_sql_ignore = """
INSERT IGNORE INTO history_t0_tca (
    code, date, max_amt, round_num, long_round_num, short_round_num,
    trade_num, long_open_vol, long_open_amt, short_open_vol, short_open_amt,
    long_close_vol, long_close_amt, short_close_vol, short_close_amt,
    profit, fee, profit_ac, profit_long, fee_long, profit_ac_long,
    profit_short, fee_short, profit_ac_short, amt_open, vol_open,
    ret_to_trade, ret_to_hold, ret_to_trade_ac, ret_to_hold_ac,
    ret_to_trade_long, ret_to_hold_long, ret_to_trade_ac_long,
    ret_to_hold_ac_long, ret_to_trade_short, ret_to_hold_short,
    ret_to_trade_ac_short, ret_to_hold_ac_short, position
) VALUES (
    :code, :date, :max_amt, :round_num, :long_round_num, :short_round_num,
    :trade_num, :long_open_vol, :long_open_amt, :short_open_vol, :short_open_amt,
    :long_close_vol, :long_close_amt, :short_close_vol, :short_close_amt,
    :profit, :fee, :profit_ac, :profit_long, :fee_long, :profit_ac_long,
    :profit_short, :fee_short, :profit_ac_short, :amt_open, :vol_open,
    :ret_to_trade, :ret_to_hold, :ret_to_trade_ac, :ret_to_hold_ac,
    :ret_to_trade_long, :ret_to_hold_long, :ret_to_trade_ac_long,
    :ret_to_hold_ac_long, :ret_to_trade_short, :ret_to_hold_short,
    :ret_to_trade_ac_short, :ret_to_hold_ac_short, :position
);
"""

from typing import Tuple
# from py import log
from loguru import logger

import pymysql
import pandas as pd
from sqlalchemy import create_engine,text

def get_connection(host,db,user,passwd,port=3306,engine=False):
    if engine:
        return create_engine("mysql+pymysql://{}:{}@{}/{}?charset={}".format(user,passwd,host,db,'utf8mb4'))
    conn=pymysql.connect(host=host,db=db,user=user,passwd=passwd,port=port)
    return conn



engine = get_connection("************","zs_trading_data","root","jtwmy,dt4gx",engine=True)


file = '/home/<USER>/trade/t0_backtest/intraday/history_tca/signal_merged.fea'
df = pd.read_feather(file)
df['code'] = df['code'].astype('str').str.zfill(6)




# 导出为 CSV（确保路径可写）
csv_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/signal_merged.csv"
df.to_csv(csv_file, index=False)  # 不保存索引



# 使用 SQLAlchemy 的连接执行批量插入
# with engine.connect() as connection:
    
# for i in range(0, len(df), chunk_size):
#     chunk = df.iloc[i:i+chunk_size]
#     # 数据预处理
#     # chunk['date'] = pd.to_datetime(chunk['date'])
    
#     # 将 DataFrame 转换为字典列表
#     # data_dicts = chunk.to_dict('records')
    
#     # 执行批量插入
#     try:
#         chunk.to_sql(
#             name='history_t0_tca',
#             con=engine,
#             if_exists='append',  # 追加数据
#             index=False          # 不写入索引
#         )
        
        
#         # connection.execute(text(insert_sql_ignore), data_dicts)
#         print(f"第 {chunks_processed + 1} 个数据块已成功导入。")
#     except Exception as e:
#         print(f"第 {chunks_processed + 1} 个数据块导入失败: {e}")
#         # 可以选择记录失败的 chunk 或进行其他处理
    
#     chunks_processed += 1

# print("所有数据块已处理完成。")