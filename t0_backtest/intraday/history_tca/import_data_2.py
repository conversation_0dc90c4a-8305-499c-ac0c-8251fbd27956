import pymysql

# 数据库连接配置
db_config = {
    'host': '************',
    'port': 3306,
    'user': 'root',
    'password': 'jtwmy,dt4gx',
    'database': 'zs_trading_data',
    'local_infile': True
}



# 创建数据库连接
connection = pymysql.connect(**db_config)


csv_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/signal_merged.csv"

try:
    with connection.cursor() as cursor:
        # 执行 LOAD DATA INFILE
        load_sql = f"""
        LOAD DATA LOCAL INFILE '{csv_file}'
        INTO TABLE history_t0_tca
        FIELDS TERMINATED BY ','  # CSV 分隔符
        ENCLOSED BY '"'           # 字段引号（如果有）
        LINES TERMINATED BY '\n'  # 行分隔符
        IGNORE 1 ROWS;            # 如果 CSV 有标题行，忽略第一行
        """
        cursor.execute(load_sql)
    connection.commit()
    print("数据导入成功！")
except Exception as e:
    print(f"导入失败: {e}")
    connection.rollback()
finally:
    connection.close()