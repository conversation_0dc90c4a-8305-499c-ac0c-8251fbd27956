#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滚动统计样本脚本
只处理前100个标的作为示例
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import time

def main():
    """
    主函数 - 处理前100个标的的滚动统计
    """
    print("=== 滚动统计样本分析（前100个标的）===")
    
    # 文件路径
    input_file = "/home/<USER>/trade/t0_backtest/intraday/results/final_position_100000/filtered_data_position_100000.csv"
    output_file = "/home/<USER>/trade/t0_backtest/intraday/results/final_position_100000/perf_symbol_roll_20_sample_100.xlsx"
    
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    start_time = time.time()
    
    # 读取数据
    print("读取数据...")
    df = pd.read_csv(input_file)
    print(f"原始数据形状: {df.shape}")
    
    # 过滤有交易的记录
    df_traded = df[df['round_num'] > 0].copy()
    print(f"有交易记录数: {len(df_traded)}")
    
    # 获取前100个标的
    symbols = df_traded['code'].unique()[:100]
    print(f"处理标的数: {len(symbols)}")
    
    # 筛选前100个标的的数据
    df_sample = df_traded[df_traded['code'].isin(symbols)].copy()
    print(f"样本数据形状: {df_sample.shape}")
    
    # 导入按日统计脚本的函数
    sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
    from daily_stats_analysis import calculate_basic_daily_stats
    
    # 按标的处理
    symbol_rolling_stats = {}
    
    for i, symbol in enumerate(symbols):
        if (i + 1) % 10 == 0:
            print(f"处理标的 {i+1}/{len(symbols)}: {symbol}")
        
        try:
            # 获取该标的的数据
            symbol_data = df_sample[df_sample['code'] == symbol].copy()
            
            if len(symbol_data) == 0:
                continue
            
            # 按日期分组计算统计指标
            daily_stats = []
            for date, group in symbol_data.groupby('date'):
                # 简化数据转换
                group_copy = group.copy()
                group_copy['symbol'] = symbol
                group_copy['price'] = group_copy['max_amt']
                group_copy['quantity'] = 1
                group_copy['hold_amt'] = group_copy['max_amt']
                group_copy['trd_amt'] = group_copy['amt_open']

                # 添加缺失字段
                group_copy['max_exposure'] = group_copy['position']
                group_copy['max_long_exposure'] = group_copy['long_open_amt']

                stats = calculate_basic_daily_stats(group_copy)
                stats['date'] = date
                daily_stats.append(stats)
            
            if len(daily_stats) == 0:
                continue
            
            daily_stats_df = pd.DataFrame(daily_stats)
            daily_stats_df['date'] = pd.to_datetime(daily_stats_df['date'], format='%Y%m%d')
            daily_stats_df = daily_stats_df.sort_values('date').reset_index(drop=True)
            
            # 计算滚动20日统计
            n = 20
            key_cols = ['date', 'hold_amt', 'trd_amt', 'profit', 'profit_ac', 'ret2hold_ac']
            available_cols = [col for col in key_cols if col in daily_stats_df.columns]
            df_subset = daily_stats_df[available_cols].copy()
            
            # 计算滚动统计
            rolling_stats = df_subset.rolling(window=n, min_periods=1).agg({
                'hold_amt': 'mean',
                'trd_amt': 'sum',
                'profit': 'sum',
                'profit_ac': 'sum',
                'ret2hold_ac': 'mean'
            })
            
            # 添加日期和标的列
            rolling_stats['date'] = daily_stats_df['date']
            rolling_stats['symbol'] = symbol
            
            # 重新排列列顺序
            cols = ['date', 'symbol'] + [col for col in rolling_stats.columns if col not in ['date', 'symbol']]
            rolling_stats = rolling_stats[cols]
            
            # 获取每周最后一个交易日
            rolling_stats['year_week'] = rolling_stats['date'].dt.strftime('%Y-%U')
            weekly_last_days = rolling_stats.groupby('year_week')['date'].max()
            
            # 筛选每周最后一个交易日的数据
            weekly_rolling_stats = rolling_stats[rolling_stats['date'].isin(weekly_last_days)].copy()
            weekly_rolling_stats = weekly_rolling_stats.drop('year_week', axis=1)
            
            if len(weekly_rolling_stats) > 0:
                symbol_rolling_stats[symbol] = weekly_rolling_stats
                
        except Exception as e:
            print(f"处理标的 {symbol} 时出错: {e}")
            continue
    
    # 保存结果
    print(f"\n保存结果...")
    
    # 确保输出目录存在
    output_dir = Path(output_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if len(symbol_rolling_stats) == 0:
        print("⚠️  没有滚动统计数据需要保存")
        pd.DataFrame().to_excel(output_file, index=False)
    else:
        # 保存为Excel文件，每个标的一个sheet
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            total_records = 0
            saved_count = 0
            
            for symbol, rolling_stats_df in symbol_rolling_stats.items():
                if len(rolling_stats_df) > 0:
                    try:
                        # 格式化日期
                        rolling_stats_df_copy = rolling_stats_df.copy()
                        rolling_stats_df_copy['date'] = rolling_stats_df_copy['date'].dt.strftime('%Y%m%d')
                        
                        # 使用标的代码作为sheet名称
                        sheet_name = str(symbol)[:31]
                        rolling_stats_df_copy.to_excel(writer, sheet_name=sheet_name, index=False)
                        total_records += len(rolling_stats_df_copy)
                        saved_count += 1
                        
                    except Exception as e:
                        print(f"保存标的 {symbol} 时出错: {e}")
                        continue
            
            print(f"✅ 已保存 {saved_count} 个标的，共 {total_records} 条滚动统计记录")
    
    total_time = time.time() - start_time
    print(f"=== 滚动统计样本分析完成 ===")
    print(f"总耗时: {total_time/60:.1f}分钟")
    print(f"处理标的数: {len(symbol_rolling_stats)}")
    
    return len(symbol_rolling_stats) > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
