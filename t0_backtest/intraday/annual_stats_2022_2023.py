#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2022-2023年度统计分析脚本
筛选position=100000且时间在20220101-20231231之间的数据进行年化回测统计
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import time
from datetime import datetime

def filter_data_2022_2023(input_file, position_value=100000):
    """
    筛选2022-2023年且position=100000的数据
    """
    print(f"=== 筛选2022-2023年数据 ===")
    print(f"输入文件: {input_file}")
    print(f"筛选条件: position = {position_value}, 日期 20220101-20231231")
    
    try:
        # 读取数据
        print("正在读取数据...")
        if input_file.endswith('.fea') or input_file.endswith('.feather'):
            df = pd.read_feather(input_file)
        else:
            df = pd.read_csv(input_file)
        
        print(f"✅ 原始数据形状: {df.shape}")
        
        # 筛选position=100000的数据
        df_position = df[df['position'] == position_value].copy()
        print(f"✅ position={position_value}的数据形状: {df_position.shape}")
        
        # 筛选2022-2023年的数据
        df_filtered = df_position[
            (df_position['date'] >= 20220101) & 
            (df_position['date'] <= 20231231)
        ].copy()
        print(f"✅ 2022-2023年筛选后数据形状: {df_filtered.shape}")
        
        if len(df_filtered) == 0:
            print(f"❌ 没有找到符合条件的数据")
            return None
        
        # 显示数据信息
        print(f"✅ 数据期间: {df_filtered['date'].min()} - {df_filtered['date'].max()}")
        print(f"✅ 标的数量: {df_filtered['code'].nunique()}")
        print(f"✅ 交易日数: {df_filtered['date'].nunique()}")
        print(f"✅ 有交易记录数: {len(df_filtered[df_filtered['round_num'] > 0])}")
        
        return df_filtered
        
    except Exception as e:
        print(f"❌ 筛选数据时出错: {e}")
        return None

def calculate_annual_stats_2022_2023(df_filtered):
    """
    计算2022-2023年的年度统计
    """
    print("\n=== 计算2022-2023年度统计 ===")
    
    # 导入按日统计脚本的函数
    sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
    from daily_stats_analysis import transform_data_for_daily_stats, calculate_basic_daily_stats
    
    # 转换数据格式
    transformed_df = transform_data_for_daily_stats(df_filtered)
    
    # 按日期分组计算统计指标
    daily_stats = []
    
    for date, group in transformed_df.groupby('date'):
        print(f"处理日期: {date}")
        stats = calculate_basic_daily_stats(group)
        stats['date'] = date
        daily_stats.append(stats)
    
    daily_stats_df = pd.DataFrame(daily_stats)
    print(f"生成 {len(daily_stats_df)} 条日统计记录")
    
    # 计算年度统计
    stats = {}
    
    # 基础指标
    stats['数据期间'] = f"{df_filtered['date'].min()}-{df_filtered['date'].max()}"
    stats['交易天数'] = len(daily_stats_df)
    stats['平均底仓金额（万元）'] = daily_stats_df['hold_amt'].mean() / 10000  # 转换为万元
    stats['平均交易金额（万元）'] = daily_stats_df['trd_amt'].mean() / 10000  # 转换为万元
    stats['费用'] = daily_stats_df['fee'].sum()
    stats['盈利'] = daily_stats_df['profit'].sum()
    stats['费后盈利'] = daily_stats_df['profit_ac'].sum()
    
    # 换手率相关
    stats['平均换手%'] = daily_stats_df['turnover'].mean() * 100
    stats['年化换手（倍）'] = daily_stats_df['turnover'].mean() * 250
    
    # 收益率相关
    stats['底仓收益(bps)'] = daily_stats_df['ret2hold_ac'].sum() * 10000  # 转换为bps
    stats['年化底仓收益%'] = daily_stats_df['ret2hold_ac'].mean() * 250 * 100
    
    # 胜率
    stats['日胜率%'] = len(daily_stats_df[daily_stats_df['profit_ac'] > 0]) / stats['交易天数'] * 100
    
    # 敞口相关
    if 'max_exposure_rate' in daily_stats_df.columns:
        stats['最大敞口%'] = daily_stats_df['max_exposure_rate'].max() * 100
    else:
        stats['最大敞口%'] = 0
    
    if 'max_long_exposure_rate' in daily_stats_df.columns:
        stats['最大资金占用%'] = daily_stats_df['max_long_exposure_rate'].max() * 100
    else:
        stats['最大资金占用%'] = 0
    
    # 计算年度分解统计
    annual_breakdown = {}
    
    # 按年份分组
    daily_stats_df['year'] = pd.to_datetime(daily_stats_df['date'], format='%Y%m%d').dt.year
    
    for year in [2022, 2023]:
        year_data = daily_stats_df[daily_stats_df['year'] == year]
        if len(year_data) > 0:
            annual_breakdown[f'{year}年交易天数'] = len(year_data)
            annual_breakdown[f'{year}年费后盈利'] = year_data['profit_ac'].sum()
            annual_breakdown[f'{year}年底仓收益(bps)'] = year_data['ret2hold_ac'].sum() * 10000
            annual_breakdown[f'{year}年年化收益%'] = year_data['ret2hold_ac'].mean() * 250 * 100
            annual_breakdown[f'{year}年日胜率%'] = len(year_data[year_data['profit_ac'] > 0]) / len(year_data) * 100
    
    # 合并统计结果
    stats.update(annual_breakdown)
    
    # 转换为DataFrame
    annual_stats_df = pd.DataFrame([stats])
    
    # 格式化数值
    for col in annual_stats_df.columns:
        if annual_stats_df[col].dtype in ['float64', 'int64'] and col != '数据期间':
            annual_stats_df[col] = annual_stats_df[col].round(2)
    
    return annual_stats_df, daily_stats_df

def save_annual_stats_2022_2023(annual_stats_df, daily_stats_df, output_dir):
    """
    保存2022-2023年度统计结果
    """
    print(f"\n=== 保存统计结果 ===")
    
    # 确保输出目录存在
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 保存年度汇总统计
    annual_file = f"{output_dir}/perf_annually_2022_2023_position_100000.csv"
    annual_stats_df.to_csv(annual_file, index=False, encoding='utf-8-sig')
    print(f"✅ 年度统计已保存: {annual_file}")
    
    # 保存按日统计（用于进一步分析）
    daily_file = f"{output_dir}/perf_daily_2022_2023_position_100000.csv"
    daily_stats_df.to_csv(daily_file, index=False)
    print(f"✅ 按日统计已保存: {daily_file}")
    
    # 生成详细报告
    report_file = f"{output_dir}/annual_report_2022_2023.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("2022-2023年度回测统计报告\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 写入年度统计
        f.write("年度统计汇总:\n")
        for col, val in annual_stats_df.iloc[0].items():
            f.write(f"- {col}: {val}\n")
        
        f.write(f"\n按日统计记录数: {len(daily_stats_df)}\n")
        f.write(f"数据文件: {daily_file}\n")
    
    print(f"✅ 详细报告已保存: {report_file}")

def main():
    """
    主函数
    """
    print("=== 2022-2023年度统计分析 ===")
    
    # 配置参数
    input_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/signal_merged.fea"
    output_dir = "/home/<USER>/trade/t0_backtest/intraday/results/annual_2022_2023"
    position_value = 100000
    
    print(f"输入文件: {input_file}")
    print(f"输出目录: {output_dir}")
    print(f"筛选条件: position = {position_value}, 时间 2022-2023")
    
    start_time = time.time()
    
    # 第一步：筛选数据
    df_filtered = filter_data_2022_2023(input_file, position_value)
    
    if df_filtered is None:
        print("❌ 数据筛选失败，程序终止")
        return False
    
    # 第二步：计算年度统计
    annual_stats_df, daily_stats_df = calculate_annual_stats_2022_2023(df_filtered)
    
    # 第三步：保存结果
    save_annual_stats_2022_2023(annual_stats_df, daily_stats_df, output_dir)
    
    # 显示关键结果
    print(f"\n=== 关键统计结果 ===")
    key_metrics = ['交易天数', '年化底仓收益%', '日胜率%', '平均换手%']
    for metric in key_metrics:
        if metric in annual_stats_df.columns:
            value = annual_stats_df[metric].iloc[0]
            print(f"{metric}: {value}")
    
    total_time = time.time() - start_time
    print(f"\n=== 分析完成 ===")
    print(f"总耗时: {total_time:.1f}秒")
    print(f"结果保存在: {output_dir}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
