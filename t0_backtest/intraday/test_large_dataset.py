#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整数据集测试脚本
在signal_merged.fea上进行抽样测试，确保脚本能处理大数据集
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import subprocess
import time

def load_and_sample_data(input_file, sample_size=10000, output_file=None):
    """
    加载完整数据集并进行抽样
    """
    print(f"=== 加载和抽样数据 ===")
    print(f"输入文件: {input_file}")
    print(f"抽样大小: {sample_size}")
    
    try:
        # 读取feather文件
        print("正在读取feather文件...")
        df = pd.read_feather(input_file)
        print(f"✅ 成功读取数据，形状: {df.shape}")
        print(f"✅ 列名: {list(df.columns)}")
        
        # 显示数据基本信息
        print(f"✅ 数据期间: {df['date'].min()} - {df['date'].max()}")
        print(f"✅ 标的数量: {df['code'].nunique()}")
        print(f"✅ 交易日数: {df['date'].nunique()}")
        
        # 进行抽样
        if len(df) > sample_size:
            print(f"数据量较大({len(df)}行)，进行抽样...")
            
            # 分层抽样：按日期分层，确保每个日期都有代表性
            dates = df['date'].unique()
            sample_per_date = max(1, sample_size // len(dates))
            
            sampled_dfs = []
            for date in dates:
                date_df = df[df['date'] == date]
                if len(date_df) <= sample_per_date:
                    sampled_dfs.append(date_df)
                else:
                    sampled_dfs.append(date_df.sample(n=sample_per_date, random_state=42))
            
            df_sample = pd.concat(sampled_dfs, ignore_index=True)
            print(f"✅ 抽样完成，抽样后形状: {df_sample.shape}")
        else:
            df_sample = df.copy()
            print(f"✅ 数据量适中，使用全部数据")
        
        # 保存抽样数据
        if output_file:
            print(f"保存抽样数据到: {output_file}")
            df_sample.to_csv(output_file, index=False)
            print(f"✅ 抽样数据已保存")
        
        return df_sample
        
    except Exception as e:
        print(f"❌ 加载数据时出错: {e}")
        return None

def test_script_on_large_data(script_name, input_file, output_file, description, timeout=300):
    """
    在大数据集上测试脚本
    """
    print(f"\n=== 测试 {description} (大数据集) ===")
    print(f"脚本: {script_name}")
    print(f"输入: {input_file}")
    print(f"输出: {output_file}")
    print(f"超时时间: {timeout}秒")
    
    start_time = time.time()
    
    try:
        # 运行脚本
        result = subprocess.run([
            'python', script_name, input_file, output_file
        ], capture_output=True, text=True, timeout=timeout, 
           cwd='/home/<USER>/trade/t0_backtest/intraday')
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ 脚本运行成功 (耗时: {execution_time:.1f}秒)")
            
            # 检查输出文件
            if os.path.exists(output_file):
                print("✅ 输出文件生成成功")
                
                # 读取并显示结果摘要
                if output_file.endswith('.csv'):
                    df = pd.read_csv(output_file)
                    print(f"✅ 输出记录数: {len(df)}")
                    print(f"✅ 输出列数: {len(df.columns)}")
                    
                    # 显示数值列的统计信息
                    numeric_cols = df.select_dtypes(include=[np.number]).columns
                    if len(numeric_cols) > 0:
                        print(f"✅ 数值列统计:")
                        for col in numeric_cols[:3]:  # 只显示前3列
                            print(f"  {col}: min={df[col].min():.2f}, max={df[col].max():.2f}, mean={df[col].mean():.2f}")
                
                elif output_file.endswith('.xlsx'):
                    df = pd.read_excel(output_file)
                    print(f"✅ 输出记录数: {len(df)}")
                    print(f"✅ 输出列数: {len(df.columns)}")
                
                return True, execution_time
            else:
                print("❌ 输出文件未生成")
                return False, execution_time
        else:
            print("❌ 脚本运行失败")
            print(f"错误信息: {result.stderr}")
            return False, execution_time
            
    except subprocess.TimeoutExpired:
        print(f"❌ 脚本运行超时 (>{timeout}秒)")
        return False, timeout
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False, 0

def main():
    """
    主测试函数
    """
    print("=== 完整数据集测试 ===")
    
    # 文件路径
    large_input_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/signal_merged.fea"
    sample_input_file = "/home/<USER>/trade/t0_backtest/intraday/results/large_sample.csv"
    results_dir = "/home/<USER>/trade/t0_backtest/intraday/results"
    
    # 确保结果目录存在
    Path(results_dir).mkdir(parents=True, exist_ok=True)
    
    # 第一步：加载和抽样数据
    df_sample = load_and_sample_data(large_input_file, sample_size=5000, output_file=sample_input_file)
    
    if df_sample is None:
        print("❌ 无法加载数据，测试终止")
        return False
    
    # 第二步：测试各个脚本
    test_cases = [
        {
            'script': 'daily_stats_analysis.py',
            'output': f'{results_dir}/large_daily_stats.csv',
            'description': '按日统计分析',
            'timeout': 300
        },
        {
            'script': 'symbol_stats_analysis.py',
            'output': f'{results_dir}/large_symbol_stats.csv',
            'description': '按标的统计分析',
            'timeout': 300
        },
        {
            'script': 'annual_stats_analysis.py',
            'output': f'{results_dir}/large_annual_stats.csv',
            'description': '按年统计分析',
            'timeout': 300
        },
        {
            'script': 'rolling_stats_analysis.py',
            'output': f'{results_dir}/large_rolling_stats.xlsx',
            'description': '滚动周期统计分析',
            'timeout': 600  # 滚动统计可能需要更长时间
        }
    ]
    
    # 执行测试
    test_results = []
    total_time = 0
    
    for test_case in test_cases:
        success, execution_time = test_script_on_large_data(
            test_case['script'],
            sample_input_file,
            test_case['output'],
            test_case['description'],
            test_case['timeout']
        )
        
        test_results.append({
            'test': test_case['description'],
            'success': success,
            'time': execution_time
        })
        total_time += execution_time
    
    # 测试总结
    print("\n=== 大数据集测试总结 ===")
    success_count = sum(1 for result in test_results if result['success'])
    total_count = len(test_results)
    
    print(f"总测试数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    print(f"总耗时: {total_time:.1f}秒")
    
    for result in test_results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['test']} (耗时: {result['time']:.1f}秒)")
    
    # 性能评估
    print("\n=== 性能评估 ===")
    sample_size = len(df_sample)
    print(f"抽样数据量: {sample_size:,} 条记录")
    
    if success_count > 0:
        avg_time_per_record = total_time / sample_size
        print(f"平均处理速度: {avg_time_per_record*1000:.2f} 毫秒/记录")
        
        # 估算完整数据集处理时间
        try:
            full_df = pd.read_feather(large_input_file)
            full_size = len(full_df)
            estimated_time = avg_time_per_record * full_size
            print(f"完整数据集大小: {full_size:,} 条记录")
            print(f"估算完整处理时间: {estimated_time/60:.1f} 分钟")
        except:
            print("无法估算完整数据集处理时间")
    
    # 检查生成的文件
    print("\n=== 生成的文件 ===")
    for test_case in test_cases:
        if os.path.exists(test_case['output']):
            file_size = os.path.getsize(test_case['output'])
            print(f"✅ {test_case['output']} ({file_size:,} bytes)")
        else:
            print(f"❌ {test_case['output']} (不存在)")
    
    if success_count == total_count:
        print("\n🎉 所有大数据集测试通过！脚本可以处理完整数据集。")
        print("💡 建议：可以在完整数据集上运行统计分析。")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个测试失败，建议优化后再处理完整数据集。")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
