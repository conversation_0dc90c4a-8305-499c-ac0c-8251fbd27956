
from ast import arg
from calendar import Calendar
from pdb import run
from re import L
from turtle import st
from joblib import Parallel, delayed
from pqdm.processes import pqdm
import os, sys
import pandas as pd
import datetime
import pickle
from pathlib import Path
import warnings

from zstandard import STRATEGY_DFAST

# notebook_dir = Path.cwd() 
# print(str(notebook_dir.parent))
# # sys.path.insert(0, str(notebook_dir.parent))

# print(sys.path)
from backtest import backtest as bt
from tca import tca
from tca import stk_tools
import test

cur_dir = os.path.dirname(os.path.abspath(__file__))
p_dir = os.path.join(cur_dir, '../../')
sys.path.insert(0, p_dir)
from data_utils.trading_calendar import Calendar
from misc.Readstockfile import update_xlsx_putdf

sys.path.remove(p_dir)

warnings.filterwarnings('ignore') 


def pickle_dump(data,pt):
    with open(pt, 'wb') as file:
        pickle.dump(data, file)

def pickle_load(pt):
    with open(pt, 'rb') as file:
        return pickle.load(file)
    
    
def run_test_inventory(date,pords):
    try:
        open_th=0.004
        ret=bt.run_by_date(date,open_th,pords,isbt=True,rt_raw_tca=True,filt_sig_by_time=True)
        # print(f'run {date} ret {ret}')
        return ret
    except Exception as e:
        print("run {} failed {}".format(date,e))
        return None
    
   
def run_test_download_trds(date,pords):
    try:
        open_th=0.004
        ret=bt.run_by_date_get_trds(date,open_th,pords,isbt=True,rt_raw_tca=True,filt_sig_by_time=True)
        # print(f'run {date} ret {ret}')
        return ret
    except Exception as e:
        print("run {} failed {}".format(date,e))
        return None
    
"""
## 回测数据格式
-  dict= {'********':[{'id': 'ccf48cd5-83a9-41cf-b0d6-dea305d42635',  'symbol': '000002',  'quantity': 36400,  'account_id': 'test_account_w3',  'start_time': datetime.datetime(2024, 1, 2, 9, 35),  'end_time': datetime.datetime(2024, 1, 2, 14, 57)}, {'id': 'fe52a5b4-c0d8-4097-b88e-1fc2fdd045e0',  'symbol': '000063',  'quantity': 33000,  'account_id': 'test_account_w3',  'start_time': datetime.datetime(2024, 1, 2, 9, 35),  'end_time': datetime.datetime(2024, 1, 2, 14, 57)},]}

"""



# demo1
# pords=bt.read_positions("/home/<USER>/py/stk_py/intraday/backtest/data/inventory/A500_rmtop20%_2024.csv",date,"sym","qty",account='test_account_w3',filt_type='csv')
# pords=bt.read_positions(file_1,date,"sym","qty",account='test_account_w3',filt_type='csv')

# demo2
# pords=bt.read_positions("/home/<USER>/py/stk_py/intraday/backtest/data/inventory/daiwenzheng_inventory_processed.csv",date,"symbol","qty",account='test_account_w3',filt_type='csv')
# pords=bt.read_positions(file_2,date,"symbol","qty",account='test_account_w3',filt_type='csv')



# demo3
# df=pd.read_csv(r"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/zz2000_pos_0940.csv")
def dump_rongshuhai_inventory_run_test(folder, test_name):
    df=pd.read_csv(file_3)
    df.rename(columns={"Unnamed: 0": "date"}, inplace=True)
    d={}
    for data in df.to_dict('records'):
        date=data['date'][:10].replace('-', '')
        # if date<"********" or date>"********":
        if date<"********" :
            continue
        st=datetime.datetime.strptime(data['date'], '%Y-%m-%d %H:%M:%S')
        
        print(date)
        l=[]
        for k,v in data.items():
            if k=='date':
                continue
            if v==0:
                continue
            po=bt.make_parentorder(k.split(".")[0],int(v),"test_account_w3",date,start_time=None) 
            l.append(po)
        d[date]=l

    stk_tools.pickle_dump(d, os.path.join(cur_dir, './', f"inventory/{folder}/{test_name}.pkl"))


    # datas=stk_tools.pickle_load(r"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/hs300_pos_0935.pkl")
    # datas=stk_tools.pickle_load(os.path.join(cur_dir, './', f"inventory/{rsh_name}.pkl"))
    datas = d
    args=[]
    for k,v in datas.items():
        args.append([k,pd.DataFrame(v)])
        
    print(args[-1])
    # args[0][1].to_csv(os.path.join(cur_dir, './', "inventory/sample_zz2000_pos_0940.csv"))
        
        
    results=pqdm(args,run_test_inventory,50,argument_type='args')

    l=[_ for _ in results if _ is not None]
    print(len(l))
    stk_tools.pickle_dump(l, os.path.join(cur_dir, './', f"results/{folder}/{test_name}.pkl"))

def dump_index_consti_run_test(folder, test_name):
    # test_name = 'cons_1000_30M'
    file = f'/home/<USER>/trade/{test_name}.xlsx'
    sheet_names = [
        '********',
        '20240201',    
        '20240301',    
        '20240401',    
        '20240501',    
        '20240601',    
        '20240701',    
        '20240801',    
        '20240901',    
        '20241001',    
        '20241101',    
        '20241201',
        '********',    
        '20250201',    
        '20250301',    
        '20250401',    
        '20250501',    
        '20250601',    
    ]
    sheet_datas = {sheet_name: pd.read_excel(file, sheet_name=sheet_name, dtype={'ticker':'str'})[['ticker', 'volume']] for sheet_name in sheet_names}
    
    trading_dates = [d.strftime('%Y%m%d') for d in Calendar.trading_dates('********', '********')]
    
    d = {}
    for date in trading_dates:
        for sheet_name in sheet_names:
            if date >= sheet_name:
                break
        # get data
        l = []
        for idx, row in sheet_datas[sheet_name].iterrows():
            po=bt.make_parentorder(row['ticker'], int(row['volume']), "test_account_w3", date, start_time=None) 
            l.append(po)
        d[date] = l

    stk_tools.pickle_dump(d, os.path.join(cur_dir, './', f"inventory/{folder}/{test_name}.pkl"))


    # datas=stk_tools.pickle_load(r"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/hs300_pos_0935.pkl")
    # datas=stk_tools.pickle_load(os.path.join(cur_dir, './', f"inventory/{rsh_name}.pkl"))
    datas = d
    args=[]
    for k,v in datas.items():
        args.append([k,pd.DataFrame(v)])
        
    print(args[-1])
    # args[0][1].to_csv(os.path.join(cur_dir, './', "inventory/sample_zz2000_pos_0940.csv"))
        
        
    results=pqdm(args,run_test_inventory,50,argument_type='args')

    l=[_ for _ in results if _ is not None]
    print(len(l))
    stk_tools.pickle_dump(l, os.path.join(cur_dir, './', f"results/{folder}/{test_name}.pkl"))


def load_data_run_test(folder, test_name, start_date, end_date):
    # pt=r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_hs300_pos_0935.pkl"
    pt = os.path.join(cur_dir, f"inventory/{folder}/{test_name}.pkl")
    datas = stk_tools.pickle_load(pt)
    
    args=[]
    for k, v in datas.items():
        if k < start_date or k > end_date:
            continue
        args.append([k, pd.DataFrame(v)])
    
    print(args[-1])
    results=pqdm(args, run_test_inventory, 50, argument_type='args')
    l = [_ for _ in results if _ is not None]
    stk_tools.pickle_dump(l, os.path.join(cur_dir, f"results/{folder}/{test_name}.pkl"))

    results=pqdm(args, run_test_download_trds, 40, argument_type='args')
    l = [_ for _ in results if _ is not None]
    stk_tools.pickle_dump(l, os.path.join(cur_dir, f"results/{folder}/trds_{test_name}.pkl"))
    
    

def save_trds_from_pkl(folder, test_name):
    # pt=r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_hs300_pos_0935.pkl"
    pt = os.path.join(cur_dir, './', f"results/{folder}/trds_{test_name}.pkl")
    datas = stk_tools.pickle_load(pt)
    
    if len(datas) == 0:
        return
    df = pd.concat(datas)
    df.to_csv(os.path.join(cur_dir, './', f"results/{folder}/trds_{test_name}.csv"), index=False, encoding='gbk')








# stats
# =========================================
def stats_inventory_run_test(folder, test_name, start_date=None, end_date=None):
    # pt=r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_hs300_pos_0935.pkl"
    pt = os.path.join(cur_dir, './', f"results/{folder}/{test_name}.pkl")
    perf_daily,perf_annually=bt.get_perf(pt, start_date, end_date)

    perf_daily.to_csv( os.path.join(cur_dir, './', f"results/{folder}/perf_daily_{test_name}.csv"),index=False, encoding='gbk')
    perf_annually.to_csv( os.path.join(cur_dir, './', f"results/{folder}/perf_annually_{test_name}.csv"),index=False, encoding='gbk')

    perf_symbol = bt.get_perf_by_sym(pt, start_date, end_date)
    perf_symbol.to_csv( os.path.join(cur_dir, './', f"results/{folder}/perf_symbol_{test_name}.csv"),index=False, encoding='gbk')



    # perf_daily.to_csv( r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_perf_daily_hs300_pos_0935.csv",index=False)
    # perf_annually.to_csv(r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_perf_annually_hs300_pos_0935.csv",index=False)

def stats_shareA_roll_20_test(folder, test_name, start_date, end_date):
    
    pt = os.path.join(cur_dir, './', f"results/{folder}/{test_name}.pkl")
    
    result_file = os.path.join(cur_dir, './', f"results/{folder}/perf_symbol_roll_20_{test_name}.xlsx")
    trade_days = [day.strftime('%Y%m%d') for day in Calendar.trading_dates(start_date, end_date)]
    last_trade_days_by_week = Calendar.get_last_trading_days_of_weeks(start_date, end_date)
    
    n = 20
    with open(pt, 'rb') as file:
        datas=pickle.load(file)
        
    if len(datas) == 0:
        return
    df=pd.concat(datas)
    
    args = []
    
    
    for i in range(3, len(last_trade_days_by_week)):
        loop_end = last_trade_days_by_week[i]
        loop_start = trade_days[max(trade_days.index(loop_end) - (n-1), 0)]
        
        args.append([df, loop_start, loop_end, n])
       
    results = pqdm(args, bt.get_perf_by_symbol_window, n_jobs=20, argument_type='args')
        
    for res in results:
        if res.shape[0] > 0:
            try:
                update_xlsx_putdf(res, result_file, sheet_name=res['日期'].iloc[0], index=False)
            except Exception as e:
                print(e)
                res.to_excel(result_file, sheet_name=res['日期'].iloc[0], index=False)


if __name__=="__main__":
    # date = '20250609'
    cur_dir = os.path.dirname(os.path.abspath(__file__))
    # file_1 = os.path.join(cur_dir, './', "inventory/A500_rmtop20%_2024.csv")
    # file_2 = os.path.join(cur_dir, './', "inventory/daiwenzheng_inventory_processed.csv")

    # rsh_name = 'hs300_0930_5m'
    # rsh_name = 'hs300_0930_10m'
    # rsh_name = 'zz1000_0930_5m'
    # rsh_name = 'zz1000_0930_10m'
    # rsh_name = 'zz2000_0930_5m'
    # rsh_name = 'zz2000_0930_5m_集中'
    # rsh_name = 'zz2000_0930_10m'
    # rsh_name = 'zz2000_0930_10m_集中'


    # file_3 = os.path.join(cur_dir, './', f"inventory/{rsh_name}.csv")
    
    

    # dump_rongshuhai_inventory_run_test(folder='cons', test_name='cons_1000_30M')
    # dump_index_consti_run_test(folder='cons_2425', test_name='cons_1000_30M')
    
    folder    = 'Ashare'
    test_name = 'Ashare_100000_w3.5'
    
    # folder    = 'weipan'
    # test_name = 'weipan_100000_w3'
    
    if not os.path.exists(os.path.join(cur_dir, 'results', folder)):
        os.mkdir(os.path.join(cur_dir, 'results', folder))

    if not os.path.exists(os.path.join(cur_dir, 'inventory', folder)):
        os.mkdir(os.path.join(cur_dir, 'inventory', folder))
        
    start_date = '********'
    end_date   = '20250625'
    # load_data_run_test(folder, test_name, start_date, end_date)
    # stats_shareA_roll_20_test(folder=folder, 
    #                           test_name=test_name, 
    #                           start_date=start_date, 
    #                           end_date=end_date)
    
    stats_inventory_run_test(folder=folder, 
                              test_name=test_name,
                              start_date=start_date,
                              end_date=end_date
    )
    
    # save_trds_from_pkl(folder, test_name)