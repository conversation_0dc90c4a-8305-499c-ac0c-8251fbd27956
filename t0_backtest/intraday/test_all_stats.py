#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合测试脚本
测试所有统计分析脚本的正确性
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import subprocess

def test_script(script_name, input_file, output_file, description):
    """
    测试单个脚本
    """
    print(f"\n=== 测试 {description} ===")
    print(f"脚本: {script_name}")
    print(f"输入: {input_file}")
    print(f"输出: {output_file}")
    
    try:
        # 运行脚本
        result = subprocess.run([
            'python', script_name, input_file, output_file
        ], capture_output=True, text=True, cwd='/home/<USER>/trade/t0_backtest/intraday')
        
        if result.returncode == 0:
            print("✅ 脚本运行成功")
            
            # 检查输出文件是否存在
            if os.path.exists(output_file):
                print("✅ 输出文件生成成功")
                
                # 读取并显示结果摘要
                if output_file.endswith('.csv'):
                    df = pd.read_csv(output_file)
                    print(f"✅ 输出记录数: {len(df)}")
                    print(f"✅ 输出列数: {len(df.columns)}")
                    print(f"✅ 列名: {list(df.columns)}")
                elif output_file.endswith('.xlsx'):
                    df = pd.read_excel(output_file)
                    print(f"✅ 输出记录数: {len(df)}")
                    print(f"✅ 输出列数: {len(df.columns)}")
                    print(f"✅ 列名: {list(df.columns)}")
                
                return True
            else:
                print("❌ 输出文件未生成")
                return False
        else:
            print("❌ 脚本运行失败")
            print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def compare_with_reference(output_file, reference_file, description):
    """
    与参考结果进行对比
    """
    print(f"\n=== 对比 {description} ===")
    
    try:
        if not os.path.exists(reference_file):
            print(f"⚠️  参考文件不存在: {reference_file}")
            return False
        
        if not os.path.exists(output_file):
            print(f"❌ 输出文件不存在: {output_file}")
            return False
        
        # 读取文件
        if output_file.endswith('.csv'):
            df_output = pd.read_csv(output_file)
            df_reference = pd.read_csv(reference_file)
        elif output_file.endswith('.xlsx'):
            df_output = pd.read_excel(output_file)
            df_reference = pd.read_excel(reference_file)
        else:
            print("❌ 不支持的文件格式")
            return False
        
        print(f"输出文件记录数: {len(df_output)}")
        print(f"参考文件记录数: {len(df_reference)}")
        
        # 比较列名
        output_cols = set(df_output.columns)
        reference_cols = set(df_reference.columns)
        
        common_cols = output_cols & reference_cols
        missing_cols = reference_cols - output_cols
        extra_cols = output_cols - reference_cols
        
        print(f"✅ 共同列数: {len(common_cols)}")
        if missing_cols:
            print(f"⚠️  缺失列: {missing_cols}")
        if extra_cols:
            print(f"⚠️  额外列: {extra_cols}")
        
        # 比较数值范围（如果有共同的数值列）
        numeric_cols = df_output.select_dtypes(include=[np.number]).columns
        common_numeric_cols = [col for col in numeric_cols if col in df_reference.columns]
        
        if common_numeric_cols:
            print(f"数值列对比:")
            for col in common_numeric_cols[:5]:  # 只显示前5列
                if col in df_output.columns and col in df_reference.columns:
                    output_range = f"[{df_output[col].min():.2f}, {df_output[col].max():.2f}]"
                    reference_range = f"[{df_reference[col].min():.2f}, {df_reference[col].max():.2f}]"
                    print(f"  {col}: 输出{output_range} vs 参考{reference_range}")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比过程中出错: {e}")
        return False

def main():
    """
    主测试函数
    """
    print("=== 回测数据统计分析脚本综合测试 ===")
    
    # 测试参数
    input_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/sample2.csv"
    results_dir = "/home/<USER>/trade/t0_backtest/intraday/results"
    reference_dir = "/home/<USER>/trade/t0_backtest/intraday/results/weipan"
    
    # 确保结果目录存在
    Path(results_dir).mkdir(parents=True, exist_ok=True)
    
    # 测试用例
    test_cases = [
        {
            'script': 'daily_stats_analysis.py',
            'output': f'{results_dir}/test_daily_stats.csv',
            'reference': f'{reference_dir}/perf_daily_weipan_100000_w3.csv',
            'description': '按日统计分析'
        },
        {
            'script': 'symbol_stats_analysis.py',
            'output': f'{results_dir}/test_symbol_stats.csv',
            'reference': f'{reference_dir}/perf_symbol_weipan_100000_w3.csv',
            'description': '按标的统计分析'
        },
        {
            'script': 'annual_stats_analysis.py',
            'output': f'{results_dir}/test_annual_stats.csv',
            'reference': f'{reference_dir}/perf_annually_weipan_100000_w3.csv',
            'description': '按年统计分析'
        },
        {
            'script': 'rolling_stats_analysis.py',
            'output': f'{results_dir}/test_rolling_stats.xlsx',
            'reference': f'{reference_dir}/perf_symbol_roll_20_weipan_100000_w3.xlsx',
            'description': '滚动周期统计分析'
        }
    ]
    
    # 执行测试
    test_results = []
    
    for test_case in test_cases:
        success = test_script(
            test_case['script'],
            input_file,
            test_case['output'],
            test_case['description']
        )
        test_results.append({
            'test': test_case['description'],
            'success': success
        })
        
        # 与参考结果对比
        if success:
            compare_with_reference(
                test_case['output'],
                test_case['reference'],
                test_case['description']
            )
    
    # 测试总结
    print("\n=== 测试总结 ===")
    success_count = sum(1 for result in test_results if result['success'])
    total_count = len(test_results)
    
    print(f"总测试数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    for result in test_results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['test']}")
    
    # 检查生成的文件
    print("\n=== 生成的文件 ===")
    for test_case in test_cases:
        if os.path.exists(test_case['output']):
            file_size = os.path.getsize(test_case['output'])
            print(f"✅ {test_case['output']} ({file_size} bytes)")
        else:
            print(f"❌ {test_case['output']} (不存在)")
    
    if success_count == total_count:
        print("\n🎉 所有测试通过！脚本可以用于完整数据集处理。")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个测试失败，需要修复后再处理完整数据集。")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
