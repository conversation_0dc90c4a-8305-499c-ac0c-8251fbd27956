#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成分股倒推算法
根据最新成分股和历史调整记录，倒推计算历史每个交易日的成分股列表
"""

import pandas as pd
import numpy as np
import pickle
from datetime import datetime, timedelta
from collections import defaultdict
import os
import sys

# 添加路径
cur_dir = os.path.dirname(os.path.abspath(__file__))
p_dir = os.path.join(cur_dir, '../../')
sys.path.insert(0, p_dir)
from data_utils.trading_calendar import Calendar


def load_data():
    """加载预处理后的数据"""
    print("加载数据...")
    
    # 加载交易日历
    with open('/tmp/trading_dates.pkl', 'rb') as f:
        trading_dates = pickle.load(f)
    
    # 加载当前成分股
    with open('/tmp/current_constituents.pkl', 'rb') as f:
        current_constituents = pickle.load(f)
    
    # 加载调整记录
    with open('/tmp/changes_records.pkl', 'rb') as f:
        changes_records = pickle.load(f)
    
    return trading_dates, current_constituents, changes_records


def backtrack_constituents(trading_dates, current_constituents, changes_records):
    """
    倒推成分股算法
    
    Args:
        trading_dates: 交易日列表 (格式: ['20240102', ...])
        current_constituents: 当前成分股DataFrame (列: 代码, 简称, 权重)
        changes_records: 调整记录DataFrame (列: 日期_str, 代码, 简称, 纳入/剔除)
    
    Returns:
        dict: {日期: 成分股列表} 的字典
    """
    print("开始倒推成分股...")
    
    # 初始化结果字典
    constituents_by_date = {}
    
    # 当前成分股集合（使用代码作为标识）
    current_stocks = set(current_constituents['代码'].tolist())
    print(f"初始成分股数量: {len(current_stocks)}")
    
    # 获取所有调整日期，按降序排列
    adjustment_dates = sorted(changes_records['日期_str'].unique(), reverse=True)
    print(f"调整日期: {adjustment_dates}")
    
    # 按交易日倒序处理
    trading_dates_desc = sorted(trading_dates, reverse=True)
    
    # 当前处理的调整记录索引
    adj_idx = 0
    
    for trade_date in trading_dates_desc:
        # 检查是否有调整记录需要处理
        while adj_idx < len(adjustment_dates) and trade_date <= adjustment_dates[adj_idx]:
            adj_date = adjustment_dates[adj_idx]
            print(f"处理调整日期: {adj_date}")
            
            # 获取该日期的所有调整记录
            day_changes = changes_records[changes_records['日期_str'] == adj_date]
            
            # 反向处理调整记录
            for _, record in day_changes.iterrows():
                stock_code = record['代码']
                action = record['纳入/剔除']
                stock_name = record['简称']
                
                if action == '纳入':
                    # 如果记录显示"纳入"，则从当前成分股中移除（反向操作）
                    if stock_code in current_stocks:
                        current_stocks.remove(stock_code)
                        print(f"  移除: {stock_code} {stock_name}")
                elif action == '剔除':
                    # 如果记录显示"剔除"，则加入当前成分股（反向操作）
                    current_stocks.add(stock_code)
                    print(f"  加入: {stock_code} {stock_name}")
            
            print(f"  调整后成分股数量: {len(current_stocks)}")
            adj_idx += 1
        
        # 为当前交易日保存成分股快照
        constituents_by_date[trade_date] = current_stocks.copy()
        
        # 每处理100个交易日输出一次进度
        if len(constituents_by_date) % 100 == 0:
            print(f"已处理 {len(constituents_by_date)} 个交易日")
    
    print(f"倒推完成，共处理 {len(constituents_by_date)} 个交易日")
    return constituents_by_date


def add_stock_details(constituents_by_date, current_constituents, changes_records):
    """
    为成分股添加详细信息（简称等）
    
    Args:
        constituents_by_date: {日期: 成分股代码集合}
        current_constituents: 当前成分股详细信息
        changes_records: 调整记录
    
    Returns:
        dict: {日期: 成分股详细信息列表}
    """
    print("添加股票详细信息...")
    
    # 构建代码到简称的映射
    code_to_name = {}
    
    # 从当前成分股获取映射
    for _, row in current_constituents.iterrows():
        code_to_name[row['代码']] = row['简称']
    
    # 从调整记录获取映射
    for _, row in changes_records.iterrows():
        code_to_name[row['代码']] = row['简称']
    
    # 为每个日期的成分股添加详细信息
    detailed_constituents = {}
    
    for date, stock_codes in constituents_by_date.items():
        stock_list = []
        for code in stock_codes:
            stock_info = {
                '代码': code,
                '简称': code_to_name.get(code, '未知')
            }
            stock_list.append(stock_info)
        
        # 按代码排序
        stock_list.sort(key=lambda x: x['代码'])
        detailed_constituents[date] = stock_list
    
    return detailed_constituents


def main():
    """主函数"""
    try:
        # 加载数据
        trading_dates, current_constituents, changes_records = load_data()
        
        # 倒推成分股
        constituents_by_date = backtrack_constituents(
            trading_dates, current_constituents, changes_records
        )
        
        # 添加详细信息
        detailed_constituents = add_stock_details(
            constituents_by_date, current_constituents, changes_records
        )
        
        # 保存结果
        output_file = '/home/<USER>/trade/t0_backtest/intraday/constituents_by_date.pkl'
        with open(output_file, 'wb') as f:
            pickle.dump(detailed_constituents, f)
        
        print(f"\n结果已保存到: {output_file}")
        
        # 输出统计信息
        print("\n=== 统计信息 ===")
        print(f"总交易日数: {len(detailed_constituents)}")
        
        # 统计每日成分股数量
        daily_counts = {date: len(stocks) for date, stocks in detailed_constituents.items()}
        print(f"成分股数量范围: {min(daily_counts.values())} - {max(daily_counts.values())}")
        
        # 显示几个关键日期的成分股数量
        key_dates = ['20240102', '20240630', '20241231', '20250625']
        for date in key_dates:
            if date in detailed_constituents:
                count = len(detailed_constituents[date])
                print(f"{date}: {count}只成分股")
        
        return detailed_constituents
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    result = main()
