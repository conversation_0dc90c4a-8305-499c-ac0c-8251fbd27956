from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import datetime

key="k3NgFMJMRPJhEkHy"
iv="cs3ltog6iL0M7F3c"
def encrypt(data,key, iv):
    cipher = AES.new(key.encode('utf-8'), AES.MODE_CBC, iv.encode('utf-8'))
    return (cipher.encrypt(pad(data, AES.block_size)))

def decrypt(data,key,iv):
    cipher = AES.new(key.encode('utf-8'), AES.MODE_CBC, iv.encode('utf-8'))
    return cipher.decrypt(data)

def encrypt_file(filein,fileout,key,iv):
    with open(filein,'rb') as fin:
        data=fin.read()
    with open(fileout,'wb') as fout:
        fout.write(encrypt(data,key,iv))

def decrypt_file(filein,fileout,key,iv):
    with open(filein,'rb') as fin:
        data=fin.read()
    with open(fileout,'wb') as fout:
        fout.write(decrypt(data,key,iv))

def read_from_encrypt_file(filein,key,iv):
    with open(filein,'rb') as fin:
        data=fin.read()
    return decrypt(data,key,iv)
if __name__=="__main__":
    key="k3NgFMJMRPJhEkHy"
    iv="cs3ltog6iL0M7F3c"
    # now=datetime.datetime.now()
    encrypt_file("/data/pfile001/users/allen/data/algo_trading_daily_data/20240424.json","/data/pfile001/users/allen/data/algo_trading_daily_data_encrypted/20240424.dat",key,iv)
    # print(datetime.datetime.now()-now)
    # data=read_from_encrypt_file("/data/pfile001/users/allen/data/algo_trading_daily_data_encrypted/20240424.dat",key,iv)
    # print(data)