import pandas as pd
from pathlib import Path
from joblib import <PERSON>llel, delayed
from pqdm.processes import pqdm

def dump_file(pt,df, compression='zstd', compression_level=10, reset_index=True):
    if reset_index:
        df=df.reset_index(drop=True)
    with open(pt,'wb') as f:
            df.to_feather(f, compression=compression, compression_level=compression_level)

def batch_dump(datas, compression='zstd', compression_level=10, reset_index=True,n_jobs=20):
        args = [(pt, df, compression, compression_level, reset_index) for pt, df in datas]
        pqdm(args, dump_file, n_jobs=n_jobs, argument_type='args')


def dump_file2(pt, df, compression='lz4', compression_level=None, reset_index=False, use_threads=True):
    """
    使用 Feather 格式存储 DataFrame，优化读写速度
    
    参数:
    - pt: 文件路径
    - df: 要存储的 DataFrame
    - compression: 压缩算法 ('lz4', 'zstd', 'uncompressed')
    - compression_level: 压缩级别 (None 表示使用默认值)
    - reset_index: 是否重置索引
    """
    if reset_index:
        df = df.reset_index(drop=True)
    
    if compression == 'lz4':
        compression_level = None  
    elif compression == 'zstd':
        compression_level = compression_level or 3  
    
    df.to_feather(
        pt,
        compression=compression,
        compression_level=compression_level,
        storage_options=None 
    )