{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def get_calendar():\n", "    return pd.read_feather(r\"/data/shared-data/xuj/datas/Trade_Calendar.fea\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["df=get_calendar()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CALENDAR_DATE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20200102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20200103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>20200106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20200107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20200108</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1695</th>\n", "      <td>20261225</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1696</th>\n", "      <td>20261228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1697</th>\n", "      <td>20261229</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1698</th>\n", "      <td>20261230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1699</th>\n", "      <td>20261231</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1700 rows × 1 columns</p>\n", "</div>"], "text/plain": ["     CALENDAR_DATE\n", "0         20200102\n", "1         20200103\n", "2         20200106\n", "3         20200107\n", "4         20200108\n", "...            ...\n", "1695      20261225\n", "1696      20261228\n", "1697      20261229\n", "1698      20261230\n", "1699      20261231\n", "\n", "[1700 rows x 1 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["date=\"20210101\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["def is_trading_day(date):\n", "    df=get_calendar()\n", "    return date in df['CALENDAR_DATE']\n", "\n", "def get_next_trading_day(date):\n", "    df=get_calendar()\n", "    return df[df['CALENDAR_DATE']>date]['CALENDAR_DATE'].values[0]\n", "\n", "def get_next_trading_day(date):\n", "    df=get_calendar()\n", "    return df[df['CALENDAR_DATE']<date]['CALENDAR_DATE'].values[0]\n", "\n", "def get_trading_dates(sd,ed):\n", "    df=get_calendar()\n", "    return list(df[(df['CALENDAR_DATE']>=sd)&(df['CALENDAR_DATE']<=ed)]['CALENDAR_DATE'].values)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["['20240102',\n", " '20240103',\n", " '20240104',\n", " '20240105',\n", " '20240108',\n", " '20240109',\n", " '20240110',\n", " '20240111',\n", " '20240112',\n", " '20240115',\n", " '20240116',\n", " '20240117',\n", " '20240118',\n", " '20240119',\n", " '20240122',\n", " '20240123',\n", " '20240124',\n", " '20240125',\n", " '20240126',\n", " '20240129',\n", " '20240130',\n", " '20240131',\n", " '20240408',\n", " '20240409',\n", " '20240219',\n", " '20240220',\n", " '20240221',\n", " '20240222',\n", " '20240223',\n", " '20240226',\n", " '20240227',\n", " '20240228',\n", " '20240229',\n", " '20240708',\n", " '20240709',\n", " '20240801',\n", " '20240802',\n", " '20240805',\n", " '20240806',\n", " '20240311',\n", " '20240312',\n", " '20240313',\n", " '20240314',\n", " '20240315',\n", " '20240318',\n", " '20240319',\n", " '20240320',\n", " '20240321',\n", " '20240322',\n", " '20240325',\n", " '20240326',\n", " '20240327',\n", " '20240328',\n", " '20240329',\n", " '20240401',\n", " '20240402',\n", " '20240403',\n", " '20240410',\n", " '20240411',\n", " '20240412',\n", " '20240415',\n", " '20240416',\n", " '20240417',\n", " '20240418',\n", " '20240419',\n", " '20240422',\n", " '20240423',\n", " '20240424',\n", " '20240425',\n", " '20240426',\n", " '20240429',\n", " '20240430',\n", " '20240506',\n", " '20240507',\n", " '20240508',\n", " '20240509',\n", " '20240510',\n", " '20240513',\n", " '20240514',\n", " '20240515',\n", " '20240516',\n", " '20240517',\n", " '20240520',\n", " '20240521',\n", " '20240522',\n", " '20240523',\n", " '20240524',\n", " '20240527',\n", " '20240528',\n", " '20240529',\n", " '20240530',\n", " '20240531',\n", " '20240603',\n", " '20240604',\n", " '20240605',\n", " '20240606',\n", " '20240607',\n", " '20240611',\n", " '20240612',\n", " '20240613',\n", " '20240614',\n", " '20240617',\n", " '20240618',\n", " '20240619',\n", " '20240620',\n", " '20240621',\n", " '20240624',\n", " '20240625',\n", " '20240626',\n", " '20240627',\n", " '20240628',\n", " '20240701',\n", " '20240702',\n", " '20240703',\n", " '20240704',\n", " '20240705',\n", " '20240710',\n", " '20240711',\n", " '20240712',\n", " '20240715',\n", " '20240716',\n", " '20240717',\n", " '20240718',\n", " '20240719',\n", " '20240722',\n", " '20240723',\n", " '20240724',\n", " '20240725',\n", " '20240726',\n", " '20240729',\n", " '20240730',\n", " '20240731',\n", " '20240807',\n", " '20240808',\n", " '20240809',\n", " '20240812',\n", " '20240813',\n", " '20240814',\n", " '20240815',\n", " '20240816',\n", " '20240819',\n", " '20240820',\n", " '20240821',\n", " '20240822',\n", " '20240823',\n", " '20240826',\n", " '20240827',\n", " '20240828',\n", " '20240829',\n", " '20240830',\n", " '20240902',\n", " '20240903',\n", " '20240904',\n", " '20240905',\n", " '20240906',\n", " '20240909',\n", " '20240910',\n", " '20240911',\n", " '20240912',\n", " '20240913',\n", " '20240918',\n", " '20240919',\n", " '20240920',\n", " '20240923',\n", " '20240924',\n", " '20240925',\n", " '20240926',\n", " '20240927',\n", " '20240930',\n", " '20241008',\n", " '20241009',\n", " '20241010',\n", " '20241011',\n", " '20241014',\n", " '20241015',\n", " '20241016',\n", " '20241017',\n", " '20241018',\n", " '20241021',\n", " '20241022',\n", " '20241023',\n", " '20241024',\n", " '20241025',\n", " '20241028',\n", " '20241029',\n", " '20241030',\n", " '20241031',\n", " '20241101',\n", " '20241104',\n", " '20241105',\n", " '20241106',\n", " '20241107',\n", " '20241108',\n", " '20241111',\n", " '20241112',\n", " '20241113',\n", " '20241114',\n", " '20241115',\n", " '20241118',\n", " '20241119',\n", " '20241120',\n", " '20241121',\n", " '20241122',\n", " '20241125',\n", " '20241126',\n", " '20241127',\n", " '20241128',\n", " '20241129',\n", " '20241202',\n", " '20241203',\n", " '20241204',\n", " '20241205',\n", " '20241206',\n", " '20241209',\n", " '20241210',\n", " '20241211',\n", " '20241212',\n", " '20241213',\n", " '20241216',\n", " '20241217',\n", " '20241218',\n", " '20241219',\n", " '20241220',\n", " '20241223',\n", " '20241224',\n", " '20241225',\n", " '20241226',\n", " '20241227',\n", " '20241230',\n", " '20241231',\n", " '20240201',\n", " '20240202',\n", " '20240205',\n", " '20240206',\n", " '20240207',\n", " '20240208',\n", " '20240301',\n", " '20240304',\n", " '20240305',\n", " '20240306',\n", " '20240307',\n", " '20240308',\n", " '20250102',\n", " '20250103',\n", " '20250106',\n", " '20250107',\n", " '20250108',\n", " '20250109',\n", " '20250110',\n", " '20250113',\n", " '20250114',\n", " '20250115',\n", " '20250116',\n", " '20250117',\n", " '20250120',\n", " '20250121',\n", " '20250122',\n", " '20250123',\n", " '20250124',\n", " '20250127',\n", " '20250205',\n", " '20250206',\n", " '20250207',\n", " '20250210',\n", " '20250211',\n", " '20250212',\n", " '20250213',\n", " '20250214',\n", " '20250217',\n", " '20250218',\n", " '20250219',\n", " '20250220',\n", " '20250221',\n", " '20250224',\n", " '20250225',\n", " '20250226',\n", " '20250227',\n", " '20250228',\n", " '20250303',\n", " '20250304',\n", " '20250305',\n", " '20250306',\n", " '20250307',\n", " '20250310',\n", " '20250311',\n", " '20250312',\n", " '20250313',\n", " '20250314',\n", " '20250317',\n", " '20250318',\n", " '20250319',\n", " '20250320',\n", " '20250321',\n", " '20250324',\n", " '20250325',\n", " '20250326',\n", " '20250327',\n", " '20250328',\n", " '20250331',\n", " '20250401',\n", " '20250402',\n", " '20250403',\n", " '20250407',\n", " '20250408',\n", " '20250409',\n", " '20250410',\n", " '20250411',\n", " '20250414',\n", " '20250415',\n", " '20250416',\n", " '20250417',\n", " '20250418',\n", " '20250421',\n", " '20250422',\n", " '20250423',\n", " '20250424',\n", " '20250425',\n", " '20250428',\n", " '20250429',\n", " '20250430']"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["get_trading_dates(\"20240101\",\"20250501\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from utils import trading_calendar"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from utils import mysql"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["/data/shared-data/xuj/datas/Trade_Calendar.fea"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["tc.sort_values('CALENDAR_DATE').reset_index(drop=True).to_feather(r\"/data/shared-data/xuj/datas/Trade_Calendar.fea\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def read_trade_calendar(start_date,end_date):\n", "    return mysql.query(mysql.get_datayes_db_connection(),\"select * from md_trade_cal where CALENDAR_DATE>={} and CALENDAR_DATE<={} and EXCHANGE_CD='XSHG'\".format(start_date,end_date))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["select * from md_trade_cal where CALENDAR_DATE>=20200101 and CALENDAR_DATE<=20290501 and EXCHANGE_CD='XSHG'\n"]}], "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["datas=datas[datas['IS_OPEN']==1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datas[['CALENDAR_DATE']].reset_index(drop=True).to_feather(r\"/data/shared-data/xuj/datas/Trade_Calendar.fea\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["'20241009'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["trading_calendar.get_trading_day_before_date(\"20250101\",60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 2}