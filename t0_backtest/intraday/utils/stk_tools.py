import paramiko
from scp import SCPClient
import datetime
import pandas as pd
import pickle
import numba as nb
from pathlib import Path
import chardet
def is_ashare_stk(sym):
    sym=fmt_stk_code(sym)
    prefix=sym[:2]
    if prefix in ['00','30','60','68']:
        return True
    return False    

def is_ashare_stk_with_exchangecd(sym,exchange):
    sym=fmt_stk_code(sym)
    prefix=sym[:1]
    if prefix=='6' and exchange=='XSHG':
        return True
    if prefix in ['0','3'] and exchange=='XSHE':
        return True     
    return False    

def get_exchangecd(sym):
    sym=fmt_stk_code(sym)
    if sym[:1]=='6':
        return "XSHG"
    else:
        return "XSHE"    



def fmt_stk_code(code):
    return str(int(code)).zfill(6)    


def createSSHClient(server, port, user,password=None):
    client = paramiko.SSHClient()
    client.load_system_host_keys()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    if password  is None: 
        client.connect(server,port,user)
    else:
        client.connect(server, port, user, password)    
    return client    

def createSCPClinet(server,port,user,password=None):
    cli=createSSHClient(server,port,user,password=None)
    cli = SCPClient(cli.get_transport())
    return cli


def today_str():
    return datetime.datetime.now().strftime("%Y%m%d")

#获取上周一，周五日期
def get_last_week_workdays(date=None):
    if date:
        today=datetime.datetime.strptime(str(date),"%Y%m%d")
    else:
        today=datetime.datetime.today()
    end_time=today- datetime.timedelta(days=today.isoweekday())-datetime.timedelta(days=2)
    start_time=end_time-datetime.timedelta(days=4)
    return start_time,end_time


def str_to_date(s):
    return pd.to_datetime(s).date()

def str_to_datetime(s):
    return pd.to_datetime(s)


def get_lotsize(sym):
    return 200 if sym[:3]=='688' else 100


def pickle_dump(data,pt):
    with open(pt, 'wb') as file:
        pickle.dump(data, file)

def pickle_load(pt):
    with open(pt, 'rb') as file:
        return pickle.load(file)


def read_dataframes_in_dir(dir_path,read_func,ignore_files=[]):
    dir=Path(dir_path)
    # all_data=pd.DataFrame()
    l=[]
    for file_path in dir.iterdir():
        if file_path.is_file() and file_path.name not in ignore_files:
            print(file_path)
            # 读取CSV文件并将数据合并到all_data中
            data = read_func(file_path)
            data['date']=file_path.name.split('.')[0]
            # all_data = pd.concat([all_data, data], ignore_index=True)
            l.append(data)
    all_data=pd.concat(l,ignore_index=True)
    return all_data

def calc_cost(avg_px,bm_px,side):
    return  side*(avg_px/bm_px -1 )*10000

def get_file_encoding(pt):
    with open(pt, 'rb') as f:
        result = chardet.detect(f.read())
    encoding = result['encoding']
    return encoding

if __name__=="__main__":
    # print(get_last_week_workdays("20230309"))
    n=datetime.datetime.now()
    df=read_dataframes_in_dir("/data/pfile001/users/allen/data/algo_basic_data",pickle_load,['2022.fea'])
    print(datetime.datetime.now()-n)
    df.reset_index(drop=True).to_feather("/data/pfile001/users/allen/data/algo_basic_data/2022.fea",compression='zstd', compression_level=10)