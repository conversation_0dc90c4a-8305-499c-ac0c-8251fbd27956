import pandas as pd
from utils import mysql

def get_calendar():
    return pd.read_feather(r"/data/shared-data/xuj/datas/Trade_Calendar.fea")
    

def is_trading_day(date):
    df=get_calendar()
    return date in df['CALENDAR_DATE'].values

def get_next_trading_day(date):
    df=get_calendar()
    return df[df['CALENDAR_DATE']>date]['CALENDAR_DATE'].values[0]

def get_prev_trade_date(date):
    df=get_calendar()
    return df[df['CALENDAR_DATE']<date]['CALENDAR_DATE'].values[-1]

def get_trading_dates(sd,ed):
    df=get_calendar()
    return list(df[(df['CALENDAR_DATE']>=sd)&(df['CALENDAR_DATE']<=ed)]['CALENDAR_DATE'].values)

def get_trading_days_before_date(date,n):
    df=get_calendar()
    return df[df['CALENDAR_DATE']<date]['CALENDAR_DATE'].values[-n:]

#返回date之前n天的交易日
def get_trading_day_before_date(date,n):
    df=get_calendar()
    return df[df['CALENDAR_DATE']<date]['CALENDAR_DATE'].values[-n]

def read_trade_calendar_from_mysql(start_date,end_date):
    return mysql.query(mysql.get_datayes_db_connection(),"select * from md_trade_cal where CALENDAR_DATE>={} and CALENDAR_DATE<={} and EXCHANGE_CD='XSHG'".format(start_date,end_date))

def gen_file():
    datas=read_trade_calendar_from_mysql("20200101","20290501")
    datas['CALENDAR_DATE']=datas['CALENDAR_DATE'].apply(lambda x: x.strftime('%Y%m%d'))
    datas=datas[['CALENDAR_DATE']]
    datas=datas.sort_values('CALENDAR_DATE')
    datas.reset_index(drop=True).to_feather(r"/data/shared-data/xuj/datas/Trade_Calendar.fea")