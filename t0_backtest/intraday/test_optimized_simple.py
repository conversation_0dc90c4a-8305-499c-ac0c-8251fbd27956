#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试优化版本程序
"""

import pandas as pd
import numpy as np
import time
import sys
import os

def create_small_test_data():
    """
    创建小规模测试数据
    """
    print("创建测试数据...")
    
    # 创建测试数据
    np.random.seed(42)
    test_data = []
    
    # 只测试10个标的，10天数据
    for symbol in range(1, 11):
        for day in range(1, 11):
            date_str = f'2022010{day:01d}' if day < 10 else f'202201{day:02d}'
            
            # 每个标的每天只有1条记录
            record = {
                'code': symbol,
                'date': date_str,
                'max_amt': 100000,
                'round_num': np.random.randint(1, 5),
                'long_round_num': np.random.randint(0, 3),
                'short_round_num': np.random.randint(0, 3),
                'trade_num': np.random.randint(1, 10),
                'long_open_vol': np.random.uniform(0, 1000),
                'long_open_amt': np.random.uniform(0, 50000),
                'short_open_vol': np.random.uniform(0, 1000),
                'short_open_amt': np.random.uniform(0, 50000),
                'long_close_vol': np.random.uniform(0, 1000),
                'long_close_amt': np.random.uniform(0, 50000),
                'short_close_vol': np.random.uniform(0, 1000),
                'short_close_amt': np.random.uniform(0, 50000),
                'profit': np.random.uniform(-1000, 1000),
                'fee': np.random.uniform(0, 100),
                'profit_long': np.random.uniform(-500, 500),
                'fee_long': np.random.uniform(0, 50),
                'profit_short': np.random.uniform(-500, 500),
                'fee_short': np.random.uniform(0, 50),
                'amt_open': np.random.uniform(10000, 90000),
                'vol_open': np.random.uniform(100, 900),
                'position': 100000
            }
            
            # 计算费后盈利
            record['profit_ac'] = record['profit'] - record['fee']
            record['profit_ac_long'] = record['profit_long'] - record['fee_long']
            record['profit_ac_short'] = record['profit_short'] - record['fee_short']
            
            test_data.append(record)
    
    test_df = pd.DataFrame(test_data)
    
    # 保存测试数据
    test_file = "/tmp/small_test_data.csv"
    test_df.to_csv(test_file, index=False)
    
    print(f"生成测试数据: {len(test_df)} 条记录")
    print(f"标的数: 10, 天数: 10")
    
    return test_file

def test_optimized_version():
    """
    测试优化版本
    """
    print("=== 测试优化版本 ===")
    
    # 创建测试数据
    test_file = create_small_test_data()
    
    try:
        # 导入优化版本
        sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
        from rolling_stats_weekly_full_optimized import main as optimized_main
        
        # 临时修改sys.argv
        original_argv = sys.argv.copy()
        sys.argv = ['rolling_stats_weekly_full_optimized.py', test_file, '/tmp/test_output.xlsx', '5']
        
        try:
            print("开始运行优化版本...")
            start_time = time.time()
            
            success = optimized_main('20220101', '20220131')
            
            end_time = time.time()
            
            if success:
                print(f"✅ 优化版本运行成功！")
                print(f"耗时: {end_time - start_time:.2f}秒")
                
                # 检查输出文件
                output_file = '/tmp/test_output.xlsx'
                if os.path.exists(output_file):
                    print(f"✅ 输出文件已生成: {output_file}")
                    
                    # 读取第一个sheet查看结果
                    try:
                        df = pd.read_excel(output_file, sheet_name=0)
                        print(f"第一个sheet记录数: {len(df)}")
                        if len(df) > 0:
                            print("前几列数据:")
                            print(df.head())
                    except Exception as e:
                        print(f"读取输出文件时出错: {e}")
                else:
                    print("⚠️  输出文件未生成")
                    
            else:
                print("❌ 优化版本运行失败")
                
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        for temp_file in [test_file, '/tmp/test_output.xlsx']:
            if os.path.exists(temp_file):
                os.remove(temp_file)

def main():
    """
    主函数
    """
    print("=== 优化版本简单测试 ===")
    test_optimized_version()
    print("=== 测试完成 ===")

if __name__ == "__main__":
    main()
