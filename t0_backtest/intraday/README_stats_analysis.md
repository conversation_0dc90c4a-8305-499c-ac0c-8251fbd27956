# 回测数据统计分析脚本使用说明

## 概述

本项目提供了一套完整的回测数据统计分析脚本，用于处理T0回测数据并生成各种统计报告。脚本支持按日、按标的、按年和滚动周期等多种维度的统计分析。

## 文件结构

```
t0_backtest/intraday/
├── data_structure_analysis.py      # 数据结构分析脚本
├── daily_stats_analysis.py         # 按日统计分析脚本
├── symbol_stats_analysis.py        # 按标的统计分析脚本
├── annual_stats_analysis.py        # 按年统计分析脚本
├── rolling_stats_analysis.py       # 滚动周期统计分析脚本
├── test_all_stats.py              # 样本数据测试脚本
├── test_large_dataset.py          # 大数据集测试脚本
├── batch_process_stats.py         # 批量处理脚本
├── README_stats_analysis.md       # 本说明文档
├── history_tca/
│   ├── sample2.csv                 # 样本数据
│   └── signal_merged.fea          # 完整数据集
└── results/                       # 输出结果目录
```

## 数据格式要求

### 输入数据格式
脚本支持两种输入格式：
1. **CSV格式**: 如 `sample2.csv`
2. **Feather格式**: 如 `signal_merged.fea`

### 必需字段
输入数据必须包含以下字段：
- `code`: 标的代码
- `date`: 交易日期 (格式: YYYYMMDD)
- `max_amt`: 最大持仓金额
- `round_num`: 交易轮次数
- `profit`: 盈利金额
- `fee`: 手续费
- `profit_ac`: 费后盈利
- `long_open_amt`, `long_close_amt`: 多头开仓/平仓金额
- `short_open_amt`, `short_close_amt`: 空头开仓/平仓金额
- `amt_open`: 开仓总金额
- 其他相关字段...

## 脚本功能说明

### 1. 按日统计分析 (`daily_stats_analysis.py`)
**功能**: 按交易日期汇总统计指标
**输出**: 每日的交易统计数据，包括盈利、费用、换手率、胜率等

**使用方法**:
```bash
python daily_stats_analysis.py [输入文件] [输出文件]
```

**输出字段**:
- `hold_amt`: 持仓金额
- `trd_amt`: 交易金额
- `profit_ac`: 费后盈利
- `turnover`: 换手率
- `win_rate_ac`: 费后胜率
- 等50个统计指标

### 2. 按标的统计分析 (`symbol_stats_analysis.py`)
**功能**: 按标的代码汇总统计指标
**输出**: 每个标的的累计交易统计数据

**使用方法**:
```bash
python symbol_stats_analysis.py [输入文件] [输出文件]
```

**输出字段**:
- `symbol`: 标的代码
- `hold_amt`: 平均持仓金额
- `profit_ac`: 累计费后盈利
- `win_rate_ac`: 费后胜率
- `win_loss_ratio_ac`: 费后盈亏比
- 等12个统计指标

### 3. 按年统计分析 (`annual_stats_analysis.py`)
**功能**: 汇总年度统计指标
**输出**: 整体年度表现统计

**使用方法**:
```bash
python annual_stats_analysis.py [输入文件] [输出文件]
```

**输出字段**:
- `交易天数`: 总交易天数
- `平均底仓金额（万元）`: 平均持仓金额
- `年化收益率%`: 年化收益率
- `日胜率%`: 日胜率
- 等13个统计指标

### 4. 滚动周期统计分析 (`rolling_stats_analysis.py`)
**功能**: 计算每周最后一个交易日节点的最近N日滚动统计
**输出**: 滚动窗口统计数据

**使用方法**:
```bash
python rolling_stats_analysis.py [输入文件] [输出文件] [滚动窗口大小]
```

**默认参数**: 滚动窗口大小 = 20日

## 批量处理

### 使用批量处理脚本
```bash
python batch_process_stats.py [输入文件] [输出目录前缀]
```

该脚本会自动运行所有四种统计分析，并将结果保存到指定目录。

### 手动批量处理
```bash
# 设置输入文件
INPUT_FILE="/home/<USER>/trade/t0_backtest/intraday/history_tca/signal_merged.fea"
OUTPUT_DIR="/home/<USER>/trade/t0_backtest/intraday/results/full_analysis"

# 创建输出目录
mkdir -p $OUTPUT_DIR

# 运行各种统计分析
python daily_stats_analysis.py $INPUT_FILE $OUTPUT_DIR/daily_stats.csv
python symbol_stats_analysis.py $INPUT_FILE $OUTPUT_DIR/symbol_stats.csv
python annual_stats_analysis.py $INPUT_FILE $OUTPUT_DIR/annual_stats.csv
python rolling_stats_analysis.py $INPUT_FILE $OUTPUT_DIR/rolling_stats.xlsx 20
```

## 性能说明

### 处理能力
- **小数据集** (< 10万条): 秒级处理
- **中等数据集** (10万-100万条): 分钟级处理
- **大数据集** (> 1000万条): 小时级处理

### 内存要求
- 建议至少 8GB 内存处理大数据集
- 对于超大数据集，建议分批处理

### 优化建议
1. 使用SSD存储提高I/O性能
2. 对于超大数据集，考虑先按时间段分割
3. 可以并行处理不同的统计维度

## 测试验证

### 样本数据测试
```bash
python test_all_stats.py
```

### 大数据集测试
```bash
python test_large_dataset.py
```

## 故障排除

### 常见问题

1. **内存不足错误**
   - 解决方案: 减少数据量或增加内存
   - 可以使用分批处理

2. **字段缺失错误**
   - 解决方案: 检查输入数据是否包含所有必需字段
   - 参考 `data_structure_analysis.py` 的字段映射

3. **日期格式错误**
   - 解决方案: 确保日期格式为 YYYYMMDD
   - 检查日期字段是否为整数类型

4. **输出文件权限错误**
   - 解决方案: 检查输出目录的写权限
   - 确保输出目录存在

### 调试模式
在脚本中添加详细日志输出：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 输出结果说明

### 文件格式
- **CSV文件**: 日统计、标的统计、年度统计
- **Excel文件**: 滚动统计（支持更复杂的格式）

### 数值单位
- **金额**: 元（人民币）
- **收益率**: 小数形式（如 0.01 表示 1%）
- **换手率**: 小数形式
- **胜率**: 小数形式（如 0.6 表示 60%）

## 扩展开发

### 添加新的统计维度
1. 参考现有脚本结构
2. 实现数据转换函数
3. 实现统计计算函数
4. 添加测试用例

### 自定义统计指标
修改相应的统计函数，添加新的计算逻辑。

## 联系支持

如有问题或建议，请联系开发团队。
