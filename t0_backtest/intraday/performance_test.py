#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能对比测试脚本
比较原版本和优化版本的性能差异
"""

import pandas as pd
import numpy as np
import time
import sys
import os
from pathlib import Path

def test_small_dataset():
    """
    使用小数据集测试性能
    """
    print("=== 小数据集性能测试 ===")
    
    # 创建测试数据
    np.random.seed(42)
    n_symbols = 100
    n_days = 50
    n_records_per_day = 10
    
    test_data = []
    base_date = pd.Timestamp('2022-01-01')
    
    for symbol in range(1, n_symbols + 1):
        for day in range(n_days):
            date = base_date + pd.Timedelta(days=day)
            date_str = date.strftime('%Y%m%d')
            
            for _ in range(n_records_per_day):
                record = {
                    'code': symbol,
                    'date': date_str,
                    'max_amt': 100000,
                    'round_num': np.random.randint(0, 5),
                    'long_round_num': np.random.randint(0, 3),
                    'short_round_num': np.random.randint(0, 3),
                    'trade_num': np.random.randint(1, 10),
                    'long_open_vol': np.random.uniform(0, 1000),
                    'long_open_amt': np.random.uniform(0, 50000),
                    'short_open_vol': np.random.uniform(0, 1000),
                    'short_open_amt': np.random.uniform(0, 50000),
                    'long_close_vol': np.random.uniform(0, 1000),
                    'long_close_amt': np.random.uniform(0, 50000),
                    'short_close_vol': np.random.uniform(0, 1000),
                    'short_close_amt': np.random.uniform(0, 50000),
                    'profit': np.random.uniform(-1000, 1000),
                    'fee': np.random.uniform(0, 100),
                    'profit_ac': 0,  # 将在后面计算
                    'profit_long': np.random.uniform(-500, 500),
                    'fee_long': np.random.uniform(0, 50),
                    'profit_ac_long': 0,
                    'profit_short': np.random.uniform(-500, 500),
                    'fee_short': np.random.uniform(0, 50),
                    'profit_ac_short': 0,
                    'amt_open': np.random.uniform(10000, 90000),
                    'vol_open': np.random.uniform(100, 900),
                    'position': 100000
                }
                
                # 计算费后盈利
                record['profit_ac'] = record['profit'] - record['fee']
                record['profit_ac_long'] = record['profit_long'] - record['fee_long']
                record['profit_ac_short'] = record['profit_short'] - record['fee_short']
                
                test_data.append(record)
    
    test_df = pd.DataFrame(test_data)
    
    # 保存测试数据
    test_file = "/tmp/test_rolling_stats_data.csv"
    test_df.to_csv(test_file, index=False)
    
    print(f"生成测试数据: {len(test_df)} 条记录")
    print(f"标的数: {n_symbols}, 天数: {n_days}")
    
    return test_file

def run_original_version(test_file):
    """
    运行原版本进行性能测试
    """
    print("\n--- 测试原版本性能 ---")
    
    try:
        # 导入原版本
        sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
        from rolling_stats_weekly_full import main as original_main
        
        start_time = time.time()
        
        # 临时修改sys.argv来传递参数
        original_argv = sys.argv.copy()
        sys.argv = ['rolling_stats_weekly_full.py', test_file, '/tmp/test_original_output.xlsx', '20']
        
        try:
            success = original_main('20220101', '20221231')
            end_time = time.time()
            
            if success:
                print(f"✅ 原版本完成，耗时: {end_time - start_time:.2f}秒")
                return end_time - start_time
            else:
                print("❌ 原版本执行失败")
                return None
                
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"❌ 原版本测试出错: {e}")
        return None

def run_optimized_version(test_file):
    """
    运行优化版本进行性能测试
    """
    print("\n--- 测试优化版本性能 ---")
    
    try:
        # 导入优化版本
        sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
        from rolling_stats_weekly_full_optimized import main as optimized_main
        
        start_time = time.time()
        
        # 临时修改sys.argv来传递参数
        original_argv = sys.argv.copy()
        sys.argv = ['rolling_stats_weekly_full_optimized.py', test_file, '/tmp/test_optimized_output.xlsx', '20']
        
        try:
            success = optimized_main('20220101', '20221231')
            end_time = time.time()
            
            if success:
                print(f"✅ 优化版本完成，耗时: {end_time - start_time:.2f}秒")
                return end_time - start_time
            else:
                print("❌ 优化版本执行失败")
                return None
                
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"❌ 优化版本测试出错: {e}")
        return None

def compare_results():
    """
    比较两个版本的结果是否一致
    """
    print("\n--- 比较结果一致性 ---")
    
    try:
        # 读取两个版本的输出文件
        original_file = '/tmp/test_original_output.xlsx'
        optimized_file = '/tmp/test_optimized_output.xlsx'
        
        if not os.path.exists(original_file) or not os.path.exists(optimized_file):
            print("⚠️  输出文件不存在，无法比较")
            return False
        
        # 读取第一个sheet进行比较
        original_df = pd.read_excel(original_file, sheet_name=0)
        optimized_df = pd.read_excel(optimized_file, sheet_name=0)
        
        print(f"原版本记录数: {len(original_df)}")
        print(f"优化版本记录数: {len(optimized_df)}")
        
        if len(original_df) != len(optimized_df):
            print("⚠️  记录数不一致")
            return False
        
        # 比较关键字段
        key_columns = ['symbol', 'period_days', 'total_profit_ac', 'win_rate']
        for col in key_columns:
            if col in original_df.columns and col in optimized_df.columns:
                diff = abs(original_df[col] - optimized_df[col]).max()
                if diff > 1e-6:  # 允许小的数值误差
                    print(f"⚠️  字段 {col} 存在差异，最大差异: {diff}")
                    return False
        
        print("✅ 结果一致性验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 结果比较出错: {e}")
        return False

def main():
    """
    主测试函数
    """
    print("=== 滚动统计程序性能对比测试 ===")
    
    # 生成测试数据
    test_file = test_small_dataset()
    
    # 测试原版本
    original_time = run_original_version(test_file)
    
    # 测试优化版本
    optimized_time = run_optimized_version(test_file)
    
    # 性能对比
    print("\n=== 性能对比结果 ===")
    
    if original_time and optimized_time:
        speedup = original_time / optimized_time
        print(f"原版本耗时: {original_time:.2f}秒")
        print(f"优化版本耗时: {optimized_time:.2f}秒")
        print(f"性能提升: {speedup:.1f}倍")
        
        if speedup > 1:
            print("🚀 优化版本更快！")
        else:
            print("⚠️  优化版本未能提升性能")
    else:
        print("❌ 无法完成性能对比")
    
    # 比较结果
    compare_results()
    
    # 清理临时文件
    for temp_file in [test_file, '/tmp/test_original_output.xlsx', '/tmp/test_optimized_output.xlsx']:
        if os.path.exists(temp_file):
            os.remove(temp_file)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
