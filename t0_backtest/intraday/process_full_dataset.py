#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整数据集处理脚本
筛选position=100000的数据，进行按日、按标的、按年、按滚动周期统计
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import subprocess
import time
from datetime import datetime

def filter_position_data(input_file, position_value=100000, output_file=None):
    """
    筛选指定position值的数据
    """
    print(f"=== 筛选position={position_value}的数据 ===")
    print(f"输入文件: {input_file}")
    
    try:
        # 读取数据
        print("正在读取数据...")
        if input_file.endswith('.fea') or input_file.endswith('.feather'):
            df = pd.read_feather(input_file)
        else:
            df = pd.read_csv(input_file)
        
        print(f"✅ 原始数据形状: {df.shape}")
        print(f"✅ position值分布: {df['position'].value_counts().head()}")
        
        # 筛选指定position的数据
        df_filtered = df[df['position'] == position_value].copy()
        print(f"✅ 筛选后数据形状: {df_filtered.shape}")
        
        if len(df_filtered) == 0:
            print(f"❌ 没有找到position={position_value}的数据")
            return None
        
        # 显示筛选后的数据信息
        print(f"✅ 数据期间: {df_filtered['date'].min()} - {df_filtered['date'].max()}")
        print(f"✅ 标的数量: {df_filtered['code'].nunique()}")
        print(f"✅ 交易日数: {df_filtered['date'].nunique()}")
        print(f"✅ 有交易记录数: {len(df_filtered[df_filtered['round_num'] > 0])}")
        
        # 保存筛选后的数据
        if output_file:
            print(f"保存筛选数据到: {output_file}")
            df_filtered.to_csv(output_file, index=False)
            print(f"✅ 筛选数据已保存")
        
        return df_filtered
        
    except Exception as e:
        print(f"❌ 筛选数据时出错: {e}")
        return None

def run_stats_analysis(input_file, output_dir, position_value=100000):
    """
    运行所有统计分析
    """
    print(f"\n=== 运行统计分析 ===")
    print(f"输入文件: {input_file}")
    print(f"输出目录: {output_dir}")
    
    # 确保输出目录存在
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 定义输出文件
    output_files = {
        'daily': f"{output_dir}/perf_daily_position_{position_value}.csv",
        'symbol': f"{output_dir}/perf_symbol_position_{position_value}.csv", 
        'annual': f"{output_dir}/perf_annually_position_{position_value}.csv",
        'rolling': f"{output_dir}/perf_symbol_roll_20_position_{position_value}.xlsx"
    }
    
    # 统计分析脚本
    scripts = {
        'daily': 'daily_stats_analysis.py',
        'symbol': 'symbol_stats_analysis.py',
        'annual': 'annual_stats_analysis.py',
        'rolling': 'rolling_stats_analysis.py'
    }
    
    # 执行各种统计分析
    results = {}
    total_time = 0
    
    for analysis_type, script_name in scripts.items():
        print(f"\n--- 执行{analysis_type}统计分析 ---")
        start_time = time.time()
        
        try:
            # 运行脚本
            cmd = ['python', script_name, input_file, output_files[analysis_type]]
            if analysis_type == 'rolling':
                cmd.append('20')  # 滚动窗口大小
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                cwd='/home/<USER>/trade/t0_backtest/intraday',
                timeout=3600  # 1小时超时
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            total_time += execution_time
            
            if result.returncode == 0:
                print(f"✅ {analysis_type}统计完成 (耗时: {execution_time:.1f}秒)")
                
                # 检查输出文件
                if os.path.exists(output_files[analysis_type]):
                    file_size = os.path.getsize(output_files[analysis_type])
                    print(f"✅ 输出文件: {output_files[analysis_type]} ({file_size:,} bytes)")
                    
                    # 读取并显示结果摘要
                    try:
                        if output_files[analysis_type].endswith('.csv'):
                            df_result = pd.read_csv(output_files[analysis_type])
                            print(f"✅ 记录数: {len(df_result)}, 列数: {len(df_result.columns)}")
                        elif output_files[analysis_type].endswith('.xlsx'):
                            # 读取Excel文件的所有sheet
                            excel_file = pd.ExcelFile(output_files[analysis_type])
                            sheet_count = len(excel_file.sheet_names)
                            print(f"✅ Excel文件包含 {sheet_count} 个sheet")
                            if sheet_count > 0:
                                # 读取第一个sheet查看记录数
                                df_first = pd.read_excel(output_files[analysis_type], sheet_name=0)
                                print(f"✅ 第一个sheet记录数: {len(df_first)}")
                    except Exception as e:
                        print(f"⚠️  读取结果文件时出错: {e}")
                    
                    results[analysis_type] = {
                        'success': True,
                        'time': execution_time,
                        'file': output_files[analysis_type]
                    }
                else:
                    print(f"❌ 输出文件未生成: {output_files[analysis_type]}")
                    results[analysis_type] = {'success': False, 'time': execution_time}
            else:
                print(f"❌ {analysis_type}统计失败")
                print(f"错误信息: {result.stderr}")
                results[analysis_type] = {'success': False, 'time': execution_time}
                
        except subprocess.TimeoutExpired:
            print(f"❌ {analysis_type}统计超时")
            results[analysis_type] = {'success': False, 'time': 3600}
        except Exception as e:
            print(f"❌ {analysis_type}统计出错: {e}")
            results[analysis_type] = {'success': False, 'time': 0}
    
    # 统计总结
    print(f"\n=== 统计分析总结 ===")
    success_count = sum(1 for r in results.values() if r['success'])
    total_count = len(results)
    
    print(f"总分析数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    print(f"总耗时: {total_time/60:.1f} 分钟")
    
    for analysis_type, result in results.items():
        status = "✅" if result['success'] else "❌"
        print(f"{status} {analysis_type}统计 (耗时: {result['time']:.1f}秒)")
    
    return results

def generate_summary_report(results, output_dir, position_value=100000):
    """
    生成汇总报告
    """
    print(f"\n=== 生成汇总报告 ===")
    
    report_file = f"{output_dir}/analysis_summary_position_{position_value}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"Position {position_value} 统计分析汇总报告\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 50 + "\n\n")
        
        # 分析结果汇总
        f.write("分析结果汇总:\n")
        for analysis_type, result in results.items():
            status = "成功" if result['success'] else "失败"
            f.write(f"- {analysis_type}统计: {status} (耗时: {result['time']:.1f}秒)\n")
            if result['success'] and 'file' in result:
                f.write(f"  输出文件: {result['file']}\n")
        
        f.write("\n")
        
        # 文件列表
        f.write("生成的文件列表:\n")
        for analysis_type, result in results.items():
            if result['success'] and 'file' in result:
                file_path = result['file']
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    f.write(f"- {file_path} ({file_size:,} bytes)\n")
        
        f.write("\n")
        
        # 使用说明
        f.write("文件说明:\n")
        f.write("- perf_daily_*.csv: 按日统计结果，包含每日的交易统计指标\n")
        f.write("- perf_symbol_*.csv: 按标的统计结果，包含每个标的的累计统计指标\n")
        f.write("- perf_annually_*.csv: 按年统计结果，包含整体年度表现\n")
        f.write("- perf_symbol_roll_20_*.xlsx: 滚动20日统计结果，每个标的一个sheet\n")
    
    print(f"✅ 汇总报告已保存: {report_file}")

def main():
    """
    主函数
    """
    print("=== 完整数据集统计分析 ===")
    
    # 配置参数
    input_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/signal_merged.fea"
    position_value = 100000
    output_dir = f"/home/<USER>/trade/t0_backtest/intraday/results/position_{position_value}"
    filtered_data_file = f"{output_dir}/filtered_data_position_{position_value}.csv"
    
    print(f"输入文件: {input_file}")
    print(f"筛选条件: position = {position_value}")
    print(f"输出目录: {output_dir}")
    
    # 第一步：筛选数据
    df_filtered = filter_position_data(input_file, position_value, filtered_data_file)
    
    if df_filtered is None:
        print("❌ 数据筛选失败，程序终止")
        return False
    
    # 第二步：运行统计分析
    results = run_stats_analysis(filtered_data_file, output_dir, position_value)
    
    # 第三步：生成汇总报告
    generate_summary_report(results, output_dir, position_value)
    
    # 最终总结
    success_count = sum(1 for r in results.values() if r['success'])
    total_count = len(results)
    
    if success_count == total_count:
        print(f"\n🎉 所有统计分析完成！结果保存在: {output_dir}")
    else:
        print(f"\n⚠️  部分统计分析失败，请检查错误信息")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
