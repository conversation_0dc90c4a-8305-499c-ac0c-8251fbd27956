#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装Numba优化库的脚本
"""

import subprocess
import sys

def install_numba():
    """
    安装Numba库
    """
    print("正在安装Numba库...")
    
    try:
        # 尝试导入numba
        import numba
        print("✅ Numba已经安装")
        print(f"Numba版本: {numba.__version__}")
        return True
    except ImportError:
        print("⚠️  Numba未安装，开始安装...")
        
        try:
            # 安装numba
            subprocess.check_call([sys.executable, "-m", "pip", "install", "numba"])
            print("✅ Numba安装成功")
            
            # 验证安装
            import numba
            print(f"Numba版本: {numba.__version__}")
            return True
            
        except Exception as e:
            print(f"❌ Numba安装失败: {e}")
            print("请手动安装: pip install numba")
            return False

if __name__ == "__main__":
    success = install_numba()
    if success:
        print("\n🚀 现在可以运行优化版本的滚动统计程序了！")
    else:
        print("\n⚠️  请手动安装Numba后再运行优化版本")
