#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滚动周期统计分析脚本
基于tca.stats_agg_by_sym_roll_n函数，创建每周最后一个交易日节点的最近20日表现统计脚本
输出类似perf_symbol_roll_20_weipan_100000_w3.xlsx的结果
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加tca模块路径
sys.path.append('/home/<USER>/trade/t0_backtest/intraday/tca')
import tca

def prepare_data_for_rolling_stats(df):
    """
    准备数据用于滚动统计
    """
    print("准备数据用于滚动统计...")
    
    # 导入按日统计脚本的函数
    sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
    from daily_stats_analysis import transform_data_for_daily_stats, calculate_basic_daily_stats
    
    # 转换数据格式
    transformed_df = transform_data_for_daily_stats(df)
    
    # 按日期分组计算统计指标
    daily_stats = []
    
    for date, group in transformed_df.groupby('date'):
        print(f"处理日期: {date}")
        stats = calculate_basic_daily_stats(group)
        stats['date'] = date
        daily_stats.append(stats)
    
    daily_stats_df = pd.DataFrame(daily_stats)
    print(f"生成 {len(daily_stats_df)} 条日统计记录")
    
    return daily_stats_df

def calculate_rolling_stats_by_symbol(df, n=20):
    """
    按标的计算滚动统计指标
    """
    print(f"按标的计算滚动{n}日统计指标...")

    # 导入按日统计脚本的函数
    sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
    from daily_stats_analysis import transform_data_for_daily_stats, calculate_basic_daily_stats

    # 转换数据格式
    transformed_df = transform_data_for_daily_stats(df)

    # 按标的分组处理
    symbol_rolling_stats = {}

    for symbol, symbol_group in transformed_df.groupby('symbol'):
        print(f"处理标的: {symbol}")

        # 按日期分组计算统计指标
        daily_stats = []
        for date, group in symbol_group.groupby('date'):
            stats = calculate_basic_daily_stats(group)
            stats['date'] = date
            daily_stats.append(stats)

        if len(daily_stats) == 0:
            continue

        daily_stats_df = pd.DataFrame(daily_stats)
        daily_stats_df['date'] = pd.to_datetime(daily_stats_df['date'], format='%Y%m%d')
        daily_stats_df = daily_stats_df.sort_values('date').reset_index(drop=True)

        # 计算滚动统计
        rolling_stats = calculate_symbol_rolling_stats(daily_stats_df, symbol, n)

        if len(rolling_stats) > 0:
            symbol_rolling_stats[symbol] = rolling_stats

    return symbol_rolling_stats

def calculate_symbol_rolling_stats(daily_stats_df, symbol, n=20):
    """
    计算单个标的的滚动统计
    """
    if len(daily_stats_df) == 0:
        return pd.DataFrame()

    # 选择需要的列
    required_cols = ['date', 'hold_amt', 'fee', 'trd_amt', 'turnover', 'profit', 'profit_ac', 'ret2hold', 'ret2hold_ac']
    available_cols = [col for col in required_cols if col in daily_stats_df.columns]
    df_subset = daily_stats_df[available_cols].copy()

    # 计算滚动统计
    rolling_stats = df_subset.rolling(window=n, min_periods=1).agg({
        'hold_amt': 'mean',
        'fee': 'sum',
        'trd_amt': 'sum',
        'turnover': 'mean',
        'profit': 'sum',
        'profit_ac': 'sum',
        'ret2hold': 'mean',
        'ret2hold_ac': 'mean'
    })

    # 添加日期和标的列
    rolling_stats['date'] = daily_stats_df['date']
    rolling_stats['symbol'] = symbol

    # 重新排列列顺序
    cols = ['date', 'symbol'] + [col for col in rolling_stats.columns if col not in ['date', 'symbol']]
    rolling_stats = rolling_stats[cols]

    # 获取每周最后一个交易日
    rolling_stats['year_week'] = rolling_stats['date'].dt.strftime('%Y-%U')
    weekly_last_days = rolling_stats.groupby('year_week')['date'].max()

    # 筛选每周最后一个交易日的数据
    weekly_rolling_stats = rolling_stats[rolling_stats['date'].isin(weekly_last_days)].copy()
    weekly_rolling_stats = weekly_rolling_stats.drop('year_week', axis=1)

    return weekly_rolling_stats

def save_rolling_stats(symbol_rolling_stats, output_file):
    """
    保存滚动统计结果，每个标的保存为一个sheet
    """
    print(f"保存结果到: {output_file}")

    # 确保输出目录存在
    output_dir = Path(output_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)

    if len(symbol_rolling_stats) == 0:
        print("⚠️  没有滚动统计数据需要保存")
        # 创建空文件
        pd.DataFrame().to_excel(output_file, index=False)
        return

    # 根据文件扩展名保存
    if output_file.endswith('.xlsx'):
        # 保存为Excel文件，每个标的一个sheet
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            total_records = 0

            for symbol, rolling_stats_df in symbol_rolling_stats.items():
                if len(rolling_stats_df) > 0:
                    # 格式化日期
                    rolling_stats_df_copy = rolling_stats_df.copy()
                    rolling_stats_df_copy['date'] = rolling_stats_df_copy['date'].dt.strftime('%Y%m%d')

                    # 使用标的代码作为sheet名称，限制长度
                    sheet_name = str(symbol)[:31]  # Excel sheet名称最大31字符
                    rolling_stats_df_copy.to_excel(writer, sheet_name=sheet_name, index=False)
                    total_records += len(rolling_stats_df_copy)
                    print(f"  标的 {symbol}: {len(rolling_stats_df_copy)} 条记录")

            print(f"✅ 已保存 {len(symbol_rolling_stats)} 个标的，共 {total_records} 条滚动统计记录")
    else:
        # 保存为CSV文件，合并所有标的
        all_rolling_stats = []
        for symbol, rolling_stats_df in symbol_rolling_stats.items():
            if len(rolling_stats_df) > 0:
                rolling_stats_df_copy = rolling_stats_df.copy()
                rolling_stats_df_copy['date'] = rolling_stats_df_copy['date'].dt.strftime('%Y%m%d')
                all_rolling_stats.append(rolling_stats_df_copy)

        if all_rolling_stats:
            combined_df = pd.concat(all_rolling_stats, ignore_index=True)
            combined_df.to_csv(output_file, index=False)
            print(f"✅ 已保存 {len(combined_df)} 条滚动统计记录到CSV文件")

def main(input_file, output_file, rolling_window=20):
    """
    主函数
    """
    print(f"=== 滚动周期统计分析 ===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"滚动窗口: {rolling_window}日")

    # 读取数据
    print("读取数据...")
    if input_file.endswith('.fea') or input_file.endswith('.feather'):
        df = pd.read_feather(input_file)
    else:
        df = pd.read_csv(input_file)
    print(f"原始数据形状: {df.shape}")

    # 过滤有交易的记录
    df_traded = df[df['round_num'] > 0].copy()
    print(f"有交易记录数: {len(df_traded)}")

    if len(df_traded) == 0:
        print("没有交易记录，无法进行统计")
        return {}

    # 按标的计算滚动统计
    symbol_rolling_stats = calculate_rolling_stats_by_symbol(df_traded, rolling_window)

    # 保存结果
    save_rolling_stats(symbol_rolling_stats, output_file)

    print("=== 滚动周期统计分析完成 ===")
    return symbol_rolling_stats

if __name__ == "__main__":
    # 默认参数
    input_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/sample2.csv"
    output_file = "/home/<USER>/trade/t0_backtest/intraday/results/rolling_stats_sample.xlsx"
    rolling_window = 20
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    if len(sys.argv) > 3:
        rolling_window = int(sys.argv[3])
    
    # 执行分析
    rolling_stats_df = main(input_file, output_file, rolling_window)
