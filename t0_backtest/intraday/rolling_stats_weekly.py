#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按周末节点滚动统计脚本
每个周末一个sheet，包含所有股票在该周末回看过去20个交易日的表现
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import time

def main():
    """
    主函数 - 按周末节点计算滚动统计
    """
    print("=== 按周末节点滚动统计分析 ===")
    
    # 文件路径
    input_file = "/home/<USER>/trade/t0_backtest/intraday/results/final_position_100000/filtered_data_position_100000.csv"
    output_file = "/home/<USER>/trade/t0_backtest/intraday/results/final_position_100000/perf_weekly_roll_20_position_100000.xlsx"
    n = 20  # 滚动窗口
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    if len(sys.argv) > 3:
        n = int(sys.argv[3])
    
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"滚动窗口: {n}日")
    
    start_time = time.time()
    
    # 读取数据
    print("读取数据...")
    df = pd.read_csv(input_file)
    print(f"原始数据形状: {df.shape}")
    
    # 过滤有交易的记录
    df_traded = df[df['round_num'] > 0].copy()
    print(f"有交易记录数: {len(df_traded)}")
    
    if len(df_traded) == 0:
        print("没有交易记录，无法进行统计")
        return False
    
    # 导入按日统计脚本的函数
    sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
    from daily_stats_analysis import calculate_basic_daily_stats
    
    # 按标的和日期计算每日统计
    print("计算每日统计...")
    daily_stats_all = []
    
    symbols = df_traded['code'].unique()
    print(f"标的总数: {len(symbols)}")
    
    # 只处理前50个标的作为示例
    symbols_sample = symbols[:50]
    print(f"处理标的数（示例）: {len(symbols_sample)}")
    
    for i, symbol in enumerate(symbols_sample):
        if (i + 1) % 10 == 0:
            print(f"  处理标的 {i+1}/{len(symbols_sample)}")
        
        symbol_data = df_traded[df_traded['code'] == symbol].copy()
        
        for date, group in symbol_data.groupby('date'):
            try:
                # 简化数据转换
                group_copy = group.copy()
                group_copy['symbol'] = symbol
                group_copy['price'] = group_copy['max_amt']
                group_copy['quantity'] = 1
                group_copy['hold_amt'] = group_copy['max_amt']
                group_copy['trd_amt'] = group_copy['amt_open']
                group_copy['max_exposure'] = group_copy['position']
                group_copy['max_long_exposure'] = group_copy['long_open_amt']
                
                stats = calculate_basic_daily_stats(group_copy)
                stats['date'] = date
                stats['symbol'] = symbol
                daily_stats_all.append(stats)
                
            except Exception as e:
                print(f"    处理标的 {symbol} 日期 {date} 时出错: {e}")
                continue
    
    if len(daily_stats_all) == 0:
        print("没有生成日统计数据")
        return False
    
    daily_stats_df = pd.DataFrame(daily_stats_all)
    daily_stats_df['date'] = pd.to_datetime(daily_stats_df['date'], format='%Y%m%d')
    daily_stats_df = daily_stats_df.sort_values(['symbol', 'date']).reset_index(drop=True)
    
    print(f"生成 {len(daily_stats_df)} 条日统计记录")
    
    # 获取所有交易日期并确定每周最后一个交易日
    print("确定每周最后一个交易日...")
    all_dates = daily_stats_df['date'].unique()
    all_dates = pd.Series(all_dates).sort_values()

    # 按周分组，获取每周最后一个交易日
    weekly_dates = pd.DataFrame({'date': all_dates})
    weekly_dates['year_week'] = weekly_dates['date'].dt.strftime('%Y-%U')
    weekly_last_days = weekly_dates.groupby('year_week')['date'].max().sort_values()
    
    print(f"识别出 {len(weekly_last_days)} 个周末交易日")
    
    # 按周末节点计算滚动统计
    print("计算滚动统计...")
    weekly_rolling_stats = {}
    
    for week_end_date in weekly_last_days:
        week_str = week_end_date.strftime('%Y%m%d')
        print(f"  处理周末日期: {week_str}")
        
        # 获取该周末日期前N个交易日的数据
        end_date = week_end_date
        start_date = end_date - pd.Timedelta(days=n*2)  # 预留足够的天数
        
        # 筛选时间范围内的数据
        period_data = daily_stats_df[
            (daily_stats_df['date'] >= start_date) & 
            (daily_stats_df['date'] <= end_date)
        ].copy()
        
        if len(period_data) == 0:
            continue
        
        # 按标的计算滚动统计
        symbol_stats = []
        
        for symbol in period_data['symbol'].unique():
            symbol_period_data = period_data[period_data['symbol'] == symbol].copy()
            symbol_period_data = symbol_period_data.sort_values('date')
            
            # 取最近N个交易日
            if len(symbol_period_data) > n:
                symbol_period_data = symbol_period_data.tail(n)
            
            if len(symbol_period_data) == 0:
                continue
            
            # 计算滚动统计指标
            stats = {
                'symbol': symbol,
                'period_days': len(symbol_period_data),
                'start_date': symbol_period_data['date'].min().strftime('%Y%m%d'),
                'end_date': symbol_period_data['date'].max().strftime('%Y%m%d'),
                'avg_hold_amt': symbol_period_data['hold_amt'].mean(),
                'total_trd_amt': symbol_period_data['trd_amt'].sum(),
                'total_profit': symbol_period_data['profit'].sum(),
                'total_profit_ac': symbol_period_data['profit_ac'].sum(),
                'avg_ret2hold_ac': symbol_period_data['ret2hold_ac'].mean(),
                'total_round_num': symbol_period_data['round_num'].sum(),
                'win_days': len(symbol_period_data[symbol_period_data['profit_ac'] > 0]),
                'win_rate': len(symbol_period_data[symbol_period_data['profit_ac'] > 0]) / len(symbol_period_data) if len(symbol_period_data) > 0 else 0
            }
            
            # 计算累计收益率
            if stats['avg_hold_amt'] > 0:
                stats['period_return_ac'] = stats['total_profit_ac'] / stats['avg_hold_amt']
            else:
                stats['period_return_ac'] = 0
            
            symbol_stats.append(stats)
        
        if len(symbol_stats) > 0:
            weekly_rolling_stats[week_str] = pd.DataFrame(symbol_stats)
    
    # 保存结果
    print(f"\n保存结果...")
    
    # 确保输出目录存在
    output_dir = Path(output_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if len(weekly_rolling_stats) == 0:
        print("⚠️  没有滚动统计数据需要保存")
        pd.DataFrame().to_excel(output_file, index=False)
    else:
        # 保存为Excel文件，每个周末一个sheet
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            total_records = 0
            saved_count = 0
            
            for week_date, week_stats_df in weekly_rolling_stats.items():
                if len(week_stats_df) > 0:
                    try:
                        # 使用周末日期作为sheet名称
                        sheet_name = f"Week_{week_date}"
                        week_stats_df.to_excel(writer, sheet_name=sheet_name, index=False)
                        total_records += len(week_stats_df)
                        saved_count += 1
                        
                        if saved_count % 10 == 0:
                            print(f"  已保存 {saved_count} 个周末...")
                            
                    except Exception as e:
                        print(f"  保存周末 {week_date} 时出错: {e}")
                        continue
            
            print(f"✅ 已保存 {saved_count} 个周末，共 {total_records} 条滚动统计记录")
    
    total_time = time.time() - start_time
    print(f"=== 滚动统计分析完成 ===")
    print(f"总耗时: {total_time/60:.1f}分钟")
    print(f"处理周数: {len(weekly_rolling_stats)}")
    
    return len(weekly_rolling_stats) > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
