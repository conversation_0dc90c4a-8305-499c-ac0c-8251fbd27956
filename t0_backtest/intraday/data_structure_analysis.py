#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据结构分析和字段映射
分析sample2.csv和目标统计结果的字段差异，建立字段映射关系
"""

import pandas as pd
import numpy as np
from pathlib import Path

def analyze_sample_data():
    """分析样本数据结构"""
    print("=== 分析样本数据结构 ===")
    
    # 读取样本数据
    sample_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/sample2.csv"
    df_sample = pd.read_csv(sample_file)
    
    print(f"样本数据形状: {df_sample.shape}")
    print(f"样本数据列名: {list(df_sample.columns)}")
    print(f"样本数据前5行:")
    print(df_sample.head())
    print(f"\n样本数据类型:")
    print(df_sample.dtypes)
    print(f"\n样本数据统计信息:")
    print(df_sample.describe())
    
    return df_sample

def analyze_target_results():
    """分析目标结果数据结构"""
    print("\n=== 分析目标结果数据结构 ===")
    
    results_dir = "/home/<USER>/trade/t0_backtest/intraday/results/weipan/"
    
    # 分析按日统计结果
    print("\n--- 按日统计结果 ---")
    df_daily = pd.read_csv(results_dir + "perf_daily_weipan_100000_w3.csv")
    print(f"按日统计形状: {df_daily.shape}")
    print(f"按日统计列名: {list(df_daily.columns)}")
    print(f"按日统计前3行:")
    print(df_daily.head(3))
    
    # 分析按标的统计结果
    print("\n--- 按标的统计结果 ---")
    df_symbol = pd.read_csv(results_dir + "perf_symbol_weipan_100000_w3.csv")
    print(f"按标的统计形状: {df_symbol.shape}")
    print(f"按标的统计列名: {list(df_symbol.columns)}")
    print(f"按标的统计前3行:")
    print(df_symbol.head(3))
    
    # 分析按年统计结果
    print("\n--- 按年统计结果 ---")
    df_annual = pd.read_csv(results_dir + "perf_annually_weipan_100000_w3.csv")
    print(f"按年统计形状: {df_annual.shape}")
    print(f"按年统计列名: {list(df_annual.columns)}")
    print(f"按年统计内容:")
    print(df_annual)
    
    return df_daily, df_symbol, df_annual

def create_field_mapping():
    """创建字段映射关系"""
    print("\n=== 创建字段映射关系 ===")
    
    # sample2.csv 字段
    sample_fields = [
        'code', 'date', 'max_amt', 'round_num', 'long_round_num', 'short_round_num', 
        'trade_num', 'long_open_vol', 'long_open_amt', 'short_open_vol', 'short_open_amt',
        'long_close_vol', 'long_close_amt', 'short_close_vol', 'short_close_amt',
        'profit', 'fee', 'profit_ac', 'profit_long', 'fee_long', 'profit_ac_long',
        'profit_short', 'fee_short', 'profit_ac_short', 'amt_open', 'vol_open',
        'ret_to_trade', 'ret_to_hold', 'ret_to_trade_ac', 'ret_to_hold_ac',
        'ret_to_trade_long', 'ret_to_hold_long', 'ret_to_trade_ac_long', 'ret_to_hold_ac_long',
        'ret_to_trade_short', 'ret_to_hold_short', 'ret_to_trade_ac_short', 'ret_to_hold_ac_short',
        'position'
    ]
    
    # 目标统计函数需要的字段
    required_fields = {
        'stats_agg_by_date': [
            'price', 'quantity', 'trd_amt', 'fee', 'profit', 'position', 
            'cancel_ord_num', 'ord_num', 'reject_ord_num',
            'long_open_amt', 'long_close_amt', 'profit_long', 'fee_long',
            'short_open_amt', 'short_close_amt', 'profit_short', 'fee_short',
            'round_num', 'win_round_num', 'win_round_num_ac', 'profit_round_amt_ac',
            'max_exposure', 'max_long_exposure'
        ],
        'stats_agg_by_sym': [
            'price', 'quantity', 'fee', 'trd_amt', 'profit', 'round_num',
            'win_round_num_ac', 'profit_round_amt_ac'
        ],
        'stats_agg_annually': [
            'hold_amt', 'trd_amt', 'fee', 'profit', 'profit_ac', 'turnover',
            'ret2hold_ac', 'max_exposure_rate', 'max_long_exposure_rate'
        ]
    }
    
    # 字段映射关系
    field_mapping = {
        # 基础字段映射
        'symbol': 'code',  # 标的代码
        'date': 'date',    # 日期
        'price': 'max_amt',  # 使用max_amt作为价格基准（持仓金额）
        'quantity': 1,     # 数量设为1，因为已经是金额
        'profit': 'profit',
        'fee': 'fee',
        'profit_ac': 'profit_ac',
        'position': 'position',
        'round_num': 'round_num',
        
        # 交易金额计算
        'trd_amt': 'amt_open',  # 开仓金额作为交易金额
        
        # 多头相关
        'long_open_amt': 'long_open_amt',
        'long_close_amt': 'long_close_amt', 
        'profit_long': 'profit_long',
        'fee_long': 'fee_long',
        
        # 空头相关
        'short_open_amt': 'short_open_amt',
        'short_close_amt': 'short_close_amt',
        'profit_short': 'profit_short',
        'fee_short': 'fee_short',
        
        # 需要计算的字段
        'hold_amt': 'max_amt',  # 持仓金额
        'turnover': 'calculated',  # 需要计算: trd_amt/hold_amt
        'ret2hold': 'ret_to_hold',
        'ret2hold_ac': 'ret_to_hold_ac',
        'ret2trade': 'ret_to_trade', 
        'ret2trade_ac': 'ret_to_trade_ac',
        
        # 缺失字段（需要设置默认值或计算）
        'cancel_ord_num': 0,
        'ord_num': 'trade_num',
        'reject_ord_num': 0,
        'win_round_num': 'calculated',  # 需要根据profit>0计算
        'win_round_num_ac': 'calculated',  # 需要根据profit_ac>0计算
        'profit_round_amt_ac': 'calculated',  # 需要计算盈利轮次的profit_ac总和
        'max_exposure': 'calculated',  # 需要计算最大敞口
        'max_long_exposure': 'calculated',  # 需要计算最大多头敞口
    }
    
    print("字段映射关系:")
    for target_field, source_field in field_mapping.items():
        print(f"  {target_field} <- {source_field}")
    
    return field_mapping

def identify_missing_fields():
    """识别缺失字段和需要计算的字段"""
    print("\n=== 识别缺失字段和计算逻辑 ===")
    
    missing_calculations = {
        'turnover': 'trd_amt / hold_amt',
        'win_round_num': 'count(profit > 0)',
        'win_round_num_ac': 'count(profit_ac > 0)', 
        'profit_round_amt_ac': 'sum(profit_ac where profit_ac > 0)',
        'max_exposure': 'max(position)',
        'max_long_exposure': 'max(long_position)',
        'trd_rate': 'count(trd_amt > 0) / total_count',
        'cancel_rate': 'cancel_ord_num / (ord_num - reject_ord_num)',
        'win_rate_ac': 'win_round_num_ac / round_num',
        'win_loss_ratio_ac': 'avg_win_profit / avg_loss_profit'
    }
    
    print("需要计算的字段:")
    for field, calculation in missing_calculations.items():
        print(f"  {field}: {calculation}")
    
    return missing_calculations

if __name__ == "__main__":
    # 执行分析
    df_sample = analyze_sample_data()
    df_daily, df_symbol, df_annual = analyze_target_results()
    field_mapping = create_field_mapping()
    missing_calculations = identify_missing_fields()
    
    print("\n=== 分析完成 ===")
    print("已识别字段映射关系和计算逻辑，可以开始创建统计脚本。")
