# from misc.tools import find_latest_remote_file_on_date, get_stock_adj_close
# from misc.ssh_conn import sftp_clent_zsdav
# from misc.Readstockfile import update_remote_xlsx_getdf, update_remote_xlsx_putdf

import os, sys
# name = find_latest_remote_file_on_date(
#     sftp_method=sftp_clent_zsdav,
#     dir= '超量子中泰/hold',
#     file_prefix='hold_',
#     date='20250610'
# )
# print(name)


# path = 'clz_zz1000账户超额带公式.xlsx'

# df = update_remote_xlsx_getdf(path, src_type='zsdav', sheet_name='clz_zz1000')
# df['test'] = 'test'
# print(df)
# update_remote_xlsx_putdf(df, file_type='xlsx', dest_type='zsdav', dest_path=path, sheet_name='clz_zz1000', index=False)



# get current working dir
# cur_dir = os.path.dirname(os.path.abspath(__file__))
# print(cur_dir)
# # get python path
# python_path = sys.path[0]
# print(python_path)

# # get current working dir
# work_dir = os.getcwd()
# print(work_dir)


df = get_stock_adj_close('20250617')
print(df.head(3))