import re
import pandas as pd
from joblib import Parallel, delayed
from data import data_reader
import os
import uuid
import datetime
from tca import tca
import pickle
from numba import jit

def create_order(sym,pid,price,qty,side,time,account_id):
    return {'pid':pid,'id':uuid.uuid4().__str__(),'symbol':sym,'price':price,'quantity':qty,'filled_price':price,'filled_quantity':qty,'status':100,'side':side,'create_time':time,'last_upd_time':time,'account_id':account_id}


def bt_pord(pord,sigs,open_th,close_th,close_price):
    
    buy_qty,sell_qty,position=0,0,0
    lotsize=200 if pord['symbol']=='688' else 100
    ords=[]
    for sig in sigs:
        if ((pord['quantity']-buy_qty) <lotsize) and ((pord['quantity']-sell_qty)<lotsize):
            break
        px,qty,side=None,None,None
        # print(pord['symbol'],sig['signal']-sig['spd_ret'],open_th)
        if (sig['signal']-sig['spd_ret'])>=open_th:  
            side=1
            inventory_buy=(pord['quantity']-buy_qty)
            qty=min(inventory_buy,sig['as1'])
            qty=qty-qty%lotsize
            px=sig['ask1']
        elif (sig['signal']+sig['spd_ret'])<=-open_th:
            side=-1
            inventory_sell=(pord['quantity']-sell_qty)
            qty=min(inventory_sell,sig['bs1'])
            qty=qty-qty%lotsize
            px=sig['bid1']
        else:
            if (sig['signal']-sig['spd_ret'])>=close_th and position<0:  
                side=1
                qty=min(-position,sig['as1'])
                qty=qty-qty%lotsize
                px=sig['ask1']
            elif (sig['signal']+sig['spd_ret'])<=-close_th and position>0:
                side=-1
                qty=min(position,sig['bs1'])
                qty=qty-qty%lotsize
                px=sig['bid1']

        if side is not None:
            if qty>=lotsize:
                ord=create_order(pord['symbol'],pord['id'],px,qty,side,sig['time'],pord['account_id'])
                ords.append(ord)
                if side>0:
                    buy_qty+=qty
                else:
                    sell_qty+=qty
                position=buy_qty-sell_qty
    if position>=lotsize:
        ord=create_order(pord['symbol'],pord['id'],close_price,position,-1,pord['end_time'],pord['account_id'])
        ords.append(ord)
    if -position>=lotsize:
        ord=create_order(pord['symbol'],pord['id'],close_price,-position,1,pord['end_time'],pord['account_id'])
        ords.append(ord)
    return ords
    



def backtest_by_pord(pord,sigs,open_th,close_th,filt_sig_by_time,close_price):
    signals=sigs.copy()
    if filt_sig_by_time:
        signals=signals[(signals['time']>pord['start_time'])&(signals['time']<pord['end_time'])]
    signals['spd_ret']=(signals['ask1']-signals['bid1'])/((signals['ask1']+signals['bid1'])/2)
    signals['signal']=signals['signal']/10000

    sigs = signals.to_dict("records")
    ords = bt_pord(pord, sigs, open_th, close_th, close_price)
    return ords
    
    # buy_qty,sell_qty,position=0,0,0
    # lotsize=200 if pord['symbol']=='688' else 100
    # ords=[]
    # for sig in signals.to_dict("records"):
    #     if ((pord['quantity']-buy_qty) <lotsize) and ((pord['quantity']-sell_qty)<lotsize):
    #         break
    #     px,qty,side=None,None,None
    #     # print(pord['symbol'],sig['signal']-sig['spd_ret'],open_th)
    #     if (sig['signal']-sig['spd_ret'])>=open_th:  
    #         side=1
    #         inventory_buy=(pord['quantity']-buy_qty)
    #         qty=min(inventory_buy,sig['as1'])
    #         qty=qty-qty%lotsize
    #         px=sig['ask1']
    #     elif (sig['signal']+sig['spd_ret'])<=-open_th:
    #         side=-1
    #         inventory_sell=(pord['quantity']-sell_qty)
    #         qty=min(inventory_sell,sig['bs1'])
    #         qty=qty-qty%lotsize
    #         px=sig['bid1']
    #     else:
    #         if (sig['signal']-sig['spd_ret'])>=close_th and position<0:  
    #             side=1
    #             qty=min(-position,sig['as1'])
    #             qty=qty-qty%lotsize
    #             px=sig['ask1']
    #         elif (sig['signal']+sig['spd_ret'])<=-close_th and position>0:
    #             side=-1
    #             qty=min(position,sig['bs1'])
    #             qty=qty-qty%lotsize
    #             px=sig['bid1']

    #     if side is not None:
    #         if qty>=lotsize:
    #             ord=create_order(pord['symbol'],pord['id'],px,qty,side,sig['time'],pord['account_id'])
    #             ords.append(ord)
    #             if side>0:
    #                 buy_qty+=qty
    #             else:
    #                 sell_qty+=qty
    #             position=buy_qty-sell_qty
    # if position>=lotsize:
    #     ord=create_order(pord['symbol'],pord['id'],close_price,position,-1,pord['end_time'],pord['account_id'])
    #     ords.append(ord)
    # if -position>=lotsize:
    #     ord=create_order(pord['symbol'],pord['id'],close_price,-position,1,pord['end_time'],pord['account_id'])
    #     ords.append(ord)
    # return ords

# def backtest_by_pords(sym,pos,date,close_px,open_th,close_th,filt_sig_by_time,bt=True):
#     l=[]
#     if bt:
#         signals=data_reader.get_arron_signals_bt(date,sym)
#         if signals is None:
#             signals=data_reader.get_arron_signals_prod(date,sym)
#     else:
#         signals=data_reader.get_arron_signals_prod(date,sym)
#     print(f'signals: {signals}')
#     if signals is not None:
#         if signals is not None:
#             signals['time']=pd.to_datetime(signals['time'])
#         if not bt:
#             signals['time']=signals['time']+datetime.timedelta(hours=8)
#         for po in pos.to_dict('records'):
#             ords=backtest_by_pord(po,signals,open_th,close_th,filt_sig_by_time,close_px)
#             l=l+ords
#     return l

def backtest_by_pords(sym,pos,date,close_px,open_th,close_th,filt_sig_by_time,bt=True):
    l=[]
    is_bt=False
    if bt:
        signals=data_reader.get_arron_signals_bt(date,sym)
        is_bt=True
        if signals is None:
            signals=data_reader.get_arron_signals_prod(date,sym)
            is_bt=False
    else:
        signals=data_reader.get_arron_signals_prod(date,sym)
    if signals is not None:
        if signals is not None:
            signals['time']=pd.to_datetime(signals['time'])
        if not is_bt or date>"20250101":
            signals['time']=signals['time']+datetime.timedelta(hours=8)
        for po in pos.to_dict('records'):
            ords=backtest_by_pord(po,signals,open_th,close_th,filt_sig_by_time,close_px)
            l=l+ords
    return l
    
def backtest_by_date(date,pords,open_th,close_th,filt_sig_by_time,bt=True):
    pxs=data_reader.get_close_pxs_by_date_from_file(date)
    args=[]
    for sym,pos in pords.groupby("symbol"):
        close_px=pxs.get(sym,None)
        # if sym == '000034':
        #     print(f'debug {date} {sym} {close_px}')
        if close_px is None:
            print("can not get close price of {} {}".format(date,sym))
            continue
        args.append([sym,pos,date,close_px,bt])
    # rets=Parallel(n_jobs=1)(delayed(backtest_by_pords)(a[0],a[1],a[2],a[3],open_th,close_th,filt_sig_by_time,a[4]) for a in args)
    rets=[]
    for a in args:
        ret=backtest_by_pords(a[0],a[1],a[2],a[3],open_th,close_th,filt_sig_by_time,a[4])
        rets.append(ret)
    l=[]
    for r in rets:
        l=l+r
    return pd.DataFrame(l),pords


def make_parentorder(symbol,quantity,account_id,date,start_time=None,end_time=None):
    if start_time is None:
        start_time=datetime.datetime.strptime(date+"093000","%Y%m%d%H%M%S")
    if end_time is None:
        end_time=datetime.datetime.strptime(date+"145700","%Y%m%d%H%M%S")
    id=uuid.uuid4().__str__()
    return {'id':id,'symbol':symbol,'quantity':quantity,'account_id':account_id,'start_time':start_time,'end_time':end_time}


def read_positions(pt,date,sym_name,qty_name,filt_type='csv',account="test_account_w3"):
    if filt_type=='csv':
        df=pd.read_csv(pt)
    else:
        df=pd.read_excel(pt)
    df=df.rename(columns={sym_name:'symbol',qty_name:'quantity'})
    df=df[['symbol','quantity']]
    df['symbol']=df['symbol'].apply(lambda x:str(int(x)).zfill(6))
    df['quantity']=df['quantity'].astype(int)
    l=[]
    for data in df.to_dict('records'):
        po=make_parentorder(data['symbol'],data['quantity'],account,date)
        l.append(po)
    return pd.DataFrame(l)

def read_position_value(pt,date,sym_name,val_name,sym_process_func=lambda x:str(int(x)).zfill(6),filt_type='csv',account="test_account_w3"):
    if filt_type=='csv':
        df=pd.read_csv(pt)
    else:
        df=pd.read_excel(pt)
    df=df.rename(columns={sym_name:'symbol',val_name:'val'})
    df=df[['symbol','val']]
    df['symbol']=df['symbol'].apply(sym_process_func)
    df['val']=df['val'].astype(int)
    prices=data_reader.get_close_pxs_by_date_from_file(date)
    df['quantity']=df.apply(lambda x:int(x['val']/prices.get(x['symbol'],0)/100)*100,axis=1).fillna(0)
    l=[]
    for data in df.to_dict('records'):
        po=make_parentorder(data['symbol'],data['quantity'],account,date)
        l.append(po)
    return pd.DataFrame(l)

###
#date 日期
#open_th: 开仓阈值
#close_th: 平仓阈值
#filt_sig_by_time: 是否根据start time 和end time 来交易
#rt_raw_tca: 是否直接返回交易数据，false的话直接返回统计数据
###
def run_by_date(date,open_th,pords,close_th=0,filt_sig_by_time=False,isbt=False,rt_raw_tca=False):
    ords,pords=backtest_by_date(date,pords,open_th,close_th,filt_sig_by_time,isbt)
    # print(f'ords: {ords} pords: {pords}')
    if ords.empty:
        assert("ord is empty")
        return None
    ords['operation']=ords['side'].apply(lambda x:100 if x==1 else 110)
    pxs=data_reader.get_close_pxs_by_date_from_file(date)
    pos,trds=tca.get_transaction_and_position(pords,ords,pxs)
    max_exposure,max_long_exposure=tca.get_exposure(ords)
    rets=tca.stats_by_pords(pords,ords,pos,trds)
    tca_df=pd.DataFrame(rets).fillna(0)
    tca_df['date']=date
    tca_df['max_exposure']=max_exposure
    tca_df['max_long_exposure']=max_long_exposure
    if rt_raw_tca:
        return tca_df
    rlts=tca.stats_agg_by_date(tca_df)
    rlts['date']=date
    return rlts


def run_by_date_get_trds(date,open_th,pords,close_th=0,filt_sig_by_time=False,isbt=False,rt_raw_tca=False):
    ords,pords=backtest_by_date(date,pords,open_th,close_th,filt_sig_by_time,isbt)
    # print(f'ords: {ords} pords: {pords}')
    if ords.empty:
        assert("ord is empty")
        return None
    ords['operation']=ords['side'].apply(lambda x:100 if x==1 else 110)
    pxs=data_reader.get_close_pxs_by_date_from_file(date)
    pos,trds=tca.get_transaction_and_position(pords,ords,pxs)
    
    return trds #pd.DataFrame()
    

def run_by_date2(date,open_th,pords,close_th=0,filt_sig_by_time=False,isbt=False):
    ords,pords=backtest_by_date(date,pords,open_th,close_th,filt_sig_by_time,isbt)
    if ords.empty:
        assert("ord is empty")
    ords['operation']=ords['side'].apply(lambda x:100 if x==1 else 110)
    pxs=data_reader.get_close_pxs_by_date_from_file(date)
    pos,trds=tca.get_transaction_and_position(pords,ords,pxs)
    max_exposure,max_long_exposure=tca.get_exposure(ords)
    rets=tca.stats_by_pords(pords,ords,pos,trds)
    tca_df=pd.DataFrame(rets).fillna(0)
    tca_df['date']=date
    tca_df['max_exposure']=max_exposure
    tca_df['max_long_exposure']=max_long_exposure
    return tca_df,trds

def get_perf(pt, start_date=None, end_date=None):
    with open(pt, 'rb') as file:
        datas=pickle.load(file)
    df=pd.concat(datas)
    l=[]
    for date,g in df.groupby('date'):
        if start_date is not None and date<start_date:
            continue
        if end_date is not None and date>end_date:
            continue
        d=tca.stats_agg_by_date(g)
        d['date']=date
        l.append(d)
    perf_daily=pd.DataFrame(l)
    perf_annually=tca.stats_agg_annually(perf_daily)
    return perf_daily,perf_annually

def get_perf_by_sym(pt, start_date=None, end_date=None):
    with open(pt, 'rb') as file:
        datas=pickle.load(file)
    df=pd.concat(datas)
    l=[]
    for sym,g in df.groupby('symbol'):   
        if start_date is not None:
            g=g[g['date']>=start_date]
        if end_date is not None:
            g=g[g['date']<=end_date]
        d=tca.stats_agg_by_sym(g)
        d['symbol']=sym
        l.append(d)
    perf_sym=pd.DataFrame(l)
    return perf_sym

def get_perf_by_symbol_window(df, start_date, end_date, n):
    # with open(pt, 'rb') as file:
    #     datas=pickle.load(file)
    # df=pd.concat(datas)
    # print(f'columns: {df.columns}')
    # print(f'shape: {df.shape}')
    
    l = []
    df=df[(pd.to_datetime(df['date'])>=pd.to_datetime(start_date)) & (pd.to_datetime(df['date'])<=pd.to_datetime(end_date))].copy()
    df=df.sort_values(by=['date'])
    # select the last n days of day_at_window
    for sym,g in df.groupby('symbol'):
        d = tca.stats_agg_by_sym_window(g)

        l.append(d)
    perf_sym=pd.DataFrame(l)
    perf_sym=perf_sym.sort_values(by=['ret2hold_ac_roll_mean(bps)'], ascending=False)
    perf_sym=perf_sym.rename(columns={
        'date':                   '日期',
        'symbol':                 '代码',
        'hold_amt_roll_mean':     f'近{n}日平均底仓市值',
        'fee_roll_sum':           f'近{n}日总费用',
        'trd_amt_roll_sum':       f'近{n}日总交易金额',
        'turnover_roll_mean':     f'近{n}日平均换手率',
        'profit_roll_sum':        f'近{n}日总费前盈利',
        'profit_ac_roll_sum':          f'近{n}日总费后盈利',
        'ret2hold_roll_mean(bps)':     f'近{n}日平均底仓收益率(bps)',
        'ret2hold_ac_roll_mean(bps)':  f'近{n}日平均费后底仓收益率(bps)',
    })
    
    
    return perf_sym
        
    



    # df=df.set_index('date')
    # df=df.resample('D').sum()
    # df=df.reset_index()
    # df['date']=df['date'].dt.strftime('%Y%m%d')
    # df=df[df['date']>=day_at_window]
    # print(f'columns: {df.columns}')
    # print(f'shape: {df.shape}')
    
    
    
    # l=[]
    # for sym,g in df.groupby('symbol'):   
    #     df=tca.stats_agg_by_sym_roll_n(g, n)
        
    #     l.append(df)
    # perf_sym=pd.concat(l, ignore_index=True)
    # return perf_sym
    
    # df['date']=pd.to_datetime(df['date'])
    # df=df.sort_values(by='date')
    # df['ret']=df['pnl'].div(df['position_value']).fillna(0)
    # df['ret']=df['ret'].rolling(20).sum()
    # df['ret']=df['ret'].div(20)
    # df=df[['date','symbol','ret']]