#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保存结果数据为多种格式
"""

import pickle
import json
import pandas as pd
from datetime import datetime
import os


def save_results():
    """保存结果数据为多种格式"""
    print('=== 保存结果数据为多种格式 ===')

    # 加载结果数据
    with open('constituents_by_date.pkl', 'rb') as f:
        constituents_by_date = pickle.load(f)

    print(f'加载数据: {len(constituents_by_date)}个交易日')

    # 1. 保存为JSON格式（便于其他语言读取）
    print('保存为JSON格式...')
    try:
        with open('constituents_by_date.json', 'w', encoding='utf-8') as f:
            json.dump(constituents_by_date, f, ensure_ascii=False, indent=2)
        print('JSON格式保存成功')
    except Exception as e:
        print(f'JSON保存失败: {e}')

    # 2. 保存为CSV格式（便于Excel查看）
    print('保存为CSV格式...')
    try:
        csv_data = []
        for date, stocks in constituents_by_date.items():
            for stock in stocks:
                csv_data.append({
                    '日期': date,
                    '代码': stock['代码'],
                    '简称': stock['简称']
                })

        df_csv = pd.DataFrame(csv_data)
        df_csv.to_csv('constituents_by_date.csv', index=False, encoding='utf-8-sig')
        print('CSV格式保存成功')
    except Exception as e:
        print(f'CSV保存失败: {e}')

    # 3. 创建简化版本（只包含代码列表）
    print('创建简化版本...')
    try:
        simplified_data = {}
        for date, stocks in constituents_by_date.items():
            simplified_data[date] = [stock['代码'] for stock in stocks]

        with open('constituents_codes_by_date.pkl', 'wb') as f:
            pickle.dump(simplified_data, f)

        with open('constituents_codes_by_date.json', 'w', encoding='utf-8') as f:
            json.dump(simplified_data, f, ensure_ascii=False, indent=2)
        print('简化版本保存成功')
    except Exception as e:
        print(f'简化版本保存失败: {e}')

    # 4. 显示文件大小
    print('\n=== 保存完成 ===')
    files = [
        'constituents_by_date.pkl',
        'constituents_by_date.json',
        'constituents_by_date.csv',
        'constituents_codes_by_date.pkl',
        'constituents_codes_by_date.json'
    ]

    print('文件大小:')
    for file in files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            if size > 1024*1024:
                size_str = f'{size/1024/1024:.1f}MB'
            elif size > 1024:
                size_str = f'{size/1024:.1f}KB'
            else:
                size_str = f'{size}B'
            print(f'{file}: {size_str}')
        else:
            print(f'{file}: 文件不存在')


if __name__ == "__main__":
    save_results()
