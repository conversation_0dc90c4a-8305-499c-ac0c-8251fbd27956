#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证和质量检查
验证倒推结果的合理性，检查数据完整性和一致性
"""

import pickle
import pandas as pd
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import os
import sys

# 添加路径
cur_dir = os.path.dirname(os.path.abspath(__file__))
p_dir = os.path.join(cur_dir, '../../')
sys.path.insert(0, p_dir)
from data_utils.trading_calendar import Calendar


def load_all_data():
    """加载所有相关数据"""
    print("加载数据进行验证...")
    
    # 加载倒推结果
    with open('constituents_by_date.pkl', 'rb') as f:
        constituents_by_date = pickle.load(f)
    
    # 加载原始数据
    with open('/tmp/current_constituents.pkl', 'rb') as f:
        current_constituents = pickle.load(f)
    
    with open('/tmp/changes_records.pkl', 'rb') as f:
        changes_records = pickle.load(f)
    
    with open('/tmp/trading_dates.pkl', 'rb') as f:
        trading_dates = pickle.load(f)
    
    return constituents_by_date, current_constituents, changes_records, trading_dates


def validate_data_completeness(constituents_by_date, trading_dates):
    """验证数据完整性"""
    print("\n=== 数据完整性验证 ===")
    
    # 检查是否所有交易日都有数据
    missing_dates = []
    for date in trading_dates:
        if date not in constituents_by_date:
            missing_dates.append(date)
    
    if missing_dates:
        print(f"❌ 缺失数据的交易日: {len(missing_dates)}个")
        print(f"缺失日期: {missing_dates[:10]}...")  # 只显示前10个
        return False
    else:
        print(f"✅ 所有{len(trading_dates)}个交易日都有数据")
    
    # 检查是否有多余的日期
    extra_dates = []
    for date in constituents_by_date.keys():
        if date not in trading_dates:
            extra_dates.append(date)
    
    if extra_dates:
        print(f"⚠️  多余的非交易日数据: {len(extra_dates)}个")
        print(f"多余日期: {extra_dates}")
    else:
        print("✅ 没有多余的非交易日数据")
    
    return len(missing_dates) == 0


def validate_stock_counts(constituents_by_date):
    """验证成分股数量的合理性"""
    print("\n=== 成分股数量验证 ===")
    
    daily_counts = {}
    for date, stocks in constituents_by_date.items():
        daily_counts[date] = len(stocks)
    
    min_count = min(daily_counts.values())
    max_count = max(daily_counts.values())
    
    print(f"成分股数量范围: {min_count} - {max_count}")
    
    # 检查数量变化是否合理（应该在400左右）
    if min_count < 350 or max_count > 450:
        print(f"❌ 成分股数量异常: {min_count} - {max_count}")
        return False
    else:
        print(f"✅ 成分股数量在合理范围内")
    
    # 统计数量分布
    count_distribution = Counter(daily_counts.values())
    print(f"数量分布: {dict(count_distribution)}")
    
    return True


def validate_adjustment_consistency(constituents_by_date, changes_records):
    """验证调整记录的一致性"""
    print("\n=== 调整记录一致性验证 ===")
    
    # 获取调整日期
    adjustment_dates = sorted(changes_records['日期_str'].unique())
    
    validation_errors = []
    
    for adj_date in adjustment_dates:
        print(f"验证调整日期: {adj_date}")
        
        # 获取该日期的调整记录
        day_changes = changes_records[changes_records['日期_str'] == adj_date]
        
        # 找到调整前后的交易日
        prev_date = None
        next_date = None
        
        sorted_dates = sorted(constituents_by_date.keys())
        for i, date in enumerate(sorted_dates):
            if date == adj_date:
                if i > 0:
                    prev_date = sorted_dates[i-1]
                if i < len(sorted_dates) - 1:
                    next_date = sorted_dates[i+1]
                break
        
        if prev_date and next_date:
            prev_stocks = set(stock['代码'] for stock in constituents_by_date[prev_date])
            next_stocks = set(stock['代码'] for stock in constituents_by_date[next_date])
            
            # 计算实际变化
            actual_added = next_stocks - prev_stocks
            actual_removed = prev_stocks - next_stocks
            
            # 计算预期变化
            expected_added = set()
            expected_removed = set()
            
            for _, record in day_changes.iterrows():
                stock_code = record['代码']
                action = record['纳入/剔除']
                
                if action == '纳入':
                    expected_added.add(stock_code)
                elif action == '剔除':
                    expected_removed.add(stock_code)
            
            # 比较实际变化和预期变化
            if actual_added != expected_added:
                error_msg = f"{adj_date}: 实际新增 {actual_added} != 预期新增 {expected_added}"
                validation_errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            
            if actual_removed != expected_removed:
                error_msg = f"{adj_date}: 实际移除 {actual_removed} != 预期移除 {expected_removed}"
                validation_errors.append(error_msg)
                print(f"  ❌ {error_msg}")
            
            if actual_added == expected_added and actual_removed == expected_removed:
                print(f"  ✅ 调整记录一致")
    
    if validation_errors:
        print(f"\n❌ 发现{len(validation_errors)}个一致性错误")
        return False
    else:
        print(f"\n✅ 所有调整记录都一致")
        return True


def validate_latest_constituents(constituents_by_date, current_constituents):
    """验证最新成分股的一致性"""
    print("\n=== 最新成分股一致性验证 ===")
    
    # 获取最新日期的成分股
    latest_date = max(constituents_by_date.keys())
    latest_stocks = set(stock['代码'] for stock in constituents_by_date[latest_date])
    
    # 获取原始最新成分股
    original_stocks = set(current_constituents['代码'].tolist())
    
    print(f"最新日期: {latest_date}")
    print(f"倒推结果成分股数量: {len(latest_stocks)}")
    print(f"原始成分股数量: {len(original_stocks)}")
    
    # 比较差异
    missing_in_result = original_stocks - latest_stocks
    extra_in_result = latest_stocks - original_stocks
    
    if missing_in_result:
        print(f"❌ 倒推结果中缺失的股票: {len(missing_in_result)}只")
        print(f"缺失股票: {list(missing_in_result)[:10]}...")
    
    if extra_in_result:
        print(f"❌ 倒推结果中多余的股票: {len(extra_in_result)}只")
        print(f"多余股票: {list(extra_in_result)[:10]}...")
    
    if not missing_in_result and not extra_in_result:
        print("✅ 最新成分股完全一致")
        return True
    else:
        return False


def generate_validation_report(constituents_by_date, changes_records, trading_dates):
    """生成验证报告"""
    print("\n=== 验证报告 ===")
    
    # 基本统计
    total_trading_days = len(trading_dates)
    total_result_days = len(constituents_by_date)
    
    daily_counts = [len(stocks) for stocks in constituents_by_date.values()]
    min_stocks = min(daily_counts)
    max_stocks = max(daily_counts)
    avg_stocks = sum(daily_counts) / len(daily_counts)
    
    # 调整统计
    total_adjustments = len(changes_records)
    adjustment_dates = changes_records['日期_str'].nunique()
    
    print(f"交易日期间: {min(trading_dates)} - {max(trading_dates)}")
    print(f"总交易日数: {total_trading_days}")
    print(f"结果数据日数: {total_result_days}")
    print(f"成分股数量: {min_stocks} - {max_stocks} (平均: {avg_stocks:.1f})")
    print(f"总调整记录: {total_adjustments}条")
    print(f"调整日期数: {adjustment_dates}个")
    
    # 保存详细报告
    report = {
        'validation_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'period': f"{min(trading_dates)} - {max(trading_dates)}",
        'total_trading_days': total_trading_days,
        'total_result_days': total_result_days,
        'stock_count_range': f"{min_stocks} - {max_stocks}",
        'average_stock_count': avg_stocks,
        'total_adjustments': total_adjustments,
        'adjustment_dates': adjustment_dates
    }
    
    with open('validation_report.pkl', 'wb') as f:
        pickle.dump(report, f)
    
    print(f"\n详细验证报告已保存到: validation_report.pkl")


def main():
    """主验证函数"""
    try:
        # 加载数据
        constituents_by_date, current_constituents, changes_records, trading_dates = load_all_data()
        
        # 执行各项验证
        validation_results = []
        
        # 1. 数据完整性验证
        result1 = validate_data_completeness(constituents_by_date, trading_dates)
        validation_results.append(('数据完整性', result1))
        
        # 2. 成分股数量验证
        result2 = validate_stock_counts(constituents_by_date)
        validation_results.append(('成分股数量', result2))
        
        # 3. 调整记录一致性验证
        result3 = validate_adjustment_consistency(constituents_by_date, changes_records)
        validation_results.append(('调整记录一致性', result3))
        
        # 4. 最新成分股一致性验证
        result4 = validate_latest_constituents(constituents_by_date, current_constituents)
        validation_results.append(('最新成分股一致性', result4))
        
        # 生成验证报告
        generate_validation_report(constituents_by_date, changes_records, trading_dates)
        
        # 总结验证结果
        print(f"\n=== 验证总结 ===")
        all_passed = True
        for test_name, result in validation_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False
        
        if all_passed:
            print(f"\n🎉 所有验证都通过！数据质量良好。")
        else:
            print(f"\n⚠️  部分验证失败，请检查数据质量。")
        
        return all_passed
        
    except Exception as e:
        print(f"验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    result = main()
