#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成分股倒推算法测试代码
验证算法正确性，特别是边界情况处理
"""

import pickle
import pandas as pd
import unittest
from datetime import datetime, timedelta
import os
import sys

# 添加路径
cur_dir = os.path.dirname(os.path.abspath(__file__))
p_dir = os.path.join(cur_dir, '../../')
sys.path.insert(0, p_dir)
from data_utils.trading_calendar import Calendar


class TestConstituentsBacktrack(unittest.TestCase):
    """成分股倒推算法测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        print("加载测试数据...")
        
        # 加载结果数据
        with open('constituents_by_date.pkl', 'rb') as f:
            cls.constituents_by_date = pickle.load(f)
        
        # 加载简化数据
        with open('constituents_codes_by_date.pkl', 'rb') as f:
            cls.constituents_codes = pickle.load(f)
        
        # 加载原始数据
        with open('/tmp/current_constituents.pkl', 'rb') as f:
            cls.current_constituents = pickle.load(f)
        
        with open('/tmp/changes_records.pkl', 'rb') as f:
            cls.changes_records = pickle.load(f)
        
        with open('/tmp/trading_dates.pkl', 'rb') as f:
            cls.trading_dates = pickle.load(f)
        
        print(f"测试数据加载完成: {len(cls.constituents_by_date)}个交易日")
    
    def test_data_completeness(self):
        """测试数据完整性"""
        print("\n测试数据完整性...")
        
        # 检查所有交易日都有数据
        missing_dates = []
        for date in self.trading_dates:
            if date not in self.constituents_by_date:
                missing_dates.append(date)
        
        self.assertEqual(len(missing_dates), 0, f"缺失交易日数据: {missing_dates}")
        
        # 检查没有多余的非交易日数据
        extra_dates = []
        for date in self.constituents_by_date.keys():
            if date not in self.trading_dates:
                extra_dates.append(date)
        
        self.assertEqual(len(extra_dates), 0, f"多余的非交易日数据: {extra_dates}")
        
        print("✅ 数据完整性测试通过")
    
    def test_stock_count_range(self):
        """测试成分股数量范围"""
        print("\n测试成分股数量范围...")
        
        daily_counts = [len(stocks) for stocks in self.constituents_by_date.values()]
        min_count = min(daily_counts)
        max_count = max(daily_counts)
        
        # 成分股数量应该在合理范围内
        self.assertGreaterEqual(min_count, 350, f"最少成分股数量过低: {min_count}")
        self.assertLessEqual(max_count, 450, f"最多成分股数量过高: {max_count}")
        
        print(f"✅ 成分股数量范围测试通过: {min_count} - {max_count}")
    
    def test_latest_consistency(self):
        """测试最新成分股一致性"""
        print("\n测试最新成分股一致性...")
        
        # 获取最新日期的成分股
        latest_date = max(self.constituents_by_date.keys())
        latest_stocks = set(stock['代码'] for stock in self.constituents_by_date[latest_date])
        
        # 获取原始最新成分股
        original_stocks = set(self.current_constituents['代码'].tolist())
        
        # 比较差异
        missing_stocks = original_stocks - latest_stocks
        extra_stocks = latest_stocks - original_stocks
        
        self.assertEqual(len(missing_stocks), 0, f"倒推结果缺失股票: {missing_stocks}")
        self.assertEqual(len(extra_stocks), 0, f"倒推结果多余股票: {extra_stocks}")
        
        print(f"✅ 最新成分股一致性测试通过: {len(latest_stocks)}只股票")
    
    def test_adjustment_logic(self):
        """测试调整逻辑正确性"""
        print("\n测试调整逻辑正确性...")
        
        # 获取调整日期
        adjustment_dates = sorted(self.changes_records['日期_str'].unique())
        
        errors = []
        
        for adj_date in adjustment_dates:
            # 获取该日期的调整记录
            day_changes = self.changes_records[self.changes_records['日期_str'] == adj_date]
            
            # 找到调整前后的交易日
            prev_date = None
            next_date = None
            
            sorted_dates = sorted(self.constituents_by_date.keys())
            for i, date in enumerate(sorted_dates):
                if date == adj_date:
                    if i > 0:
                        prev_date = sorted_dates[i-1]
                    if i < len(sorted_dates) - 1:
                        next_date = sorted_dates[i+1]
                    break
            
            if prev_date and next_date:
                prev_stocks = set(stock['代码'] for stock in self.constituents_by_date[prev_date])
                next_stocks = set(stock['代码'] for stock in self.constituents_by_date[next_date])
                
                # 计算实际变化
                actual_added = next_stocks - prev_stocks
                actual_removed = prev_stocks - next_stocks
                
                # 计算预期变化
                expected_added = set()
                expected_removed = set()
                
                for _, record in day_changes.iterrows():
                    stock_code = record['代码']
                    action = record['纳入/剔除']
                    
                    if action == '纳入':
                        expected_added.add(stock_code)
                    elif action == '剔除':
                        expected_removed.add(stock_code)
                
                # 验证变化一致性
                if actual_added != expected_added:
                    errors.append(f"{adj_date}: 实际新增 {actual_added} != 预期新增 {expected_added}")
                
                if actual_removed != expected_removed:
                    errors.append(f"{adj_date}: 实际移除 {actual_removed} != 预期移除 {expected_removed}")
        
        self.assertEqual(len(errors), 0, f"调整逻辑错误: {errors}")
        
        print(f"✅ 调整逻辑正确性测试通过: 验证了{len(adjustment_dates)}个调整日期")
    
    def test_data_structure_consistency(self):
        """测试数据结构一致性"""
        print("\n测试数据结构一致性...")
        
        # 检查完整数据和简化数据的一致性
        for date in self.constituents_by_date.keys():
            full_codes = set(stock['代码'] for stock in self.constituents_by_date[date])
            simple_codes = set(self.constituents_codes[date])
            
            self.assertEqual(full_codes, simple_codes, 
                           f"日期{date}的完整数据和简化数据不一致")
        
        print("✅ 数据结构一致性测试通过")
    
    def test_stock_code_format(self):
        """测试股票代码格式"""
        print("\n测试股票代码格式...")
        
        invalid_codes = []
        
        for date, stocks in self.constituents_by_date.items():
            for stock in stocks:
                code = stock['代码']
                # 检查股票代码格式 (6位数字.SH/SZ)
                if not (len(code) == 9 and 
                       code[:6].isdigit() and 
                       code[6] == '.' and 
                       code[7:] in ['SH', 'SZ']):
                    invalid_codes.append((date, code))
        
        self.assertEqual(len(invalid_codes), 0, f"无效股票代码: {invalid_codes[:10]}")
        
        print("✅ 股票代码格式测试通过")
    
    def test_boundary_dates(self):
        """测试边界日期"""
        print("\n测试边界日期...")
        
        # 测试第一个交易日
        first_date = min(self.trading_dates)
        self.assertIn(first_date, self.constituents_by_date, "缺少第一个交易日数据")
        
        # 测试最后一个交易日
        last_date = max(self.trading_dates)
        self.assertIn(last_date, self.constituents_by_date, "缺少最后一个交易日数据")
        
        # 测试边界日期的成分股数量
        first_count = len(self.constituents_by_date[first_date])
        last_count = len(self.constituents_by_date[last_date])
        
        self.assertGreater(first_count, 0, "第一个交易日成分股数量为0")
        self.assertGreater(last_count, 0, "最后一个交易日成分股数量为0")
        
        print(f"✅ 边界日期测试通过: {first_date}({first_count}只) - {last_date}({last_count}只)")
    
    def test_no_duplicate_stocks(self):
        """测试无重复股票"""
        print("\n测试无重复股票...")
        
        duplicate_dates = []
        
        for date, stocks in self.constituents_by_date.items():
            codes = [stock['代码'] for stock in stocks]
            if len(codes) != len(set(codes)):
                duplicate_dates.append(date)
        
        self.assertEqual(len(duplicate_dates), 0, f"存在重复股票的日期: {duplicate_dates}")
        
        print("✅ 无重复股票测试通过")


def run_performance_test():
    """运行性能测试"""
    print("\n=== 性能测试 ===")
    
    start_time = datetime.now()
    
    # 加载数据性能测试
    with open('constituents_by_date.pkl', 'rb') as f:
        data = pickle.load(f)
    
    load_time = datetime.now() - start_time
    print(f"数据加载时间: {load_time.total_seconds():.2f}秒")
    
    # 查询性能测试
    start_time = datetime.now()
    
    # 模拟100次随机查询
    import random
    dates = list(data.keys())
    for _ in range(100):
        random_date = random.choice(dates)
        stocks = data[random_date]
        _ = len(stocks)
    
    query_time = datetime.now() - start_time
    print(f"100次查询时间: {query_time.total_seconds():.2f}秒")
    
    print("✅ 性能测试完成")


def main():
    """主测试函数"""
    print("=== 成分股倒推算法测试 ===")
    
    # 运行单元测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    run_performance_test()
    
    print("\n🎉 所有测试完成！")


if __name__ == "__main__":
    main()
