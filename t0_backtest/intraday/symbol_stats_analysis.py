#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按标的统计分析脚本
基于tca.stats_agg_by_sym函数，创建按标的统计脚本
输出类似perf_symbol_weipan_100000_w3.csv的结果
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path

# 添加tca模块路径
sys.path.append('/home/<USER>/trade/t0_backtest/intraday/tca')
import tca

def transform_data_for_symbol_stats(df):
    """
    将sample2.csv格式的数据转换为tca.stats_agg_by_sym函数需要的格式
    """
    print("转换数据格式...")
    
    # 创建转换后的数据框
    transformed_df = df.copy()
    
    # 基础字段映射
    transformed_df['symbol'] = df['code']
    transformed_df['price'] = df['max_amt']  # 使用max_amt作为价格基准
    transformed_df['quantity'] = 1  # 设为1，因为已经是金额
    transformed_df['hold_amt'] = df['max_amt']
    transformed_df['trd_amt'] = df['amt_open']  # 使用开仓金额作为交易金额
    
    # 计算胜率相关字段
    transformed_df['win_round_num_ac'] = (df['profit_ac'] > 0).astype(int) * df['round_num']
    
    # 计算盈利轮次的profit_ac总和
    transformed_df['profit_round_amt_ac'] = np.where(df['profit_ac'] > 0, df['profit_ac'], 0)
    
    print(f"转换后数据形状: {transformed_df.shape}")
    return transformed_df

def calculate_symbol_stats(df):
    """
    按标的分组计算统计指标
    """
    print("按标的计算统计指标...")
    
    symbol_stats = []
    
    for symbol, group in df.groupby('symbol'):
        print(f"处理标的: {symbol}")
        
        # 使用tca.stats_agg_by_sym函数计算统计指标
        try:
            stats = tca.stats_agg_by_sym(group)
            stats['symbol'] = symbol
            symbol_stats.append(stats)
        except Exception as e:
            print(f"计算标的 {symbol} 统计指标时出错: {e}")
            # 手动计算基础统计指标
            stats = calculate_basic_symbol_stats(group)
            stats['symbol'] = symbol
            symbol_stats.append(stats)
    
    return pd.DataFrame(symbol_stats)

def calculate_basic_symbol_stats(group):
    """
    手动计算基础的标的统计指标
    """
    stats = {}
    
    # 基础指标
    stats['hold_amt'] = (group['price'] * group['quantity']).mean()
    stats['fee'] = group['fee'].sum()
    stats['trd_amt'] = group['trd_amt'].sum()
    
    # 比率指标
    if stats['hold_amt'] > 0:
        stats['turnover'] = stats['trd_amt'] / stats['hold_amt']
    else:
        stats['turnover'] = 0
    
    stats['profit'] = group['profit'].sum()
    stats['profit_ac'] = stats['profit'] - stats['fee']
    
    if stats['hold_amt'] > 0:
        stats['ret2hold'] = stats['profit'] / stats['hold_amt']
        stats['ret2hold_ac'] = stats['profit_ac'] / stats['hold_amt']
    else:
        stats['ret2hold'] = 0
        stats['ret2hold_ac'] = 0
    
    # 轮次相关指标
    stats['round_num'] = group['round_num'].sum()
    
    # 胜率计算
    win_rounds_ac = group[group['profit_ac'] > 0]['round_num'].sum()
    
    if stats['round_num'] > 0:
        stats['win_rate_ac'] = win_rounds_ac / stats['round_num']
    else:
        stats['win_rate_ac'] = 0
    
    # 盈亏比计算
    win_profit_ac = group[group['profit_ac'] > 0]['profit_ac']
    loss_profit_ac = group[group['profit_ac'] < 0]['profit_ac']
    
    if len(win_profit_ac) > 0 and len(loss_profit_ac) > 0:
        avg_win = win_profit_ac.sum() / len(win_profit_ac)
        avg_loss = abs(loss_profit_ac.sum() / len(loss_profit_ac))
        stats['win_loss_ratio_ac'] = avg_win / avg_loss if avg_loss > 0 else 0
    else:
        stats['win_loss_ratio_ac'] = 0
    
    return stats

def save_symbol_stats(symbol_stats_df, output_file):
    """
    保存按标的统计结果
    """
    print(f"保存结果到: {output_file}")
    
    # 确保输出目录存在
    output_dir = Path(output_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存CSV文件
    symbol_stats_df.to_csv(output_file, index=False)
    print(f"已保存 {len(symbol_stats_df)} 条标的统计记录")

def main(input_file, output_file):
    """
    主函数
    """
    print(f"=== 按标的统计分析 ===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    # 读取数据
    print("读取数据...")
    df = pd.read_csv(input_file)
    print(f"原始数据形状: {df.shape}")
    
    # 过滤有交易的记录
    df_traded = df[df['round_num'] > 0].copy()
    print(f"有交易记录数: {len(df_traded)}")
    
    if len(df_traded) == 0:
        print("没有交易记录，无法进行统计")
        return None
    
    # 转换数据格式
    transformed_df = transform_data_for_symbol_stats(df_traded)
    
    # 计算按标的统计
    symbol_stats_df = calculate_symbol_stats(transformed_df)
    
    # 保存结果
    save_symbol_stats(symbol_stats_df, output_file)
    
    print("=== 按标的统计分析完成 ===")
    return symbol_stats_df

if __name__ == "__main__":
    # 默认参数
    input_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/sample2.csv"
    output_file = "/home/<USER>/trade/t0_backtest/intraday/results/symbol_stats_sample.csv"
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    # 执行分析
    symbol_stats_df = main(input_file, output_file)
