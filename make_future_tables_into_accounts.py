#%%
import os
import sys
from loguru import logger
from datetime import datetime
from tabulate import tabulate

from public import *
from accounts_config.kf_accounts_basic import zs_accounts_dict
from misc.Readstockfile import write_file, read_file, read_remote_file
from misc.standard_table import standardize_margindeal
from data_utils.trading_calendar import Calendar
#%%
# date = '********'
# pre_date = '********'
#%%
if len(sys.argv) > 1:
    date = str(sys.argv[1])
else:
    date = datetime.now().strftime('%Y%m%d')

if not Calendar.is_trading_day(date):
    logger.warning('Not a trading day')
    sys.exit(0)
pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')

#%%
# 基于每日导出的hold 和deal 生成期货hold 和deal 序列
# def make_future_hold_deal_files_margin_account(account_name, date, client_type):
#     config = kf_accounts_dict[account_name]

#     def get_future_from_margindeal(df):
#         df = df.rename(columns={
#             '成交日期':'date',
#             '成交时间':'time',
#             '代码':'ticker',
#             '多空状态':'longshort',
#             '开平方向':'operation',
#             '成交均价':'price',
#             '成交数量':'volume',
#             '合同编号':'order_ref',
#             '成交编号':'deal_ref',
#             '算法':'algorithm',
#         })

#         is_future_mask = df['ticker'].apply(lambda x: re_find('^IC|^IF|^IH|^IM', x))
#         df = df[is_future_mask]
#         if df.empty:
#             return pd.DataFrame(columns=['date', 'time', 'ticker', 'longshort', 'operation', 'price', 'volume', 'order_ref', 'deal_ref'])
#         df = df[['date', 'time', 'ticker', 'longshort', 'operation', 'price', 'volume', 'order_ref', 'deal_ref']]
#         df.reset_index(drop=True, inplace=True)
#         return df

#     # hold file
#     hold_file = config['hold_file']
#     mhold_path = os.path.join(account_name, 'hold', f'hold_origin_{date}.xls')
#     # Mhold = MarginHold(source_type='dav', source_path=mhold_path.format(date), date=date, client_type=client_type)
#     Mhold = MarginHold(source_type='dav', 
#                        source_path=  mhold_path, 
#                        date=date, client_type=client_type)
#     Mhold.update_data()
#     Mhold.summarize()
#     print(Mhold.future.head(2))

#     future_hold_save_path = os.path.join('期货账户', account_name, 'futurehold_{}.xls')

#     # write to hold file @ dav
#     write_file(Mhold.future, 
#             file_type='xls', 
#             dest_type='dav', 
#             dest_path=future_hold_save_path.format(date),
#             index=False
#             )

#     # deal file
#     deal_file = config['deal_file'] 
#     mdeal_path = os.path.join(account_name, 'account', f'deal_origin_{date}.xls')

#     Mdeal = read_remote_file(mdeal_path, 
#                             src_type='dav',
#                             dtype={
#                                 'OrderId':str,
#                                 'BizId':str,
#                                 'StockCode':str,
#                                 '成交编号':str,
#                                 '意向编号':str,
#                                 }
#                             )
#     Mdeal = standardize_margindeal(Mdeal, date=date, client_type=client_type)

#     future_deal = get_future_from_margindeal(Mdeal)
#     future_deal['file_type'] = 'real'
#     print(future_deal.head(2))

#     if not future_deal.empty:
#         future_deal_save_path = os.path.join(
#             '期货账户',
#             account_name,
#             'futuredeal_{}.xls'
#         )
#         write_file(future_deal,
#                 file_type='xls',
#                 dest_type='dav',
#                 dest_path=future_deal_save_path.format(date),
#                 index=False
#                 )

#%%

# 基于每日gtrade导出的hold 和deal(或有) 生成期货hold 和deal 序列
def make_future_hold_deal_files_gtrade_future_account(account_name, date, proxy_trades):

    # # hold file
    # hold_file = 'Position-{}.csv'
    # mhold_path = os.path.join('期货账户', account_name, hold_file)    
    # Mhold = MarginHold(source_type='dav', source_path=mhold_path.format(date), date=date)
    # Mhold.update_data()
    # Mhold.summarize()
    # print(Mhold.future.head(2))

    # future_hold_save_path = os.path.join('期货账户', account_name, 'futurehold_{}.xls')

    # # write to hold file @ dav
    # write_file(Mhold.future, 
    #         file_type='xls', 
    #         dest_type='dav', 
    #         dest_path=future_hold_save_path.format(date),
    #         index=False
    #         )
    
    # deal file
    print('\n---------------')
    print(f'处理 {account_name}')
    deal_file = 'Deal-{}.csv'
    mdeal_path = os.path.join('期货账户', account_name, deal_file)

    try:
        Mdeal = MarginDeal(source_type='dav', source_path=mdeal_path.format(date), date=date, fullcols=True)
        Mdeal.update_data()
        Mdeal.summarize()
        future_deal = Mdeal.future
    except FileNotFoundError:
        logger.warning('No deal file for {}'.format(date))
        future_deal = pd.DataFrame(columns=['date', 'time', 'ticker', 'longshort', 'operation', 'price', 'volume', 'order_ref', 'deal_ref', 'file_type'])

    future_deal['file_type'] = 'real'
    print(future_deal.head(2))
    
    # 合并代下交易
    account_proxy_trades = proxy_trades[proxy_trades['account_name'] == account_name].copy().reset_index(drop=True)
    if not account_proxy_trades.empty:
        proxy_trades_taken_list.append(account_name)
        account_proxy_trades['order_ref'] = 'order_'  + pd.Series(range(1, len(account_proxy_trades) + 1)).astype(str)
        account_proxy_trades['deal_ref'] = 'deal_' + pd.Series(range(1, len(account_proxy_trades) + 1)).astype(str)
        account_proxy_trades['file_type'] = 'real'
        future_deal = pd.concat([future_deal, account_proxy_trades])
        future_deal['account_name'] = account_name
    
    if not future_deal.empty:
        future_deal_save_path = os.path.join(
            '期货账户',
            account_name,
            'futuredeal_{}.csv'
        )
        write_file(future_deal,
                file_type='xls',
                dest_type='dav',
                dest_path=future_deal_save_path.format(date),
                index=False
                )
    
    
    ################# new  get pre hold
    future_hold_path = os.path.join('期货账户', account_name, 'futurehold_{}.csv')

    pre_hold = FutureHold(
        source_type='dav', 
        source_path=future_hold_path.format(pre_date), 
        date=pre_date,
        future_price=pre_future_price
        )
    pre_hold.update_data()
    pre_hold.summarize()

    print(pre_hold.future.head(2))

    futurehold = pre_hold.future

    sum_futuredeal = future_deal[['ticker', 'longshort', 'operation', 'volume']].groupby(['ticker', 'longshort', 'operation']).sum()
    sum_futuredeal.reset_index(inplace=True)
    sum_futuredeal['volume'] = sum_futuredeal['operation'].apply(lambda x: 1 if x == '开仓' else -1) * sum_futuredeal['volume']


    newhold = pd.concat([futurehold, sum_futuredeal])
    newhold = newhold[['ticker', 'longshort', 'volume']].groupby(['ticker', 'longshort']).sum().reset_index()
    
    newhold.fillna(0, inplace=True)
    newhold = FutureHold(source_type='df', data=newhold, date=date, future_price=future_price)
    newhold.update_data()
    newhold.summarize()
    
    print(newhold.future.head(2))
    save_hold_path = os.path.join('期货账户', account_name, 'futurehold_{}.csv')
    write_file(newhold.future, file_type='xls', dest_type='dav', dest_path=save_hold_path.format(date), index=False)
    ####################



#%%
# 基于pre hold 和手动生成的deal 生成期货hold 和deal 序列
def make_future_hold_deal_from_prehold(account_name, date, pre_date, proxy_trades):
    print('\n---------------')
    print(f'处理 {account_name}')
    future_hold_path = os.path.join('期货账户', account_name, 'futurehold_{}.csv')

    pre_hold = FutureHold(
        source_type='dav', 
        source_path=future_hold_path.format(pre_date), 
        date=pre_date,
        future_price=pre_future_price
        )
    pre_hold.update_data()
    pre_hold.summarize()

    print(pre_hold.future.head(2))

    futurehold = pre_hold.future

    # try:
    account_proxy_trades = proxy_trades[proxy_trades['account_name'] == account_name].copy().reset_index(drop=True)
    if not account_proxy_trades.empty:
        # future_deal_path = os.path.join('期货账户', account_name, 'futuredeal_{}.xls')
        # deal = FutureDeal(source_type='dav', source_path=future_deal_path.format(date), date=date)
        proxy_trades_taken_list.append(account_name)
        account_proxy_trades['order_ref'] = 'order_'  + pd.Series(range(1, len(account_proxy_trades) + 1)).astype(str)
        account_proxy_trades['deal_ref'] = 'deal_' + pd.Series(range(1, len(account_proxy_trades) + 1)).astype(str)
        account_proxy_trades['file_type'] = 'real'
        
        deal = FutureDeal(source_type='df', data=account_proxy_trades, date=date)
        deal.update_data()
        deal.summarize()
        futuredeal = deal.future
        # save deal to account dir
        write_file(account_proxy_trades, file_type='xls', dest_type='dav', 
                   dest_path=os.path.join('期货账户', account_name, f'futuredeal_{date}.csv'),
                   index=False)
    # except FileNotFoundError:
    else:
        logger.warning('No deal file for {}'.format(date))
        futuredeal = pd.DataFrame(columns=['date', 'time', 'ticker', 'longshort', 'operation', 'price', 'volume', 'order_ref', 'deal_ref', 'file_type'])
        # save_deal_path = os.path.join('期货账户', account_name, 'futuredeal_{}.xls')
        # write_file(futuredeal, file_type='xls', dest_type='dav', dest_path=save_deal_path.format(date), index=False)
        
    print(futuredeal.head(2))
    
    sum_futuredeal = futuredeal[['ticker', 'longshort', 'operation', 'volume']].groupby(['ticker', 'longshort', 'operation']).sum()
    sum_futuredeal.reset_index(inplace=True)
    sum_futuredeal['volume'] = sum_futuredeal['operation'].apply(lambda x: 1 if x == '开仓' else -1) * sum_futuredeal['volume']


    newhold = pd.concat([futurehold, sum_futuredeal])
    newhold = newhold[['ticker', 'longshort', 'volume']].groupby(['ticker', 'longshort']).sum().reset_index()
    
    newhold.fillna(0, inplace=True)
    newhold = FutureHold(source_type='df', data=newhold, date=date, future_price=future_price)
    newhold.update_data()
    newhold.summarize()
    
    print(newhold.future.head(2))
    save_hold_path = os.path.join('期货账户', account_name, 'futurehold_{}.csv')
    write_file(newhold.future, file_type='xls', dest_type='dav', dest_path=save_hold_path.format(date), index=False)
#%%
# 基于pre hold 和 指定的deal源 生成期货hold 和deal 序列
# def make_future_hold_deal_from_prehold_and_dealsource(account_name, date, pre_date):
#     future_hold_path = os.path.join('期货账户', account_name, 'futurehold_{}.xls')

#     pre_hold = FutureHold(source_type='dav', source_path=future_hold_path.format(pre_date), date=pre_date)
#     pre_hold.update_data()
#     pre_hold.summarize()

#     print(pre_hold.future.head(2))

#     futurehold = pre_hold.future

#     try:
#         future_deal_path = os.path.join(account_name, 'account', 'Deal-{}.csv')
#         deal = FutureDeal(source_type='dav', source_path=future_deal_path.format(date), date=date)
#         deal.update_data()
#         deal.summarize()
#         futuredeal = deal.future
#     except FileNotFoundError:
#         logger.warning('No deal file for {}'.format(date))
#         futuredeal = pd.DataFrame(columns=['date', 'time', 'ticker', 'longshort', 'operation', 'fill_price', 'volume', 'order_ref', 'deal_ref', 'file_type'])
#         save_deal_path = os.path.join('期货账户', account_name, 'futuredeal_{}.xls')
#         write_file(futuredeal, file_type='xls', dest_type='dav', dest_path=save_deal_path.format(date), index=False)
        
#     print(futuredeal.head(2))
    
#     sum_futuredeal = futuredeal[['ticker', 'longshort', 'operation', 'volume']].groupby(['ticker', 'longshort', 'operation']).sum()
#     sum_futuredeal.reset_index(inplace=True)
#     sum_futuredeal['volume'] = sum_futuredeal['operation'].apply(lambda x: 1 if x == '开仓' else -1) * sum_futuredeal['volume']


#     newhold = pd.concat([futurehold, sum_futuredeal])
#     newhold = newhold[['ticker', 'longshort', 'volume']].groupby(['ticker', 'longshort']).sum().reset_index()
    
#     newhold.fillna(0, inplace=True)
#     newhold = FutureHold(source_type='df', data=newhold, date=date)
#     newhold.update_data()
#     newhold.summarize()
    
#     print(newhold.future.head(2))
#     save_hold_path = os.path.join('期货账户', account_name, 'futurehold_{}.xls')
#     write_file(newhold.future, file_type='xls', dest_type='dav', dest_path=save_hold_path.format(date), index=False)

#%%
margin_accounts = {
    # 可能需要手动干预的
    
    # '远航安心中性6号_中信'  :  'zhongxin_cats',
    # '远航安心中性1号_招商'  :  None,
}

gtrade_future_accounts = [
    # '远航安心中性1号',
    # '宽辅泓涛专享1号',
    # '宽辅专享1号',
]
        
virtual_future_accounts = [
    # '远航安心中性6号_国联',
    # '宽辅泓涛专享1号_广发',     #  :  None,
    # '远航安心中性1号_国信指令',
    # '远航安心中性1号_银河',
    # '远航安心中性3号',
    # '宽辅泓涛专享1号_东方',
    # '远航安心中性6号_浙商',
    # '远航安心中性6号_国君',
    # '宽辅专享1号_浙商',
    # '远航安心中性6号_银河',
    # '宽辅联丰专享_国联',
    # '宽辅专享5号',
    '超量子期货'
]

    
# for account_name in margin_accounts.keys():
#     client_type = margin_accounts[account_name]
#     make_future_hold_deal_files_margin_account(account_name, date, client_type=client_type)

def merged_future_price(date):
    future_price = get_future_price(date)
    main_future_close = get_main_index_close(date)
    main_future_close['pre_settle'] = main_future_close['pre_close']
    main_future_close['settle'] = main_future_close['close']
    future_price = pd.concat([main_future_close, future_price])
    return future_price

future_price = merged_future_price(date)
pre_future_price = merged_future_price(pre_date)



try:
    proxy_future_trades = read_remote_file(os.path.join('期货汇总', '期货代下汇总', f'期货代下汇总_{date}.csv'), src_type='dav', dtype={'date':'str'})
except FileNotFoundError:
    logger.warning('No proxy future trades file for {}'.format(date))
    proxy_future_trades = pd.DataFrame(columns=['date', 'time', 'ticker', 'longshort', 'operation', 'price', 'volume', 'account_name'])


# 宽辅泓涛专享1号_广发
try:
    gf_dma_trades = read_remote_file(os.path.join('数据导出', '宽辅泓涛专享1号_广发', date, 'TradeList.csv'), src_type='trade', dtype={})
    gf_dma_trades['cffex_mask'] = gf_dma_trades['标的代码'].apply(lambda x: re_find(r'\.CFFEX$', x))
    gf_dma_trades = gf_dma_trades[gf_dma_trades['cffex_mask']]
    if not gf_dma_trades.empty:
        gf_dma_trades.rename(columns={
            '成交时间':'time',
            '标的代码':'ticker',
            '多空方向':'longshort',
            '开平标志':'operation',
            '成交价格':'price',
            '成交数量':'volume',
        }, inplace=True)
        gf_dma_trades['date'] = date
        gf_dma_trades['time'] = pd.to_datetime(gf_dma_trades['time']).apply(lambda x: x.strftime('%H:%M:%S'))
        gf_dma_trades['account_name'] = '宽辅泓涛专享1号_广发'
        gf_dma_trades['ticker'] = gf_dma_trades['ticker'].apply(lambda x: x.split('.')[0])
        gf_dma_trades['longshort'] = gf_dma_trades['longshort'].map({'看多':'多头', '看空':'空头'})
        
        gf_dma_trades = gf_dma_trades[['date', 'time', 'ticker', 'longshort', 'operation', 'price', 'volume', 'account_name']]
    else:
        gf_dma_trades = pd.DataFrame(columns=['date', 'time', 'ticker', 'longshort', 'operation', 'price', 'volume', 'account_name'])
except FileNotFoundError:
    logger.warning('No 泓涛1号广发 trades file for {}'.format(date))
    gf_dma_trades = pd.DataFrame(columns=['date', 'time', 'ticker', 'longshort', 'operation', 'price', 'volume', 'account_name'])


proxy_future_trades = pd.concat([proxy_future_trades, gf_dma_trades])
proxy_trades_taken_list = []


for account_name in gtrade_future_accounts:
    make_future_hold_deal_files_gtrade_future_account(account_name, date, proxy_future_trades)
    
for account_name in virtual_future_accounts:
    make_future_hold_deal_from_prehold(account_name, date, pre_date, proxy_future_trades)

proxy_future_trades = proxy_future_trades[~proxy_future_trades['account_name'].isin(proxy_trades_taken_list)]
if not proxy_future_trades.empty:
    logger.error('期货代下汇总表中未处理的行数: {}, 未处理的账户: {}'.format(
        len(proxy_future_trades),
        proxy_future_trades['account_name'].unique()
    ))
    
#%%
# tmp 
# from data_utils.trading_calendar import Calendar
# from datetime import datetime
# account_name = '远航安心中性3号'
# start_date = '********'
# end_date = '********'
# tradingday = Calendar.next_trading_day(start_date)
# while tradingday <= datetime.date(datetime.strptime(end_date, '%Y%m%d')):
#     date = tradingday.strftime('%Y%m%d')
#     pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
#     make_future_hold_deal_from_prehold(account_name, date, pre_date)
#     tradingday = Calendar.next_trading_day(date)

# %%
