import leveldb
import datetime
import json
import pandas as pd
from utils import mysql
import os
def read_algo_data(p):
    ldb=leveldb.LevelDB(p)
    l=[]
    datas=list(ldb.RangeIter())
    # true='true'
    pos=[]
    for k,v in datas:
        d=json.loads(v)
        pos.append(d) 
    po_df=pd.DataFrame(pos)
    ldb=None
    return po_df 

def operation_map(x):
    if x==100:
        return 0
    if x==110:
        return 1
    return None

def status_map(s):
    if s==0:
        return 1
    if s==10:
        return 2
    if s==20:
        return 2
    if s==30:
        return 3
    if s==40:
        return 5
    if s==100:
        return 6
    if s==110:
        return 7
    if s==120:
        return 9
    return 0

date="20250605"
def dump_trading_algo_parentorders(date):
    pt=r'/data/shared-data/public/algo_trading_data/xuntou/parentorder_{}.ldb'.format(date)
    df=read_algo_data(pt)
    df['id']=df['id']+date
    df['create_time']=(pd.to_datetime(df['create_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)
    df['start_time']=(pd.to_datetime(df['start_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)
    df['end_time']=(pd.to_datetime(df['end_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)
    df['operation']=df['operation'].apply(operation_map)
    df['status']=df['status'].apply(status_map)
    df['firm']='unknown'
    df['algo_provider']='zhishu'
    df['pm']='xj'
    df['date']=date
    df['broker']='unknown'
    df['sys_type']='xuntou'
    df['remark1']=''
    df['remark2']=''
    df['remark3']=''
    df=df[['id','symbol','quantity','operation','create_time','start_time','end_time','filled_quantity','filled_price','algo_name','account_id','firm','algo_provider','pm','date','broker','params','sys_type','remark1','remark2','remark3']]
    mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),df,"algo_parentorder",{'date':date,'sys_type':'xuntou'})

def dump_orders(date):
    pt=r'/data/shared-data/public/algo_trading_data/xuntou/order_{}.ldb'.format(date)
    df=read_algo_data(pt)
    df['pid']=df['pid']+date
    df['create_time']=(pd.to_datetime(df['create_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)
    df['last_upd_time']=(pd.to_datetime(df['last_upd_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)
    df['operation']=df['operation'].apply(operation_map)
    df['status']=df['status'].apply(status_map)
    df['firm']='unknown'
    df['algo_provider']='zhishu'
    df['pm']='xj'
    df['date']=date
    df['broker']='unknown'
    df['sys_type']='xuntou'
    df['remark1']=''
    df['remark2']=''
    df['err_msg']=''
    df=df[['id','pid','symbol','create_time','last_upd_time','account_id','operation','price','quantity','filled_price','filled_quantity','status','order_id','order_type','err_msg','remark1','remark2']]
    mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),df,"orders",{'id':tuple(df['id'].values)})

if __name__=="__main__":
    for fn in os.listdir(r"/data/shared-data/public/algo_trading_data/xuntou/"):
        if fn.split("_")[0]=='order':
            date=fn.split("_")[1].split(".")[0]
            print(date)
            if date=='********':
                continue
            dump_trading_algo_parentorders(date)
            dump_orders(date)