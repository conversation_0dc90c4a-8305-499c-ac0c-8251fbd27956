#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用处理后的CFFEX期货数据示例
"""

import pandas as pd
import numpy as np

def load_cffex_data(file_path="/home/<USER>/trade/account_stat/MarketData_CFFEX_cleaned.csv"):
    """
    加载处理后的CFFEX期货数据
    
    Args:
        file_path (str): 数据文件路径
    
    Returns:
        pd.DataFrame: 清洗后的数据框
    """
    df = pd.read_csv(file_path)
    
    # 确保数据类型正确
    df['TradingDay'] = df['TradingDay'].astype(str)
    df['InstrumentID'] = df['InstrumentID'].astype(str)
    
    return df

def analyze_data(df):
    """
    分析期货数据
    
    Args:
        df (pd.DataFrame): 期货数据
    """
    print("=== CFFEX期货数据分析 ===")
    print(f"数据总行数: {len(df)}")
    print(f"交易日期: {df['TradingDay'].unique()}")
    print(f"合约总数: {df['InstrumentID'].nunique()}")
    
    # 按合约类型分组
    print("\n=== 合约类型统计 ===")
    contract_types = df['InstrumentID'].str[:2].value_counts()
    print(contract_types)
    
    # 成交量统计
    print("\n=== 成交量统计 ===")
    volume_stats = df['Volume'].describe()
    print(volume_stats)
    
    # 成交额统计
    print("\n=== 成交额统计 ===")
    turnover_stats = df['Turnover'].describe()
    print(turnover_stats)
    
    # 价格统计
    print("\n=== 价格统计 ===")
    price_cols = ['OpenPrice', 'HighestPrice', 'LowestPrice', 'ClosePrice', 'LastPrice']
    for col in price_cols:
        if col in df.columns:
            print(f"{col}: 均值={df[col].mean():.2f}, 最大值={df[col].max():.2f}, 最小值={df[col].min():.2f}")

def filter_by_conditions(df):
    """
    根据条件筛选数据的示例
    
    Args:
        df (pd.DataFrame): 期货数据
    
    Returns:
        pd.DataFrame: 筛选后的数据
    """
    # 示例：筛选成交量大于1000的合约
    high_volume = df[df['Volume'] > 1000]
    print(f"\n=== 成交量>1000的合约数量: {len(high_volume)} ===")
    
    # 示例：筛选特定合约类型
    if_contracts = df[df['InstrumentID'].str.startswith('IF')]
    print(f"IF合约数量: {len(if_contracts)}")
    
    # 示例：筛选有价格数据的合约
    with_price = df[df['ClosePrice'].notna()]
    print(f"有收盘价数据的合约数量: {len(with_price)}")
    
    return high_volume

def main():
    """主函数"""
    try:
        # 加载数据
        print("正在加载CFFEX期货数据...")
        df = load_cffex_data()
        
        # 分析数据
        analyze_data(df)
        
        # 筛选示例
        filtered_data = filter_by_conditions(df)
        
        # 显示前几行数据
        print("\n=== 数据样例 ===")
        print(df.head())
        
        print("\n数据加载和分析完成！")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
