{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import monitor\n", "import datetime"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'first'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accountId</th>\n", "      <th>operation</th>\n", "      <th>quantity</th>\n", "      <th>filledQuantity</th>\n", "      <th>cmpQty</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2____10313____111____49____072000002135____</td>\n", "      <td>BUY_TO_OPEN</td>\n", "      <td>299500</td>\n", "      <td>155300</td>\n", "      <td>0.518531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2____10313____111____49____072000002135____</td>\n", "      <td>SELL_TO_CLOSE</td>\n", "      <td>197290</td>\n", "      <td>129230</td>\n", "      <td>0.655026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2____10355____10355____49____8883558888____</td>\n", "      <td>BUY_TO_OPEN</td>\n", "      <td>130500</td>\n", "      <td>120800</td>\n", "      <td>0.925670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2____10355____10355____49____8883558888____</td>\n", "      <td>SELL_TO_CLOSE</td>\n", "      <td>114540</td>\n", "      <td>102040</td>\n", "      <td>0.890868</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     accountId      operation  quantity  \\\n", "0  2____10313____111____49____072000002135____    BUY_TO_OPEN    299500   \n", "1  2____10313____111____49____072000002135____  SELL_TO_CLOSE    197290   \n", "2  2____10355____10355____49____8883558888____    BUY_TO_OPEN    130500   \n", "3  2____10355____10355____49____8883558888____  SELL_TO_CLOSE    114540   \n", "\n", "   filledQuantity    cmpQty  \n", "0          155300  0.518531  \n", "1          129230  0.655026  \n", "2          120800  0.925670  \n", "3          102040  0.890868  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'second ERROR/CANCELED/EXPIRED'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>symbol</th>\n", "      <th>quantity</th>\n", "      <th>operation</th>\n", "      <th>filledQuantity</th>\n", "      <th>filledPrice</th>\n", "      <th>status</th>\n", "      <th>accountId</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>550</th>\n", "      <td>0000072000002135_1749_749_1075@GRZQQMT</td>\n", "      <td>300619</td>\n", "      <td>30</td>\n", "      <td>SELL_TO_CLOSE</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>EXPIRED</td>\n", "      <td>2____10313____111____49____072000002135____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>0000072000002135_1657_657_983@GRZQQMT</td>\n", "      <td>002300</td>\n", "      <td>200</td>\n", "      <td>SELL_TO_CLOSE</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>EXPIRED</td>\n", "      <td>2____10313____111____49____072000002135____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>0000072000002135_1182_182_508@GRZQQMT</td>\n", "      <td>002722</td>\n", "      <td>100</td>\n", "      <td>BUY_TO_OPEN</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>EXPIRED</td>\n", "      <td>2____10313____111____49____072000002135____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>0000072000002135_1250_250_576@GRZQQMT</td>\n", "      <td>300246</td>\n", "      <td>100</td>\n", "      <td>BUY_TO_OPEN</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>EXPIRED</td>\n", "      <td>2____10313____111____49____072000002135____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>0000072000002135_1321_321_647@GRZQQMT</td>\n", "      <td>300647</td>\n", "      <td>100</td>\n", "      <td>BUY_TO_OPEN</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>EXPIRED</td>\n", "      <td>2____10313____111____49____072000002135____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1783</th>\n", "      <td>00008883558888_1007_7_126@guojinQMT</td>\n", "      <td>002339</td>\n", "      <td>1100</td>\n", "      <td>BUY_TO_OPEN</td>\n", "      <td>900</td>\n", "      <td>7.84</td>\n", "      <td>EXPIRED</td>\n", "      <td>2____10355____10355____49____8883558888____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1785</th>\n", "      <td>00008883558888_1008_8_568@guojinQMT</td>\n", "      <td>300243</td>\n", "      <td>300</td>\n", "      <td>SELL_TO_CLOSE</td>\n", "      <td>200</td>\n", "      <td>10.78</td>\n", "      <td>EXPIRED</td>\n", "      <td>2____10355____10355____49____8883558888____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1795</th>\n", "      <td>00008883558888_1008_8_799@guojinQMT</td>\n", "      <td>002430</td>\n", "      <td>300</td>\n", "      <td>SELL_TO_CLOSE</td>\n", "      <td>200</td>\n", "      <td>19.59</td>\n", "      <td>EXPIRED</td>\n", "      <td>2____10355____10355____49____8883558888____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1808</th>\n", "      <td>00008883558888_1008_8_864@guojinQMT</td>\n", "      <td>301266</td>\n", "      <td>200</td>\n", "      <td>SELL_TO_CLOSE</td>\n", "      <td>100</td>\n", "      <td>31.36</td>\n", "      <td>EXPIRED</td>\n", "      <td>2____10355____10355____49____8883558888____</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1811</th>\n", "      <td>00008883558888_1007_7_84@guojinQMT</td>\n", "      <td>002209</td>\n", "      <td>300</td>\n", "      <td>BUY_TO_OPEN</td>\n", "      <td>200</td>\n", "      <td>12.05</td>\n", "      <td>EXPIRED</td>\n", "      <td>2____10355____10355____49____8883558888____</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>503 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                                          id  symbol  quantity      operation  \\\n", "550   0000072000002135_1749_749_1075@GRZQQMT  300619        30  SELL_TO_CLOSE   \n", "165    0000072000002135_1657_657_983@GRZQQMT  002300       200  SELL_TO_CLOSE   \n", "12     0000072000002135_1182_182_508@GRZQQMT  002722       100    BUY_TO_OPEN   \n", "16     0000072000002135_1250_250_576@GRZQQMT  300246       100    BUY_TO_OPEN   \n", "17     0000072000002135_1321_321_647@GRZQQMT  300647       100    BUY_TO_OPEN   \n", "...                                      ...     ...       ...            ...   \n", "1783     00008883558888_1007_7_126@guojinQMT  002339      1100    BUY_TO_OPEN   \n", "1785     00008883558888_1008_8_568@guojinQMT  300243       300  SELL_TO_CLOSE   \n", "1795     00008883558888_1008_8_799@guojinQMT  002430       300  SELL_TO_CLOSE   \n", "1808     00008883558888_1008_8_864@guojinQMT  301266       200  SELL_TO_CLOSE   \n", "1811      00008883558888_1007_7_84@guojinQMT  002209       300    BUY_TO_OPEN   \n", "\n", "      filledQuantity  filledPrice   status  \\\n", "550                0         0.00  EXPIRED   \n", "165                0         0.00  EXPIRED   \n", "12                 0         0.00  EXPIRED   \n", "16                 0         0.00  EXPIRED   \n", "17                 0         0.00  EXPIRED   \n", "...              ...          ...      ...   \n", "1783             900         7.84  EXPIRED   \n", "1785             200        10.78  EXPIRED   \n", "1795             200        19.59  EXPIRED   \n", "1808             100        31.36  EXPIRED   \n", "1811             200        12.05  EXPIRED   \n", "\n", "                                        accountId  \n", "550   2____10313____111____49____072000002135____  \n", "165   2____10313____111____49____072000002135____  \n", "12    2____10313____111____49____072000002135____  \n", "16    2____10313____111____49____072000002135____  \n", "17    2____10313____111____49____072000002135____  \n", "...                                           ...  \n", "1783  2____10355____10355____49____8883558888____  \n", "1785  2____10355____10355____49____8883558888____  \n", "1795  2____10355____10355____49____8883558888____  \n", "1808  2____10355____10355____49____8883558888____  \n", "1811  2____10355____10355____49____8883558888____  \n", "\n", "[503 rows x 8 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["'third running'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>symbol</th>\n", "      <th>quantity</th>\n", "      <th>operation</th>\n", "      <th>filledQuantity</th>\n", "      <th>filledPrice</th>\n", "      <th>status</th>\n", "      <th>accountId</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [id, symbol, quantity, operation, filledQuantity, filledPrice, status, accountId]\n", "Index: []"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mmonitor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmonitor\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/trade/xuntou/monitor.py:61\u001b[39m, in \u001b[36mmonitor\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m     59\u001b[39m display.display(\u001b[33m\"\u001b[39m\u001b[33mthird running\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     60\u001b[39m display.display(runningAlgoDf)\n\u001b[32m---> \u001b[39m\u001b[32m61\u001b[39m \u001b[43mtime\u001b[49m\u001b[43m.\u001b[49m\u001b[43msleep\u001b[49m\u001b[43m(\u001b[49m\u001b[32;43m60\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["monitor.monitor()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}