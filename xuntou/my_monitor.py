import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import datetime
import time
import json
import requests
from typing import Dict, List, Any
import numpy as np


acconts_alias = {
    '2____10336____103361____49____0650032828____' : '超量子-中泰',
    '2____10313____111____49____072000002135____'  : 'kuanfu-国融',
    '2____10355____10355____49____8883558888____'  : 'wuzhi-国金',
}




# 设置页面配置
st.set_page_config(
    page_title="交易监控系统",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

def timestramp2datetime(ts):
    """时间戳转换为datetime对象"""
    dt_object = datetime.datetime.fromtimestamp(ts / 1000)
    return dt_object

def get_algos():
    """获取交易算法数据"""
    try:
        x2 = requests.post('http://110.42.96.63:9201/xt/getalgo')
        datas = x2.json()
        datas2 = []
        for d in datas:
            datas2.append(json.loads(d))
        if datas2 == []:
            return pd.DataFrame()
        df2 = pd.DataFrame(datas2).fillna(0)
        df2['createTime'] = df2['createTime'].astype(int).apply(timestramp2datetime)
        df2['lastUpdTime'] = df2['lastUpdTime'].astype(int).apply(timestramp2datetime)
        df2['startTime'] = df2['startTime'].astype(int).apply(timestramp2datetime)
        df2['endTime'] = df2['endTime'].astype(int).apply(timestramp2datetime) 
        df2 = df2.sort_values(["startTime","endTime"], ascending=False) 
        return df2
    except Exception as e:
        st.error(f"获取数据失败: {str(e)}")
        return pd.DataFrame()

def check_alerts(df: pd.DataFrame, alert_config: Dict) -> List[Dict]:
    """检查预警条件"""
    alerts = []

    if df.empty:
        return alerts

    # 错误订单预警
    if alert_config.get('error_orders', False):
        error_count = len(df[df['status'].isin(['ERROR', 'CANCELED', 'EXPIRED'])])
        if error_count > alert_config.get('error_threshold', 0):
            alerts.append({
                'type': '错误订单预警',
                'message': f'发现 {error_count} 个错误/取消/过期订单',
                'severity': 'high'
            })

    # 成交率偏离预警
    if alert_config.get('fill_rate_deviation', False):
        try:
            df_numeric = df.copy()
            df_numeric['quantity'] = pd.to_numeric(df_numeric['quantity'], errors='coerce')
            df_numeric['filledQuantity'] = pd.to_numeric(df_numeric['filledQuantity'], errors='coerce')
            df_numeric['filledPrice'] = pd.to_numeric(df_numeric['filledPrice'], errors='coerce')

            # 计算当前时间
            now = datetime.datetime.now()

            # 按账户分组计算成交率偏离
            for account_id in df_numeric['accountId'].unique():
                account_df = df_numeric[df_numeric['accountId'] == account_id].copy()

                if len(account_df) == 0:
                    continue

                # 计算理论成交率
                theoretical_filled = 0
                total_quantity = 0

                for _, row in account_df.iterrows():
                    start_time = row['startTime']
                    end_time = row['endTime']
                    quantity = row['quantity']

                    if pd.isna(start_time) or pd.isna(end_time) or pd.isna(quantity):
                        continue

                    # 计算时间进度
                    total_duration = (end_time - start_time).total_seconds()
                    elapsed_duration = (now - start_time).total_seconds()

                    if total_duration > 0:
                        time_progress = min(max(elapsed_duration / total_duration, 0), 1)
                        theoretical_filled += quantity * time_progress

                    total_quantity += quantity

                if total_quantity == 0:
                    continue

                # 理论成交率
                theoretical_fill_rate = theoretical_filled / total_quantity

                # 实际成交率
                actual_filled = account_df['filledQuantity'].sum()
                actual_fill_rate = actual_filled / total_quantity

                # 计算偏离度
                deviation = abs(actual_fill_rate - theoretical_fill_rate)
                threshold = alert_config.get('fill_rate_deviation_threshold', 0.2)

                if deviation > threshold:
                    alerts.append({
                        'type': '成交率偏离预警',
                        'message': f'账户 {account_id}  {acconts_alias.get(account_id, account_id)}: \n实际成交率 {actual_fill_rate:.2%}，理论成交率 {theoretical_fill_rate:.2%}，偏离度 {deviation:.2%} > 阈值 {threshold:.2%}',
                        'severity': 'medium'
                    })

        except Exception as e:
            st.error(f"成交率偏离计算失败: {str(e)}")

    return alerts

def display_alerts(alerts: List[Dict]):
    """显示预警信息"""
    if not alerts:
        return
    
    st.subheader("🚨 预警信息")
    for alert in alerts:
        if alert['severity'] == 'high':
            st.error(f"**{alert['type']}**: {alert['message']}")
        elif alert['severity'] == 'medium':
            st.warning(f"**{alert['type']}**: {alert['message']}")
        else:
            st.info(f"**{alert['type']}**: {alert['message']}")

def create_summary_metrics(df: pd.DataFrame):
    """创建汇总指标"""
    if df.empty:
        return
    
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        total_orders = len(df)
        st.metric("总订单数", total_orders)
    
    with col2:
        running_orders = len(df[~df['status'].isin(['FILLED', 'ERROR', 'CANCELED', 'EXPIRED'])])
        st.metric("运行中订单", running_orders)
    
    with col3:
        error_orders = len(df[df['status'].isin(['ERROR', 'CANCELED', 'EXPIRED'])])
        st.metric("错误订单", error_orders)
    
    with col4:
        df_numeric = df.copy()
        df_numeric['quantity'] = pd.to_numeric(df_numeric['quantity'], errors='coerce')
        df_numeric['filledQuantity'] = pd.to_numeric(df_numeric['filledQuantity'], errors='coerce')
        
        total_qty = df_numeric['quantity'].sum()
        filled_qty = df_numeric['filledQuantity'].sum()
        fill_rate = filled_qty / total_qty if total_qty > 0 else 0
        st.metric("成交率", f"{fill_rate:.2%}")
    
    with col5:
        unique_symbols = df['symbol'].nunique()
        st.metric("交易品种数", unique_symbols)

def create_charts(df: pd.DataFrame):
    """创建图表"""
    if df.empty:
        return

    # data
    try:
        df_numeric = df.copy()
        df_numeric['filledQuantity'] = pd.to_numeric(df_numeric['filledQuantity'], errors='coerce').fillna(0)
        df_numeric['filledPrice'] = pd.to_numeric(df_numeric['filledPrice'], errors='coerce').fillna(0)

        # 按账户分组统计
        account_stats = []
        for account_id in df_numeric['accountId'].unique():
            account_df = df_numeric[df_numeric['accountId'] == account_id]

            order_count = len(account_df)

            # 计算买入和卖出金额
            buy_df = account_df[account_df['operation'].str.contains('BUY')]
            sell_df = account_df[account_df['operation'].str.contains('SELL')]

            buy_amount = (buy_df['filledQuantity'] * buy_df['filledPrice']).sum()
            sell_amount = (sell_df['filledQuantity'] * sell_df['filledPrice']).sum()

            account_stats.append({
                'account_alias': acconts_alias.get(account_id, account_id),
                'accountId': account_id,
                'order_count': order_count,
                'buy_amount': buy_amount,
                'sell_amount': sell_amount
            })

        # 转换为DataFrame并排序
        stats_df = pd.DataFrame(account_stats)
        if len(stats_df) == 0:
            st.warning("暂无账户统计数据")
            return

        stats_df = stats_df.sort_values('account_alias', ascending=False).head(20)

    except Exception as e:
        st.error(f"账户统计图表生成失败: {str(e)}")
        stats_df = pd.DataFrame(columns=['account_alias', 'order_count', 'buy_amount', 'sell_amount'])

        

    # 使用占位符确保图表完全替换，避免残影
    col1, col2 = st.columns(2)

    with col1:
        # 创建饼图占位符
        pie_chart_placeholder = st.empty()

        with pie_chart_placeholder.container():
            # 订单状态分布饼图
            status_counts = df['status'].value_counts()
            fig_pie = px.pie(
                values=status_counts.values,
                names=status_counts.index,
                title="订单状态分布"
            )
            # 清除之前的图表状态
            fig_pie.update_layout(
                showlegend=True,
                margin=dict(t=50, b=50, l=50, r=50)
            )
            st.plotly_chart(fig_pie, use_container_width=True, key=f"pie_chart_{hash(str(status_counts))}")
        
        # 账户成交率图表
        st.write("**账户成交率分析**")

        # 计算各账户的成交率
        fill_rate_stats = []
        for account_id in df_numeric['accountId'].unique():
            account_df = df_numeric[df_numeric['accountId'] == account_id]

            if len(account_df) == 0:
                continue

            # 总体成交率
            total_quantity = account_df['quantity'].sum()
            total_filled = account_df['filledQuantity'].sum()
            overall_fill_rate = (total_filled / total_quantity * 100) if total_quantity > 0 else 0

            # 买入成交率
            buy_df = account_df[account_df['operation'].str.contains('BUY')]
            buy_quantity = buy_df['quantity'].sum()
            buy_filled = buy_df['filledQuantity'].sum()
            buy_fill_rate = (buy_filled / buy_quantity * 100) if buy_quantity > 0 else 0

            # 卖出成交率
            sell_df = account_df[account_df['operation'].str.contains('SELL')]
            sell_quantity = sell_df['quantity'].sum()
            sell_filled = sell_df['filledQuantity'].sum()
            sell_fill_rate = (sell_filled / sell_quantity * 100) if sell_quantity > 0 else 0

            fill_rate_stats.append({
                'account_alias': acconts_alias.get(account_id, account_id),
                'accountId': account_id,
                'overall_fill_rate': overall_fill_rate,
                'buy_fill_rate': buy_fill_rate,
                'sell_fill_rate': sell_fill_rate
            })

        # 转换为DataFrame并排序
        fill_rate_df = pd.DataFrame(fill_rate_stats)
        if len(fill_rate_df) > 0:
            fill_rate_df = fill_rate_df.sort_values('account_alias', ascending=False).head(20)

            # 创建成交率对比图表
            fig3 = go.Figure()

            # 添加总体成交率
            fig3.add_trace(go.Bar(
                x=fill_rate_df['account_alias'],
                y=fill_rate_df['overall_fill_rate'],
                name='总体成交率',
                marker_color='blue',
                text=[f"{rate:.1f}%" for rate in fill_rate_df['overall_fill_rate']],
                textposition='auto'
            ))

            # 添加买入成交率
            fig3.add_trace(go.Bar(
                x=fill_rate_df['account_alias'],
                y=fill_rate_df['buy_fill_rate'],
                name='买入成交率',
                marker_color='green',
                text=[f"{rate:.1f}%" for rate in fill_rate_df['buy_fill_rate']],
                textposition='auto'
            ))

            # 添加卖出成交率
            fig3.add_trace(go.Bar(
                x=fill_rate_df['account_alias'],
                y=fill_rate_df['sell_fill_rate'],
                name='卖出成交率',
                marker_color='red',
                text=[f"{rate:.1f}%" for rate in fill_rate_df['sell_fill_rate']],
                textposition='auto'
            ))

            fig3.update_layout(
                title="账户成交率对比",
                xaxis_title="账户ID",
                yaxis_title="成交率 (%)",
                barmode='group',  # 并排显示
                xaxis_tickangle=45,
                height=500,
                yaxis=dict(range=[0, 100])  # Y轴范围0-100%
            )

            # 使用占位符和唯一key避免残影
            fill_rate_chart_placeholder = st.empty()
            with fill_rate_chart_placeholder.container():
                st.plotly_chart(fig3, use_container_width=True, key=f"fill_rate_chart_{hash(str(fill_rate_df))}")
        else:
            st.warning("暂无成交率数据")

    with col2:
        # 账户订单数量和成交金额统计

            # 创建分离的图表而不是子图
            st.write("**账户订单数量 (Top 10)**")
            fig1 = px.bar(
                stats_df,
                x='account_alias',
                y='order_count',
                title="账户订单数量",
                labels={'account_alias': '账户别名', 'order_count': '订单数量'},
                color_discrete_sequence=['lightblue']
            )
            fig1.update_layout(xaxis_tickangle=45)

            # 使用占位符避免残影
            order_count_placeholder = st.empty()
            with order_count_placeholder.container():
                st.plotly_chart(fig1, use_container_width=True, key=f"order_count_chart_{hash(str(stats_df))}")

            # 成交金额图表
            st.write("**账户成交金额 (买入/卖出)**")

            # 创建买入卖出对比图表

            fig2 = go.Figure()

            # print(f'{stats_df}')
            # 添加买入金额
            fig2.add_trace(go.Bar(
                x=stats_df['account_alias'],
                y=stats_df['buy_amount'],
                name='买入金额',
                marker_color='green',
                text=stats_df['buy_amount'].apply(lambda x: f'{x:,.0f}'),
                textposition='auto'
            ))

            # 添加卖出金额
            fig2.add_trace(go.Bar(
                x=stats_df['account_alias'],
                y=stats_df['sell_amount'],
                name='卖出金额',
                marker_color='red',
                text=stats_df['sell_amount'].apply(lambda x: f'{x:,.0f}'),
                textposition='auto'
            ))

            fig2.update_layout(
                title="账户成交金额对比",
                xaxis_title="账户ID",
                yaxis_title="成交金额",
                barmode='group',  # 并排显示
                xaxis_tickangle=45,
                height=500
            )

            # 使用占位符避免残影
            amount_chart_placeholder = st.empty()
            with amount_chart_placeholder.container():
                st.plotly_chart(fig2, use_container_width=True, key=f"amount_chart_{hash(str(stats_df))}")


def main():
    """主函数"""
    st.title("📊 交易监控系统")

    # 显示页面刷新时间
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    st.info(f"🕒 页面刷新时间: {current_time}")

    # 侧边栏配置
    st.sidebar.header("配置选项")

    # 自动刷新设置
    auto_refresh = st.sidebar.checkbox("自动刷新", value=True)  # 默认关闭自动刷新
    refresh_interval = 60
    if auto_refresh:
        refresh_interval = st.sidebar.selectbox(
            "刷新间隔(秒)",
            [30, 60, 120, 300],
            index=1
        )
        # st.sidebar.warning("⚠️ 自动刷新可能影响页面操作，建议在需要时手动刷新")

    # 重置自动刷新停止状态
    if not auto_refresh and 'auto_refresh_stopped' in st.session_state:
        del st.session_state.auto_refresh_stopped

    # 时间过滤
    st.sidebar.subheader("时间过滤")
    time_filter = st.sidebar.selectbox(
        "时间范围",
        ["全部", "今天", "最近1小时", "最近6小时", "自定义"]
    )

    start_datetime = None
    end_datetime = None
    if time_filter == "自定义":
        start_date = st.sidebar.date_input("开始日期", datetime.date.today())
        start_time = st.sidebar.time_input("开始时间", datetime.time(9, 0))
        end_date = st.sidebar.date_input("结束日期", datetime.date.today())
        end_time = st.sidebar.time_input("结束时间", datetime.time(15, 0))
        start_datetime = datetime.datetime.combine(start_date, start_time)
        end_datetime = datetime.datetime.combine(end_date, end_time)

    # 预警设置
    st.sidebar.subheader("预警设置")
    alert_config = {
        'error_orders': st.sidebar.checkbox("错误订单预警", value=True),
        'error_threshold': st.sidebar.number_input("错误订单阈值", min_value=0, value=5),
        'fill_rate_deviation': st.sidebar.checkbox("成交率偏离预警", value=True),
        'fill_rate_deviation_threshold': st.sidebar.slider("成交率偏离阈值", 0.01, 0.5, 0.1, 0.01)
    }

    # 获取数据
    if st.button("刷新数据") or auto_refresh or 'df' not in st.session_state:
        with st.spinner("正在获取数据..."):
            df = get_algos()
            st.session_state.df = df

        if df.empty:
            st.warning("暂无数据")
            return
    else:
        df = st.session_state.get('df', pd.DataFrame())

    if df.empty:
        st.warning("暂无数据")
        return

    # 应用时间过滤
    if time_filter == "今天":
        today = datetime.date.today()
        df = df[df['startTime'].dt.date == today]
    elif time_filter == "最近1小时":
        one_hour_ago = datetime.datetime.now() - datetime.timedelta(hours=1)
        df = df[df['startTime'] >= one_hour_ago]
    elif time_filter == "最近6小时":
        six_hours_ago = datetime.datetime.now() - datetime.timedelta(hours=6)
        df = df[df['startTime'] >= six_hours_ago]
    elif time_filter == "自定义" and start_datetime and end_datetime:
        df = df[(df['startTime'] >= start_datetime) & (df['startTime'] <= end_datetime)]

    df['account_alias'] = df['accountId'].apply(lambda x: acconts_alias.get(x, x))

    # 检查预警
    alerts = check_alerts(df, alert_config)
    display_alerts(alerts)

    # 显示汇总指标
    st.subheader("📈 关键指标")
    create_summary_metrics(df)

    # 显示图表
    st.subheader("📊 数据可视化")
    create_charts(df)

    # 数据筛选和分组
    st.subheader("🔍 数据分析")

    col1, col2 = st.columns(2)
    with col1:
        group_by = st.selectbox(
            "分组字段",
            ['accountId', 'account_alias', 'symbol', 'operation', 'status', 'algoName', 'user']
        )

    with col2:
        agg_field = st.selectbox(
            "聚合字段",
            ['quantity', 'filledQuantity', 'filledPrice']
        )

    if group_by and agg_field:
        try:
            df_numeric = df.copy()
            df_numeric[agg_field] = pd.to_numeric(df_numeric[agg_field], errors='coerce')
            grouped_data = df_numeric.groupby(group_by)[agg_field].agg(['count', 'sum', 'mean']).round(2)
            grouped_data.columns = ['数量', '总和', '平均值']
            st.dataframe(grouped_data, use_container_width=True)
        except Exception as e:
            st.error(f"分组分析失败: {str(e)}")

    # 详细数据表
    st.subheader("📋 详细数据")

    # 创建筛选区域
    st.subheader("🔍 数据筛选")

    # 使用tabs来组织不同的筛选方式
    tab1, tab2 = st.tabs(["快速筛选", "自定义条件"])

    with tab1:
        # 使用表单来避免每次选择都刷新
        with st.form("quick_filter_form"):
            col1, col2 = st.columns(2)

            with col1:
                # 状态筛选 - 检查是否需要重置
                if st.session_state.get('reset_filters_flag', False):
                    status_default = df['status'].unique().tolist()
                else:
                    status_default = st.session_state.get('status_filter', df['status'].unique().tolist())

                status_filter = st.multiselect(
                    "筛选状态",
                    df['status'].unique(),
                    default=status_default,
                    key="status_filter"
                )

            with col2:
                # 账户筛选 - 检查是否需要重置
                if st.session_state.get('reset_filters_flag', False):
                    account_default = df['account_alias'].unique()[:5].tolist() if len(df['account_alias'].unique()) > 5 else df['account_alias'].unique().tolist()
                else:
                    account_default = st.session_state.get('account_filter', df['account_alias'].unique()[:5].tolist() if len(df['account_alias'].unique()) > 5 else df['account_alias'].unique().tolist())

                account_filter = st.multiselect(
                    "筛选账户",
                    df['account_alias'].unique(),
                    default=account_default,
                    key="account_filter"
                )

            # 表单提交按钮
            col1, col2, col3 = st.columns([1, 1, 2])
            with col1:
                apply_quick_filter = st.form_submit_button("应用筛选", type="primary")
            with col2:
                reset_quick_filter = st.form_submit_button("重置筛选")

            # 处理重置操作
            if reset_quick_filter:
                # 设置重置标志，让 widgets 在下次渲染时使用默认值
                st.session_state.reset_filters_flag = True

                # 删除 widget 的 session state keys
                if 'status_filter' in st.session_state:
                    del st.session_state.status_filter
                if 'account_filter' in st.session_state:
                    del st.session_state.account_filter

                # 同时重置已应用的筛选条件
                if 'applied_status_filter' in st.session_state:
                    del st.session_state.applied_status_filter
                if 'applied_account_filter' in st.session_state:
                    del st.session_state.applied_account_filter

                st.rerun()

            # 清除重置标志（在重置后的第一次渲染时）
            if st.session_state.get('reset_filters_flag', False):
                st.session_state.reset_filters_flag = False

    with tab2:
        st.write("**自定义筛选条件** (支持多个条件，用 AND 连接)")

        # 动态添加筛选条件
        if 'filter_conditions' not in st.session_state:
            st.session_state.filter_conditions = []

        # 添加新条件按钮
        col1, col2, col3 = st.columns([2, 2, 1])

        with col1:
            new_field = st.selectbox(
                "选择字段",
                ['symbol', 'status', 'accountId', 'account_alias', 'operation', 'algoName', 'user', 'quantity', 'filledQuantity'],
                key="new_field"
            )

        with col2:
            new_operator = st.selectbox(
                "选择操作符",
                ['等于', '不等于', '包含', '不包含', '大于', '小于', '大于等于', '小于等于'],
                key="new_operator"
            )

        with col3:
            if st.button("添加条件", key="add_condition"):
                st.session_state.filter_conditions.append({
                    'field': new_field,
                    'operator': new_operator,
                    'value': ''
                })
                st.rerun()

        # 显示和编辑现有条件
        conditions_to_remove = []
        for i, condition in enumerate(st.session_state.filter_conditions):
            col1, col2, col3, col4, col5 = st.columns([2, 2, 2, 2, 1])

            with col1:
                st.write(f"**{condition['field']}**")

            with col2:
                st.write(f"**{condition['operator']}**")

            with col3:
                # 根据字段类型提供不同的输入方式
                if condition['field'] in ['quantity', 'filledQuantity']:
                    condition['value'] = st.number_input(
                        f"值 {i+1}",
                        value=float(condition['value']) if condition['value'] else 0.0,
                        key=f"condition_value_{i}"
                    )
                else:
                    condition['value'] = st.text_input(
                        f"值 {i+1}",
                        value=str(condition['value']),
                        key=f"condition_value_{i}",
                        placeholder="输入筛选值"
                    )

            with col4:
                # 显示示例
                examples = {
                    'symbol': '600000, AAPL',
                    'status': 'FILLED, CANCELED',
                    'accountId': 'ACC001',
                    'account_alias': '超量子-中泰',
                    'operation': 'BUY, SELL',
                    'algoName': 'TWAP, VWAP',
                    'user': 'trader_1',
                    'quantity': '1000',
                    'filledQuantity': '500'
                }
                st.caption(f"示例: {examples.get(condition['field'], '')}")

            with col5:
                if st.button("删除", key=f"remove_condition_{i}"):
                    conditions_to_remove.append(i)

        # 删除标记的条件
        for i in reversed(conditions_to_remove):
            st.session_state.filter_conditions.pop(i)
            st.rerun()

        # 清空所有条件
        if st.session_state.filter_conditions and st.button("清空所有条件"):
            st.session_state.filter_conditions = []
            st.rerun()

    # 应用筛选
    filtered_df = df.copy()

    # 应用快速筛选 - 只有在表单提交时才应用
    if 'apply_quick_filter' in locals() and apply_quick_filter:
        # 保存筛选条件到session state
        st.session_state.applied_status_filter = status_filter
        st.session_state.applied_account_filter = account_filter

    # 使用已保存的筛选条件
    applied_status_filter = st.session_state.get('applied_status_filter', df['status'].unique())
    applied_account_filter = st.session_state.get('applied_account_filter', df['account_alias'].unique())

    if applied_status_filter is not None and len(applied_status_filter) > 0:
        filtered_df = filtered_df[filtered_df['status'].isin(applied_status_filter)]

    if applied_account_filter is not None and len(applied_account_filter) > 0:
        filtered_df = filtered_df[filtered_df['account_alias'].isin(applied_account_filter)]

    # 应用自定义条件筛选
    for condition in st.session_state.get('filter_conditions', []):
        if condition['value'] != '' and condition['value'] is not None:
            field = condition['field']
            operator = condition['operator']
            value = condition['value']

            try:
                if operator == '等于':
                    if field in ['quantity', 'filledQuantity']:
                        filtered_df = filtered_df[filtered_df[field].astype(float) == float(value)]
                    else:
                        filtered_df = filtered_df[filtered_df[field].astype(str) == str(value)]
                elif operator == '不等于':
                    if field in ['quantity', 'filledQuantity']:
                        filtered_df = filtered_df[filtered_df[field].astype(float) != float(value)]
                    else:
                        filtered_df = filtered_df[filtered_df[field].astype(str) != str(value)]
                elif operator == '包含':
                    filtered_df = filtered_df[filtered_df[field].astype(str).str.contains(str(value), case=False, na=False)]
                elif operator == '不包含':
                    filtered_df = filtered_df[~filtered_df[field].astype(str).str.contains(str(value), case=False, na=False)]
                elif operator == '大于':
                    filtered_df = filtered_df[pd.to_numeric(filtered_df[field], errors='coerce') > float(value)]
                elif operator == '小于':
                    filtered_df = filtered_df[pd.to_numeric(filtered_df[field], errors='coerce') < float(value)]
                elif operator == '大于等于':
                    filtered_df = filtered_df[pd.to_numeric(filtered_df[field], errors='coerce') >= float(value)]
                elif operator == '小于等于':
                    filtered_df = filtered_df[pd.to_numeric(filtered_df[field], errors='coerce') <= float(value)]
            except Exception as e:
                st.error(f"筛选条件 '{field} {operator} {value}' 应用失败: {str(e)}")

    # 显示筛选结果统计
    st.info(f"筛选结果: 从 {len(df)} 条记录中筛选出 {len(filtered_df)} 条记录")

    # 显示筛选后的数据
    st.dataframe(filtered_df, use_container_width=True)

    # 导出功能
    if st.button("导出数据"):
        csv = filtered_df.to_csv(index=False)
        st.download_button(
            label="下载CSV文件",
            data=csv,
            file_name=f"trading_data_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

    # 自动刷新逻辑 - 优化以避免图表残影
    if auto_refresh:
        # 在页面底部显示自动刷新状态
        st.divider()
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            st.info(f"🔄 自动刷新已启用，间隔: {refresh_interval} 秒")

        with col2:
            if st.button("立即刷新", key="manual_refresh"):
                # 清除所有缓存状态，强制完全重新渲染
                for key in list(st.session_state.keys()):
                    if key.startswith('plotly_') or key.startswith('chart_'):
                        del st.session_state[key]
                st.rerun()

        with col3:
            if st.button("停止自动刷新", key="stop_auto_refresh"):
                st.session_state.auto_refresh_stopped = True
                st.rerun()

        # 检查是否被用户停止
        if not st.session_state.get('auto_refresh_stopped', False):
            # 创建一个占位符来显示刷新状态
            refresh_placeholder = st.empty()

            # 显示刷新倒计时
            for i in range(refresh_interval, 0, -1):
                with refresh_placeholder.container():
                    st.info(f"⏳ {i} 秒后自动刷新...")
                time.sleep(1)

            # 显示正在刷新状态
            with refresh_placeholder.container():
                st.warning("🔄 正在刷新页面...")

            # 清除图表相关的缓存状态
            for key in list(st.session_state.keys()):
                if key.startswith('plotly_') or key.startswith('chart_') or key.startswith('fig_'):
                    del st.session_state[key]

            # 短暂延迟确保状态清除
            time.sleep(0.5)

            # 清空占位符并重新运行
            refresh_placeholder.empty()
            st.rerun()

if __name__ == "__main__":
    main()
