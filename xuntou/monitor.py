import pandas as pd
import os
from IPython import display
import datetime
import requests
import time
import json
import datetime
from IPython import display
# from utils import tools
# from bo.parentorder import *

def timestramp2datetime(ts):
    dt_object = datetime.datetime.fromtimestamp(ts / 1000)
    return dt_object

def get_algos():
    x2 = requests.post('http://************:9201/xt/getalgo')
    datas=x2.json()
    datas2=[]
    for d in datas:
        datas2.append(json.loads(d))
    if datas2==[]:
        return pd.DataFrame()
    df2=pd.DataFrame(datas2).fillna(0)
    df2['startTime']=df2['startTime'].astype(int).apply(timestramp2datetime)
    df2['endTime']=df2['endTime'].astype(int).apply(timestramp2datetime) 
    # df2['filledQuantity']=df2['filledQuantity'].astype(int)
    # df2=df2[df2['status']!='FILLED']
    df2=df2.sort_values(["startTime","endTime"],ascending=False) 
    return df2

 

def monitor():
    while(True):
        oriAlgo = get_algos()
        # print(oriAlgo.shape)
        print(oriAlgo.head(3))
        print(oriAlgo.columns)
        oriAlgo = oriAlgo[oriAlgo['startTime'] > datetime.datetime.strptime('**************', '%Y%m%d%H%M%S')]
        
        if oriAlgo.empty:
            display.display("empty record",clear=True)
            time.sleep(60)
            continue
        df = oriAlgo[['id','symbol','quantity','operation','filledQuantity','filledPrice','status','accountId']]
        # m = df.to_markdown(index=False)
        m1 = df.groupby(['accountId', 'operation'])[['quantity','filledQuantity']].sum().reset_index()
        m1['cmpQty'] = m1['filledQuantity']/m1['quantity']
        # print(m1.to_markdown(index=False))
        display.display("first",clear=True)
        display.display(m1)

        errorAlgoDf = df[df['status'].isin(['ERROR', 'CANCELED','EXPIRED'])]
        # print(errorAlgoDf.to_markdown(index=False))
        display.display("second ERROR/CANCELED/EXPIRED")
        display.display(errorAlgoDf)
        runningAlgoDf = df[~df['status'].isin(['FILLED', 'ERROR', 'CANCELED','EXPIRED'])]
        # print(runningAlgoDf.to_markdown(index=False))
        display.display("third running")
        display.display(runningAlgoDf)
        time.sleep(60)


if __name__=="__main__":
    monitor()