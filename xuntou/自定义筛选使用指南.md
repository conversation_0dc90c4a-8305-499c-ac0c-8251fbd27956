# 自定义筛选功能使用指南

## 🎯 功能概述

新增的自定义筛选功能让您可以更精确地筛选交易数据，支持多种条件组合，满足复杂的数据查询需求。

## 📋 筛选方式

### 1. 快速筛选 (Tab 1)
适用于常见的筛选需求：
- **状态筛选**: 多选订单状态（FILLED, RUNNING, ERROR, CANCELED, EXPIRED）
- **账户筛选**: 多选账户ID

### 2. 自定义条件 (Tab 2)
支持复杂的筛选条件组合：

#### 支持的字段
- `symbol`: 交易品种
- `status`: 订单状态
- `accountId`: 账户ID
- `operation`: 操作类型（BUY/SELL）
- `algoName`: 算法名称
- `user`: 用户
- `quantity`: 订单数量
- `filledQuantity`: 成交数量

#### 支持的操作符
- **等于**: 精确匹配
- **不等于**: 排除特定值
- **包含**: 模糊匹配（不区分大小写）
- **不包含**: 排除包含特定文本的记录
- **大于**: 数值比较（适用于数量字段）
- **小于**: 数值比较（适用于数量字段）
- **大于等于**: 数值比较（适用于数量字段）
- **小于等于**: 数值比较（适用于数量字段）

## 🔍 使用示例

### 示例1: 查找特定股票的订单
**需求**: 找出 symbol = "600000" 的所有订单

**操作步骤**:
1. 切换到"自定义条件"标签页
2. 选择字段: `symbol`
3. 选择操作符: `等于`
4. 点击"添加条件"
5. 在值输入框中输入: `600000`

### 示例2: 查找已取消的订单
**需求**: 找出 status = "CANCELED" 的订单

**操作步骤**:
1. 切换到"自定义条件"标签页
2. 选择字段: `status`
3. 选择操作符: `等于`
4. 点击"添加条件"
5. 在值输入框中输入: `CANCELED`

### 示例3: 查找大额订单
**需求**: 找出数量大于 10000 的订单

**操作步骤**:
1. 切换到"自定义条件"标签页
2. 选择字段: `quantity`
3. 选择操作符: `大于`
4. 点击"添加条件"
5. 在值输入框中输入: `10000`

### 示例4: 组合条件查询
**需求**: 找出特定用户的买入订单

**操作步骤**:
1. 添加第一个条件:
   - 字段: `user`
   - 操作符: `等于`
   - 值: `trader_1`

2. 添加第二个条件:
   - 字段: `operation`
   - 操作符: `等于`
   - 值: `BUY`

### 示例5: 模糊搜索
**需求**: 找出包含"TWAP"算法的订单

**操作步骤**:
1. 选择字段: `algoName`
2. 选择操作符: `包含`
3. 值: `TWAP`

## 💡 使用技巧

### 1. 条件管理
- **添加条件**: 选择字段和操作符后点击"添加条件"
- **编辑条件**: 直接在值输入框中修改
- **删除条件**: 点击对应条件行的"删除"按钮
- **清空所有**: 点击"清空所有条件"按钮

### 2. 数据类型处理
- **文本字段**: symbol, status, accountId, operation, algoName, user
- **数值字段**: quantity, filledQuantity
- **自动转换**: 系统会自动处理数据类型转换

### 3. 筛选逻辑
- **多条件关系**: 所有条件之间使用 AND 逻辑连接
- **大小写**: 文本搜索不区分大小写
- **空值处理**: 自动忽略空值和无效值

### 4. 性能优化
- **先用快速筛选**: 对于简单条件，优先使用快速筛选
- **合理组合**: 避免过多复杂条件影响性能
- **及时清理**: 不需要的条件及时删除

## 📊 筛选结果

### 结果显示
- **统计信息**: 显示"从 X 条记录中筛选出 Y 条记录"
- **实时更新**: 条件变化时结果立即更新
- **完整数据**: 显示筛选后的完整数据表格

### 导出功能
- **CSV导出**: 可以导出筛选后的数据
- **文件命名**: 自动添加时间戳
- **完整字段**: 包含所有原始字段

## ⚠️ 注意事项

### 1. 输入格式
- **数值字段**: 请输入有效的数字
- **文本字段**: 支持任意文本，包括中文
- **特殊字符**: 避免使用特殊正则表达式字符

### 2. 错误处理
- **格式错误**: 系统会显示具体错误信息
- **数据类型**: 自动处理类型转换错误
- **条件冲突**: 检查条件逻辑是否合理

### 3. 性能考虑
- **大数据量**: 建议先使用时间过滤减少数据量
- **复杂条件**: 避免过多嵌套条件
- **实时筛选**: 条件较多时可能有轻微延迟

## 🚀 高级用法

### 1. 常用条件保存
虽然当前版本不支持保存条件，但您可以：
- 记录常用的筛选组合
- 建立筛选模板文档
- 使用浏览器书签保存特定查询

### 2. 数据分析流程
1. **粗筛**: 使用快速筛选缩小范围
2. **精筛**: 使用自定义条件精确定位
3. **分析**: 结合分组功能进行统计分析
4. **导出**: 保存筛选结果供进一步分析

### 3. 监控场景
- **异常订单**: status = "ERROR" 或 "CANCELED"
- **大额交易**: quantity > 某个阈值
- **特定品种**: symbol 包含特定代码
- **用户行为**: 特定用户的交易模式
- **算法效果**: 特定算法的执行情况

## 📞 技术支持

如果在使用过程中遇到问题：
1. 检查输入格式是否正确
2. 查看错误提示信息
3. 尝试简化筛选条件
4. 重新加载页面重置状态
