# 账户统计图表修复说明

## 🐛 问题描述

从您提供的截图可以看到，账户统计分析图表存在以下问题：
1. 买入/卖出金额柱状图无法正确显示
2. 图表布局混乱，数据显示不清晰
3. 子图功能可能存在兼容性问题

## 🔧 修复方案

### 原始实现问题
```python
# 原始代码使用 plotly 子图 (make_subplots)
fig = make_subplots(
    rows=2, cols=1,
    subplot_titles=('账户订单数量 (Top 10)', '账户成交金额 (买入/卖出)'),
    vertical_spacing=0.15
)

# 问题：子图在某些情况下可能显示异常
```

### 修复后的实现
```python
# 修复方案：分离为独立的图表
# 1. 账户订单数量图表
fig1 = px.bar(
    stats_df,
    x='accountId',
    y='order_count',
    title="账户订单数量",
    color_discrete_sequence=['lightblue']
)

# 2. 账户成交金额对比图表
fig2 = go.Figure()
fig2.add_trace(go.Bar(
    x=stats_df['accountId'],
    y=stats_df['buy_amount'],
    name='买入金额',
    marker_color='green'
))
fig2.add_trace(go.Bar(
    x=stats_df['accountId'],
    y=stats_df['sell_amount'],
    name='卖出金额',
    marker_color='red'
))
fig2.update_layout(barmode='group')  # 并排显示
```

## ✅ 修复内容

### 1. 图表结构调整
- **修复前**: 使用 `make_subplots` 创建双层子图
- **修复后**: 分离为两个独立的图表，更清晰直观

### 2. 数据显示优化
- **添加数据标签**: 在柱状图上显示具体数值
- **并排显示**: 买入/卖出金额使用 `barmode='group'` 并排对比
- **颜色区分**: 绿色表示买入，红色表示卖出

### 3. 错误处理增强
- **数据验证**: 检查统计数据是否为空
- **降级处理**: 如果复杂图表失败，自动降级到简单图表
- **异常捕获**: 完善的错误提示

### 4. 用户体验改进
- **清晰标题**: 每个图表都有明确的标题说明
- **数值显示**: 柱状图上显示具体的金额数值
- **交互性**: 保持 plotly 的交互功能（缩放、悬停等）

## 📊 修复后的效果

### 图表1: 账户订单数量
```
账户订单数量 (Top 10)
┌─────────────────────────────┐
│  ████ ACC001 (150订单)       │
│  ███  ACC002 (120订单)       │
│  ██   ACC003 (80订单)        │
│  █    ACC004 (50订单)        │
└─────────────────────────────┘
```

### 图表2: 账户成交金额对比
```
账户成交金额 (买入/卖出)
┌─────────────────────────────┐
│ ██ ██  ACC001               │
│ █  ███ ACC002               │
│ ███ █  ACC003               │
│ █  ██  ACC004               │
└─────────────────────────────┘
绿色=买入金额  红色=卖出金额
```

## 🔍 技术细节

### 数据处理逻辑
```python
# 按账户分组统计
for account_id in df['accountId'].unique():
    account_df = df[df['accountId'] == account_id]
    
    # 订单数量
    order_count = len(account_df)
    
    # 买入金额
    buy_df = account_df[account_df['operation'] == 'BUY']
    buy_amount = (buy_df['filledQuantity'] * buy_df['filledPrice']).sum()
    
    # 卖出金额
    sell_df = account_df[account_df['operation'] == 'SELL']
    sell_amount = (sell_df['filledQuantity'] * sell_df['filledPrice']).sum()
```

### 图表配置
```python
# 订单数量图表
fig1.update_layout(xaxis_tickangle=45)  # X轴标签倾斜

# 成交金额图表
fig2.update_layout(
    title="账户成交金额对比",
    xaxis_title="账户ID",
    yaxis_title="成交金额",
    barmode='group',      # 并排显示
    xaxis_tickangle=45,   # X轴标签倾斜
    height=400           # 固定高度
)
```

## 🚀 测试验证

### 启动测试
```bash
# 测试版本
cd /home/<USER>/trade/xuntou
streamlit run test_monitor.py --server.port 8502

# 主程序版本
streamlit run my_monitor.py --server.port 8501
```

### 验证要点
1. ✅ 账户订单数量图表正常显示
2. ✅ 买入/卖出金额并排对比显示
3. ✅ 数值标签清晰可见
4. ✅ 图表交互功能正常
5. ✅ 错误处理机制有效

## 💡 使用建议

### 数据分析
- **订单活跃度**: 通过订单数量图表识别最活跃的账户
- **资金流向**: 通过成交金额图表分析买入卖出比例
- **风险控制**: 监控单一账户的交易规模

### 性能优化
- **数据量控制**: 只显示 Top 10 账户，避免图表过于拥挤
- **实时更新**: 图表会随数据刷新自动更新
- **响应式设计**: 图表自适应容器宽度

## 📁 修复文件

- ✅ `my_monitor.py`: 主程序图表修复
- ✅ `test_monitor.py`: 测试版本图表修复

现在账户统计图表应该能够正确显示买入/卖出金额的对比了！🎯
