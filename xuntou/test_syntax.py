#!/usr/bin/env python3
"""
测试监控程序的语法和基本功能
"""

import sys
import traceback

def test_imports():
    """测试导入"""
    try:
        print("测试导入...")
        import streamlit as st
        import pandas as pd
        import plotly.express as px
        import datetime
        import numpy as np
        print("✓ 所有依赖包导入成功")
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_syntax():
    """测试语法"""
    try:
        print("测试主程序语法...")
        # 测试主程序语法
        with open('my_monitor.py', 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, 'my_monitor.py', 'exec')
        print("✓ my_monitor.py 语法正确")
        
        print("测试测试程序语法...")
        # 测试测试程序语法
        with open('test_monitor.py', 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, 'test_monitor.py', 'exec')
        print("✓ test_monitor.py 语法正确")
        
        return True
    except Exception as e:
        print(f"✗ 语法错误: {e}")
        traceback.print_exc()
        return False

def test_plotly():
    """测试 Plotly 功能"""
    try:
        print("测试 Plotly 功能...")
        import plotly.express as px
        import pandas as pd
        
        # 创建测试数据
        df = pd.DataFrame({
            'x': [1, 2, 3, 4],
            'y': [10, 11, 12, 13]
        })
        
        # 测试柱状图
        fig = px.bar(x=df['x'], y=df['y'])
        fig.update_layout(xaxis_tickangle=45)
        print("✓ Plotly 柱状图功能正常")
        
        # 测试饼图
        fig_pie = px.pie(values=[1, 2, 3], names=['A', 'B', 'C'])
        print("✓ Plotly 饼图功能正常")
        
        return True
    except Exception as e:
        print(f"✗ Plotly 测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("交易监控系统 - 语法和功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_syntax,
        test_plotly
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！监控系统可以正常运行。")
        print("\n启动命令:")
        print("streamlit run my_monitor.py --server.port 8501")
        print("streamlit run test_monitor.py --server.port 8502")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
