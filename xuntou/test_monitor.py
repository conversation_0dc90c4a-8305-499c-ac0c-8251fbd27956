import streamlit as st
import pandas as pd
import plotly.express as px
import datetime
import time
import numpy as np
from typing import Dict, List

# 设置页面配置
st.set_page_config(
    page_title="交易监控系统 - 测试版",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

def generate_mock_data(num_records=100):
    """生成模拟交易数据"""
    np.random.seed(42)
    
    symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 'META', 'NVDA', 'NFLX']
    operations = ['BUY', 'SELL']
    statuses = ['FILLED', 'RUNNING', 'ERROR', 'CANCELED', 'EXPIRED']
    algo_names = ['TWAP', 'VWAP', 'POV', 'IS']
    account_ids = [f'ACC{i:03d}' for i in range(1, 21)]
    
    data = []
    for i in range(num_records):
        start_time = datetime.datetime.now() - datetime.timedelta(
            hours=np.random.randint(0, 24),
            minutes=np.random.randint(0, 60)
        )
        
        quantity = np.random.randint(100, 10000)
        filled_quantity = np.random.randint(0, quantity) if np.random.random() > 0.3 else quantity
        
        record = {
            'id': f'ORDER_{i:06d}',
            'symbol': np.random.choice(symbols),
            'price': round(np.random.uniform(50, 500), 2),
            'quantity': quantity,
            'orderType': 'LIMIT',
            'operation': np.random.choice(operations),
            'filledQuantity': filled_quantity,
            'filledPrice': round(np.random.uniform(50, 500), 2),
            'status': np.random.choice(statuses, p=[0.6, 0.2, 0.1, 0.05, 0.05]),
            'errorId': 0 if np.random.random() > 0.1 else np.random.randint(1000, 9999),
            'errorMsg': '' if np.random.random() > 0.1 else 'Connection timeout',
            'createTime': start_time - datetime.timedelta(minutes=np.random.randint(1, 30)),
            'lastUpdTime': start_time,
            'startTime': start_time,
            'endTime': start_time + datetime.timedelta(minutes=np.random.randint(30, 180)),
            'algoName': np.random.choice(algo_names),
            'params': '{"urgency": 0.5}',
            'user': f'trader_{np.random.randint(1, 10)}',
            'batchName': f'batch_{np.random.randint(1, 20)}',
            'group': f'group_{np.random.randint(1, 5)}',
            'mkt': 'NASDAQ',
            'remark': '',
            'accountId': np.random.choice(account_ids),
            'firm': 'TestFirm',
            'version': '1.0'
        }
        data.append(record)
    
    return pd.DataFrame(data)

def check_alerts(df: pd.DataFrame, alert_config: Dict) -> List[Dict]:
    """检查预警条件"""
    alerts = []

    if df.empty:
        return alerts

    # 错误订单预警
    if alert_config.get('error_orders', False):
        error_count = len(df[df['status'].isin(['ERROR', 'CANCELED', 'EXPIRED'])])
        if error_count > alert_config.get('error_threshold', 0):
            alerts.append({
                'type': '错误订单预警',
                'message': f'发现 {error_count} 个错误/取消/过期订单',
                'severity': 'high'
            })

    # 成交率偏离预警
    if alert_config.get('fill_rate_deviation', False):
        try:
            # 计算当前时间
            now = datetime.datetime.now()

            # 按账户分组计算成交率偏离
            for account_id in df['accountId'].unique():
                account_df = df[df['accountId'] == account_id].copy()

                if len(account_df) == 0:
                    continue

                # 计算理论成交率
                theoretical_filled = 0
                total_quantity = 0

                for _, row in account_df.iterrows():
                    start_time = row['startTime']
                    end_time = row['endTime']
                    quantity = row['quantity']

                    if pd.isna(start_time) or pd.isna(end_time) or pd.isna(quantity):
                        continue

                    # 计算时间进度
                    total_duration = (end_time - start_time).total_seconds()
                    elapsed_duration = (now - start_time).total_seconds()

                    if total_duration > 0:
                        time_progress = min(max(elapsed_duration / total_duration, 0), 1)
                        theoretical_filled += quantity * time_progress

                    total_quantity += quantity

                if total_quantity == 0:
                    continue

                # 理论成交率
                theoretical_fill_rate = theoretical_filled / total_quantity

                # 实际成交率
                actual_filled = account_df['filledQuantity'].sum()
                actual_fill_rate = actual_filled / total_quantity

                # 计算偏离度
                deviation = abs(actual_fill_rate - theoretical_fill_rate)
                threshold = alert_config.get('fill_rate_deviation_threshold', 0.1)

                if deviation > threshold:
                    alerts.append({
                        'type': '成交率偏离预警',
                        'message': f'账户 {account_id}: 实际成交率 {actual_fill_rate:.2%}，理论成交率 {theoretical_fill_rate:.2%}，偏离度 {deviation:.2%} > 阈值 {threshold:.2%}',
                        'severity': 'medium'
                    })

        except Exception as e:
            st.error(f"成交率偏离计算失败: {str(e)}")

    return alerts

def display_alerts(alerts: List[Dict]):
    """显示预警信息"""
    if not alerts:
        return
    
    st.subheader("🚨 预警信息")
    for alert in alerts:
        if alert['severity'] == 'high':
            st.error(f"**{alert['type']}**: {alert['message']}")
        elif alert['severity'] == 'medium':
            st.warning(f"**{alert['type']}**: {alert['message']}")
        else:
            st.info(f"**{alert['type']}**: {alert['message']}")

def create_summary_metrics(df: pd.DataFrame):
    """创建汇总指标"""
    if df.empty:
        return
    
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        total_orders = len(df)
        st.metric("总订单数", total_orders)
    
    with col2:
        running_orders = len(df[df['status'] == 'RUNNING'])
        st.metric("运行中订单", running_orders)
    
    with col3:
        error_orders = len(df[df['status'].isin(['ERROR', 'CANCELED', 'EXPIRED'])])
        st.metric("错误订单", error_orders)
    
    with col4:
        total_qty = df['quantity'].sum()
        filled_qty = df['filledQuantity'].sum()
        fill_rate = filled_qty / total_qty if total_qty > 0 else 0
        st.metric("成交率", f"{fill_rate:.2%}")
    
    with col5:
        unique_symbols = df['symbol'].nunique()
        st.metric("交易品种数", unique_symbols)

def create_charts(df: pd.DataFrame):
    """创建图表"""
    if df.empty:
        return

    col1, col2 = st.columns(2)

    with col1:
        # 订单状态分布饼图
        status_counts = df['status'].value_counts()
        fig_pie = px.pie(
            values=status_counts.values,
            names=status_counts.index,
            title="订单状态分布"
        )
        # 使用占位符和唯一key避免残影
        pie_chart_placeholder = st.empty()
        with pie_chart_placeholder.container():
            st.plotly_chart(fig_pie, use_container_width=True, key=f"pie_chart_{hash(str(status_counts))}")

    with col2:
        # 账户订单数量和成交金额统计
        try:
            # 按账户分组统计
            account_stats = []
            for account_id in df['accountId'].unique():
                account_df = df[df['accountId'] == account_id]

                order_count = len(account_df)

                # 计算买入和卖出金额
                buy_df = account_df[account_df['operation'] == 'BUY']
                sell_df = account_df[account_df['operation'] == 'SELL']

                buy_amount = (buy_df['filledQuantity'] * buy_df['filledPrice']).sum()
                sell_amount = (sell_df['filledQuantity'] * sell_df['filledPrice']).sum()

                account_stats.append({
                    'accountId': account_id,
                    'order_count': order_count,
                    'buy_amount': buy_amount,
                    'sell_amount': sell_amount
                })

            # 转换为DataFrame并排序
            stats_df = pd.DataFrame(account_stats)
            if len(stats_df) == 0:
                st.warning("暂无账户统计数据")
                return

            stats_df = stats_df.sort_values('order_count', ascending=False).head(10)

            # 创建分离的图表而不是子图
            st.write("**账户订单数量 (Top 10)**")
            fig1 = px.bar(
                stats_df,
                x='accountId',
                y='order_count',
                title="账户订单数量",
                labels={'accountId': '账户ID', 'order_count': '订单数量'},
                color_discrete_sequence=['lightblue']
            )
            fig1.update_layout(xaxis_tickangle=45)

            # 使用占位符避免残影
            order_count_placeholder = st.empty()
            with order_count_placeholder.container():
                st.plotly_chart(fig1, use_container_width=True, key=f"order_count_chart_{hash(str(stats_df))}")

            # 成交金额图表
            st.write("**账户成交金额 (买入/卖出)**")

            # 创建买入卖出对比图表
            import plotly.graph_objects as go

            fig2 = go.Figure()

            # 添加买入金额
            fig2.add_trace(go.Bar(
                x=stats_df['accountId'],
                y=stats_df['buy_amount'],
                name='买入金额',
                marker_color='green',
                text=stats_df['buy_amount'].round(2),
                textposition='auto'
            ))

            # 添加卖出金额
            fig2.add_trace(go.Bar(
                x=stats_df['accountId'],
                y=stats_df['sell_amount'],
                name='卖出金额',
                marker_color='red',
                text=stats_df['sell_amount'].round(2),
                textposition='auto'
            ))

            fig2.update_layout(
                title="账户成交金额对比",
                xaxis_title="账户ID",
                yaxis_title="成交金额",
                barmode='group',  # 并排显示
                xaxis_tickangle=45,
                height=400
            )

            # 使用占位符避免残影
            amount_chart_placeholder = st.empty()
            with amount_chart_placeholder.container():
                st.plotly_chart(fig2, use_container_width=True, key=f"amount_chart_{hash(str(stats_df))}")

            # 账户成交率图表
            st.write("**账户成交率分析**")

            # 计算各账户的成交率
            fill_rate_stats = []
            for account_id in df['accountId'].unique():
                account_df = df[df['accountId'] == account_id]

                if len(account_df) == 0:
                    continue

                # 总体成交率
                total_quantity = account_df['quantity'].sum()
                total_filled = account_df['filledQuantity'].sum()
                overall_fill_rate = (total_filled / total_quantity * 100) if total_quantity > 0 else 0

                # 买入成交率
                buy_df = account_df[account_df['operation'] == 'BUY']
                buy_quantity = buy_df['quantity'].sum()
                buy_filled = buy_df['filledQuantity'].sum()
                buy_fill_rate = (buy_filled / buy_quantity * 100) if buy_quantity > 0 else 0

                # 卖出成交率
                sell_df = account_df[account_df['operation'] == 'SELL']
                sell_quantity = sell_df['quantity'].sum()
                sell_filled = sell_df['filledQuantity'].sum()
                sell_fill_rate = (sell_filled / sell_quantity * 100) if sell_quantity > 0 else 0

                fill_rate_stats.append({
                    'accountId': account_id,
                    'overall_fill_rate': overall_fill_rate,
                    'buy_fill_rate': buy_fill_rate,
                    'sell_fill_rate': sell_fill_rate
                })

            # 转换为DataFrame并排序
            fill_rate_df = pd.DataFrame(fill_rate_stats)
            if len(fill_rate_df) > 0:
                fill_rate_df = fill_rate_df.sort_values('overall_fill_rate', ascending=False).head(10)

                # 创建成交率对比图表
                fig3 = go.Figure()

                # 添加总体成交率
                fig3.add_trace(go.Bar(
                    x=fill_rate_df['accountId'],
                    y=fill_rate_df['overall_fill_rate'],
                    name='总体成交率',
                    marker_color='blue',
                    text=[f"{rate:.1f}%" for rate in fill_rate_df['overall_fill_rate']],
                    textposition='auto'
                ))

                # 添加买入成交率
                fig3.add_trace(go.Bar(
                    x=fill_rate_df['accountId'],
                    y=fill_rate_df['buy_fill_rate'],
                    name='买入成交率',
                    marker_color='green',
                    text=[f"{rate:.1f}%" for rate in fill_rate_df['buy_fill_rate']],
                    textposition='auto'
                ))

                # 添加卖出成交率
                fig3.add_trace(go.Bar(
                    x=fill_rate_df['accountId'],
                    y=fill_rate_df['sell_fill_rate'],
                    name='卖出成交率',
                    marker_color='red',
                    text=[f"{rate:.1f}%" for rate in fill_rate_df['sell_fill_rate']],
                    textposition='auto'
                ))

                fig3.update_layout(
                    title="账户成交率对比",
                    xaxis_title="账户ID",
                    yaxis_title="成交率 (%)",
                    barmode='group',  # 并排显示
                    xaxis_tickangle=45,
                    height=400,
                    yaxis=dict(range=[0, 100])  # Y轴范围0-100%
                )

                # 使用占位符和唯一key避免残影
                fill_rate_chart_placeholder = st.empty()
                with fill_rate_chart_placeholder.container():
                    st.plotly_chart(fig3, use_container_width=True, key=f"fill_rate_chart_{hash(str(fill_rate_df))}")
            else:
                st.warning("暂无成交率数据")

        except Exception as e:
            st.error(f"账户统计图表生成失败: {str(e)}")
            # 降级到简单的订单数量图表
            account_counts = df['accountId'].value_counts().head(10)
            fig_bar = px.bar(
                x=account_counts.index,
                y=account_counts.values,
                title="账户订单数量 (Top 10)",
                labels={'x': '账户ID', 'y': '订单数量'}
            )
            fig_bar.update_layout(xaxis_tickangle=45)

            # 使用占位符避免残影
            fallback_chart_placeholder = st.empty()
            with fallback_chart_placeholder.container():
                st.plotly_chart(fig_bar, use_container_width=True, key=f"fallback_chart_{hash(str(account_counts))}")

def main():
    """主函数"""
    st.title("📊 交易监控系统 - 测试版")
    st.info("这是使用模拟数据的测试版本")

    # 显示页面刷新时间
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    st.info(f"🕒 页面刷新时间: {current_time}")

    # 侧边栏配置
    st.sidebar.header("配置选项")
    
    # 数据生成设置
    num_records = st.sidebar.slider("模拟数据条数", 50, 500, 100)

    # 自动刷新设置
    auto_refresh = st.sidebar.checkbox("自动刷新", value=False)
    refresh_interval = 60
    if auto_refresh:
        refresh_interval = st.sidebar.selectbox(
            "刷新间隔(秒)",
            [30, 60, 120, 300],
            index=1
        )
        st.sidebar.warning("⚠️ 自动刷新可能影响页面操作，建议在需要时手动刷新")

    # 预警设置
    st.sidebar.subheader("预警设置")
    alert_config = {
        'error_orders': st.sidebar.checkbox("错误订单预警", value=True),
        'error_threshold': st.sidebar.number_input("错误订单阈值", min_value=0, value=5),
        'fill_rate_deviation': st.sidebar.checkbox("成交率偏离预警", value=True),
        'fill_rate_deviation_threshold': st.sidebar.slider("成交率偏离阈值", 0.01, 0.5, 0.1, 0.01)
    }
    
    # 生成数据
    if st.button("生成新数据") or 'df' not in st.session_state:
        with st.spinner("正在生成模拟数据..."):
            st.session_state.df = generate_mock_data(num_records)
    
    df = st.session_state.df
    
    # 检查预警
    alerts = check_alerts(df, alert_config)
    display_alerts(alerts)
    
    # 显示汇总指标
    st.subheader("📈 关键指标")
    create_summary_metrics(df)
    
    # 显示图表
    st.subheader("📊 数据可视化")
    create_charts(df)
    
    # 数据筛选和分组
    st.subheader("🔍 数据分析")
    
    col1, col2 = st.columns(2)
    with col1:
        group_by = st.selectbox(
            "分组字段",
            ['accountId', 'symbol', 'operation', 'status', 'algoName']
        )
    
    with col2:
        agg_field = st.selectbox(
            "聚合字段",
            ['quantity', 'filledQuantity', 'filledPrice']
        )
    
    if group_by and agg_field:
        grouped_data = df.groupby(group_by)[agg_field].agg(['count', 'sum', 'mean']).round(2)
        grouped_data.columns = ['数量', '总和', '平均值']
        st.dataframe(grouped_data, use_container_width=True)
    
    # 详细数据表
    st.subheader("📋 详细数据")

    # 创建筛选区域
    st.subheader("🔍 数据筛选")

    # 使用tabs来组织不同的筛选方式
    tab1, tab2 = st.tabs(["快速筛选", "自定义条件"])

    with tab1:
        # 使用表单来避免每次选择都刷新
        with st.form("quick_filter_form"):
            col1, col2 = st.columns(2)

            with col1:
                # 状态筛选
                status_filter = st.multiselect(
                    "筛选状态",
                    df['status'].unique(),
                    default=df['status'].unique(),
                    key="status_filter"
                )

            with col2:
                # 账户筛选
                account_filter = st.multiselect(
                    "筛选账户",
                    df['accountId'].unique(),
                    default=df['accountId'].unique()[:5] if len(df['accountId'].unique()) > 5 else df['accountId'].unique(),
                    key="account_filter"
                )

            # 表单提交按钮
            col1, col2, col3 = st.columns([1, 1, 2])
            with col1:
                apply_quick_filter = st.form_submit_button("应用筛选", type="primary")
            with col2:
                reset_quick_filter = st.form_submit_button("重置筛选")

            # 处理重置操作
            if reset_quick_filter:
                st.session_state.status_filter = df['status'].unique().tolist()
                st.session_state.account_filter = df['accountId'].unique()[:5].tolist() if len(df['accountId'].unique()) > 5 else df['accountId'].unique().tolist()
                st.rerun()

    with tab2:
        st.write("**自定义筛选条件** (支持多个条件，用 AND 连接)")

        # 动态添加筛选条件
        if 'filter_conditions' not in st.session_state:
            st.session_state.filter_conditions = []

        # 添加新条件按钮
        col1, col2, col3 = st.columns([2, 2, 1])

        with col1:
            new_field = st.selectbox(
                "选择字段",
                ['symbol', 'status', 'accountId', 'operation', 'algoName', 'user', 'quantity', 'filledQuantity'],
                key="new_field"
            )

        with col2:
            new_operator = st.selectbox(
                "选择操作符",
                ['等于', '不等于', '包含', '不包含', '大于', '小于', '大于等于', '小于等于'],
                key="new_operator"
            )

        with col3:
            if st.button("添加条件", key="add_condition"):
                st.session_state.filter_conditions.append({
                    'field': new_field,
                    'operator': new_operator,
                    'value': ''
                })
                st.rerun()

        # 显示和编辑现有条件
        conditions_to_remove = []
        for i, condition in enumerate(st.session_state.filter_conditions):
            col1, col2, col3, col4, col5 = st.columns([2, 2, 2, 2, 1])

            with col1:
                st.write(f"**{condition['field']}**")

            with col2:
                st.write(f"**{condition['operator']}**")

            with col3:
                # 根据字段类型提供不同的输入方式
                if condition['field'] in ['quantity', 'filledQuantity']:
                    condition['value'] = st.number_input(
                        f"值 {i+1}",
                        value=float(condition['value']) if condition['value'] else 0.0,
                        key=f"condition_value_{i}"
                    )
                else:
                    condition['value'] = st.text_input(
                        f"值 {i+1}",
                        value=str(condition['value']),
                        key=f"condition_value_{i}",
                        placeholder="输入筛选值"
                    )

            with col4:
                # 显示示例
                examples = {
                    'symbol': '600000, AAPL',
                    'status': 'FILLED, CANCELED',
                    'accountId': 'ACC001',
                    'operation': 'BUY, SELL',
                    'algoName': 'TWAP, VWAP',
                    'user': 'trader_1',
                    'quantity': '1000',
                    'filledQuantity': '500'
                }
                st.caption(f"示例: {examples.get(condition['field'], '')}")

            with col5:
                if st.button("删除", key=f"remove_condition_{i}"):
                    conditions_to_remove.append(i)

        # 删除标记的条件
        for i in reversed(conditions_to_remove):
            st.session_state.filter_conditions.pop(i)
            st.rerun()

        # 清空所有条件
        if st.session_state.filter_conditions and st.button("清空所有条件"):
            st.session_state.filter_conditions = []
            st.rerun()

    # 应用筛选
    filtered_df = df.copy()

    # 应用快速筛选 - 只有在表单提交时才应用
    if 'apply_quick_filter' in locals() and apply_quick_filter:
        # 保存筛选条件到session state
        st.session_state.applied_status_filter = status_filter
        st.session_state.applied_account_filter = account_filter

    # 使用已保存的筛选条件
    applied_status_filter = st.session_state.get('applied_status_filter', df['status'].unique())
    applied_account_filter = st.session_state.get('applied_account_filter', df['accountId'].unique())

    if applied_status_filter is not None and len(applied_status_filter) > 0:
        filtered_df = filtered_df[filtered_df['status'].isin(applied_status_filter)]

    if applied_account_filter is not None and len(applied_account_filter) > 0:
        filtered_df = filtered_df[filtered_df['accountId'].isin(applied_account_filter)]

    # 应用自定义条件筛选
    for condition in st.session_state.get('filter_conditions', []):
        if condition['value'] != '' and condition['value'] is not None:
            field = condition['field']
            operator = condition['operator']
            value = condition['value']

            try:
                if operator == '等于':
                    if field in ['quantity', 'filledQuantity']:
                        filtered_df = filtered_df[filtered_df[field].astype(float) == float(value)]
                    else:
                        filtered_df = filtered_df[filtered_df[field].astype(str) == str(value)]
                elif operator == '不等于':
                    if field in ['quantity', 'filledQuantity']:
                        filtered_df = filtered_df[filtered_df[field].astype(float) != float(value)]
                    else:
                        filtered_df = filtered_df[filtered_df[field].astype(str) != str(value)]
                elif operator == '包含':
                    filtered_df = filtered_df[filtered_df[field].astype(str).str.contains(str(value), case=False, na=False)]
                elif operator == '不包含':
                    filtered_df = filtered_df[~filtered_df[field].astype(str).str.contains(str(value), case=False, na=False)]
                elif operator == '大于':
                    filtered_df = filtered_df[pd.to_numeric(filtered_df[field], errors='coerce') > float(value)]
                elif operator == '小于':
                    filtered_df = filtered_df[pd.to_numeric(filtered_df[field], errors='coerce') < float(value)]
                elif operator == '大于等于':
                    filtered_df = filtered_df[pd.to_numeric(filtered_df[field], errors='coerce') >= float(value)]
                elif operator == '小于等于':
                    filtered_df = filtered_df[pd.to_numeric(filtered_df[field], errors='coerce') <= float(value)]
            except Exception as e:
                st.error(f"筛选条件 '{field} {operator} {value}' 应用失败: {str(e)}")

    # 显示筛选结果统计
    st.info(f"筛选结果: 从 {len(df)} 条记录中筛选出 {len(filtered_df)} 条记录")
    
    # 显示筛选后的数据
    st.dataframe(filtered_df, use_container_width=True)
    
    # 导出功能
    if st.button("导出数据"):
        csv = filtered_df.to_csv(index=False)
        st.download_button(
            label="下载CSV文件",
            data=csv,
            file_name=f"test_trading_data_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

    # 自动刷新逻辑 - 优化以避免图表残影
    if auto_refresh:
        # 在页面底部显示自动刷新状态
        st.divider()
        col1, col2, col3 = st.columns([2, 1, 1])

        with col1:
            st.info(f"🔄 自动刷新已启用，间隔: {refresh_interval} 秒")

        with col2:
            if st.button("立即刷新", key="manual_refresh"):
                # 清除所有缓存状态，强制完全重新渲染
                for key in list(st.session_state.keys()):
                    if key.startswith('plotly_') or key.startswith('chart_'):
                        del st.session_state[key]
                st.rerun()

        with col3:
            if st.button("停止自动刷新", key="stop_auto_refresh"):
                st.session_state.auto_refresh_stopped = True
                st.rerun()

        # 检查是否被用户停止
        if not st.session_state.get('auto_refresh_stopped', False):
            # 创建一个占位符来显示刷新状态
            refresh_placeholder = st.empty()

            # 显示刷新倒计时
            for i in range(refresh_interval, 0, -1):
                with refresh_placeholder.container():
                    st.info(f"⏳ {i} 秒后自动刷新...")
                time.sleep(1)

            # 显示正在刷新状态
            with refresh_placeholder.container():
                st.warning("🔄 正在刷新页面...")

            # 清除图表相关的缓存状态
            for key in list(st.session_state.keys()):
                if key.startswith('plotly_') or key.startswith('chart_') or key.startswith('fig_'):
                    del st.session_state[key]

            # 短暂延迟确保状态清除
            time.sleep(0.5)

            # 清空占位符并重新运行
            refresh_placeholder.empty()
            st.rerun()

if __name__ == "__main__":
    main()
