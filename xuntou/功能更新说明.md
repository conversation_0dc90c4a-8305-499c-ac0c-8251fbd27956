# 交易监控系统 - 功能更新说明

## 🔄 更新概览

根据您的要求，我已经对交易监控系统进行了以下重要调整：

### ✅ 已完成的更新

1. **取消账户订单数预警**
2. **成交率预警改为成交率偏离预警**
3. **取消订单时间分布图表**
4. **增强账户订单数量图表**

## 📊 详细更新内容

### 1. 预警系统调整

#### 🚫 移除功能
- **账户订单数预警**: 完全移除了单账户订单数量超过阈值的预警功能
- **账户订单数阈值配置**: 移除了相关的配置选项

#### 🔄 成交率预警 → 成交率偏离预警

**原功能**: 简单的整体成交率低于阈值预警
```
成交率 = 总成交数量 / 总订单数量
如果成交率 < 阈值，则预警
```

**新功能**: 按账户维度的理论成交率与实际成交率偏离预警
```
理论成交率 = Σ(订单数量 × 时间进度) / Σ(订单数量)
实际成交率 = Σ(成交数量) / Σ(订单数量)
偏离度 = |实际成交率 - 理论成交率|
如果偏离度 > 阈值，则预警
```

**时间进度计算**:
```
时间进度 = min(max((当前时间 - 开始时间) / (结束时间 - 开始时间), 0), 1)
```

**预警示例**:
```
账户 ACC001: 实际成交率 45.30%，理论成交率 65.20%，偏离度 19.90% > 阈值 10.00%
```

### 2. 图表系统调整

#### 🚫 移除的图表
- **订单时间分布图**: 完全移除了按小时统计的订单时间分布折线图

#### 🔄 增强的图表
**账户订单数量图表** → **账户综合统计图表**

**原功能**: 简单的Top 10账户订单数量柱状图

**新功能**: 双层图表显示账户综合统计
- **上层**: 账户订单数量 (Top 10)
- **下层**: 账户成交金额统计（买入金额 vs 卖出金额）

**计算逻辑**:
```python
# 买入金额
buy_amount = Σ(买入订单的成交数量 × 成交价格)

# 卖出金额  
sell_amount = Σ(卖出订单的成交数量 × 成交价格)
```

**图表特性**:
- 使用 plotly 子图功能创建组合图表
- 绿色表示买入金额，红色表示卖出金额
- 支持交互式缩放和悬停显示详细信息
- 自动处理数据异常和降级显示

### 3. 配置界面调整

#### 侧边栏预警设置
**更新前**:
```
☑ 错误订单预警
   错误订单阈值: 5
☑ 成交率预警  
   成交率阈值: 80%
☑ 账户订单数预警
   账户订单数阈值: 100
```

**更新后**:
```
☑ 错误订单预警
   错误订单阈值: 5
☑ 成交率偏离预警
   成交率偏离阈值: 10%
```

## 🔧 技术实现细节

### 成交率偏离计算算法

```python
def calculate_fill_rate_deviation(account_df, now):
    theoretical_filled = 0
    total_quantity = 0
    
    for _, row in account_df.iterrows():
        start_time = row['startTime']
        end_time = row['endTime']
        quantity = row['quantity']
        
        # 计算时间进度
        total_duration = (end_time - start_time).total_seconds()
        elapsed_duration = (now - start_time).total_seconds()
        
        if total_duration > 0:
            time_progress = min(max(elapsed_duration / total_duration, 0), 1)
            theoretical_filled += quantity * time_progress
        
        total_quantity += quantity
    
    # 理论成交率
    theoretical_fill_rate = theoretical_filled / total_quantity
    
    # 实际成交率
    actual_filled = account_df['filledQuantity'].sum()
    actual_fill_rate = actual_filled / total_quantity
    
    # 偏离度
    deviation = abs(actual_fill_rate - theoretical_fill_rate)
    
    return theoretical_fill_rate, actual_fill_rate, deviation
```

### 账户统计图表实现

```python
def create_account_stats_chart(df):
    # 按账户分组统计
    account_stats = []
    for account_id in df['accountId'].unique():
        account_df = df[df['accountId'] == account_id]
        
        order_count = len(account_df)
        
        # 分别计算买入和卖出金额
        buy_df = account_df[account_df['operation'] == 'BUY']
        sell_df = account_df[account_df['operation'] == 'SELL']
        
        buy_amount = (buy_df['filledQuantity'] * buy_df['filledPrice']).sum()
        sell_amount = (sell_df['filledQuantity'] * sell_df['filledPrice']).sum()
        
        account_stats.append({
            'accountId': account_id,
            'order_count': order_count,
            'buy_amount': buy_amount,
            'sell_amount': sell_amount
        })
    
    # 创建双层图表
    fig = make_subplots(rows=2, cols=1, ...)
    # 添加订单数量和成交金额图表
```

## 🎯 使用场景

### 成交率偏离预警应用场景

1. **算法执行监控**: 检测算法是否按预期时间进度执行
2. **市场流动性监控**: 识别市场流动性不足导致的成交延迟
3. **异常订单检测**: 发现执行异常或卡单的订单
4. **账户性能评估**: 比较不同账户的执行效率

### 账户统计图表应用场景

1. **资金流向分析**: 观察各账户的买入卖出资金分布
2. **交易活跃度监控**: 识别最活跃的交易账户
3. **风险控制**: 监控单一账户的交易规模
4. **业务分析**: 分析不同账户的交易模式

## 🚀 启动测试

```bash
# 测试版本（推荐先测试新功能）
cd /home/<USER>/trade/xuntou
streamlit run test_monitor.py --server.port 8502

# 主程序版本
streamlit run my_monitor.py --server.port 8501
```

## 📈 预期效果

### 预警系统
- 更精确的成交率监控，能够识别执行偏离
- 减少误报，提高预警的有效性
- 按账户维度的细粒度监控

### 图表系统
- 更丰富的账户统计信息
- 直观的资金流向展示
- 简化的界面，聚焦核心指标

### 用户体验
- 更清晰的配置选项
- 更有针对性的预警信息
- 更实用的数据可视化

所有更新已经同时应用到主程序和测试版本，您可以立即体验新功能！
