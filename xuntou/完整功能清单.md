# 🎯 交易监控系统 - 完整功能清单

## 📋 系统概览

基于 Streamlit 的现代化交易监控系统，提供实时数据监控、智能预警、数据分析和可视化功能。

## ✅ 核心功能模块

### 1. 📅 时间显示功能
- **页面刷新时间**: 实时显示数据更新时间
- **格式**: YYYY-MM-DD HH:MM:SS
- **位置**: 页面顶部醒目位置
- **图标**: 🕒 时钟图标

### 2. 📊 数据可视化模块

#### 关键指标面板
- ✅ 总订单数
- ✅ 运行中订单数
- ✅ 错误订单数
- ✅ 整体成交率
- ✅ 交易品种数

#### 图表系统
1. **订单状态分布饼图**
   - 各状态订单占比
   - 交互式图表

2. **账户订单数量柱状图**
   - Top 10 活跃账户
   - 蓝色柱状图显示

3. **账户成交金额对比图**
   - 绿色柱子：买入金额
   - 红色柱子：卖出金额
   - 并排对比显示

4. **账户成交率分析图** (新增)
   - 蓝色柱子：总体成交率
   - 绿色柱子：买入成交率
   - 红色柱子：卖出成交率
   - 百分比标签显示

### 3. 🚨 智能预警系统

#### 错误订单预警
- 监控 ERROR、CANCELED、EXPIRED 状态订单
- 可配置阈值
- 高优先级预警

#### 成交率偏离预警
- **理论成交率**: Σ(订单数量 × 时间进度) / Σ(订单数量)
- **实际成交率**: Σ(成交数量) / Σ(订单数量)
- **偏离度**: |实际成交率 - 理论成交率|
- 按账户维度监控
- 可配置偏离阈值

### 4. 🔍 高级筛选系统

#### 快速筛选
- **状态筛选**: 多选订单状态
- **账户筛选**: 多选账户ID

#### 自定义条件筛选
- **支持字段**: symbol, status, accountId, operation, algoName, user, quantity, filledQuantity
- **操作符**: 等于、不等于、包含、不包含、大于、小于、大于等于、小于等于
- **多条件**: AND 逻辑连接
- **动态管理**: 添加/删除/编辑条件

### 5. 📈 数据分析功能

#### 分组统计
- 按不同字段分组
- 聚合分析（计数、求和、平均值）
- 实时计算结果

#### 筛选结果统计
- 显示筛选前后记录数量
- 实时更新统计信息

### 6. 💾 数据管理功能

#### 数据获取
- 基于原有 `get_algos()` 函数
- 自动/手动刷新
- 数据状态管理

#### 时间过滤
- 全部数据
- 今天
- 最近1小时
- 最近6小时
- 自定义时间范围

#### 数据导出
- CSV 格式导出
- 包含时间戳的文件名
- 导出筛选后的数据

## 🎮 用户界面设计

### 页面布局
```
📊 交易监控系统
🕒 页面刷新时间: 2025-06-11 11:30:45

🚨 预警信息 (如有预警)
├── 错误订单预警
└── 成交率偏离预警

📈 关键指标
├── 总订单数  ├── 运行中订单  ├── 错误订单  ├── 成交率  ├── 交易品种数

📊 数据可视化
├── 订单状态分布 (饼图)
├── 账户订单数量 (柱状图)
├── 账户成交金额 (买入/卖出对比)
└── 账户成交率分析 (总体/买入/卖出)

🔍 数据分析
└── 分组统计功能

📋 详细数据
├── 🔍 数据筛选
│   ├── [快速筛选] [自定义条件]
│   └── 筛选结果统计
├── 数据表格显示
└── 导出功能
```

### 侧边栏配置
```
配置选项
├── 自动刷新设置
│   ├── 自动刷新开关
│   └── 刷新间隔 (30秒/1分钟/2分钟/5分钟)
├── 时间过滤
│   ├── 时间范围选择
│   └── 自定义时间设置
└── 预警设置
    ├── 错误订单预警开关
    ├── 错误订单阈值
    ├── 成交率偏离预警开关
    └── 成交率偏离阈值 (1%-50%)
```

## 🔧 技术特性

### 前端技术
- **Streamlit**: 现代化 Web 界面
- **Plotly**: 交互式图表
- **Pandas**: 数据处理
- **响应式设计**: 自适应不同屏幕

### 数据处理
- **实时计算**: 动态统计分析
- **内存管理**: session_state 状态管理
- **异常处理**: 完善的错误捕获
- **性能优化**: 数据缓存和分页

### 用户体验
- **直观界面**: 清晰的视觉设计
- **实时反馈**: 即时显示操作结果
- **错误提示**: 友好的错误信息
- **操作指导**: 示例和帮助信息

## 🚀 部署和启动

### 文件结构
```
xuntou/
├── my_monitor.py              # 主程序 (连接真实API)
├── test_monitor.py            # 测试版本 (模拟数据)
├── monitor.py                 # 原始程序 (参考)
├── run_monitor.sh             # 启动脚本
└── 文档/
    ├── 自定义筛选使用指南.md
    ├── 功能演示.md
    ├── 功能更新说明.md
    ├── 图表修复说明.md
    ├── 新增功能说明.md
    └── 完整功能清单.md (本文档)
```

### 启动命令
```bash
# 测试版本 (推荐先测试)
cd /home/<USER>/trade/xuntou
streamlit run test_monitor.py --server.port 8502

# 主程序版本
streamlit run my_monitor.py --server.port 8501

# 使用启动脚本
./run_monitor.sh
```

### 访问地址
- 测试版本: http://localhost:8502
- 主程序: http://localhost:8501

## 💡 使用建议

### 日常监控流程
1. **启动系统**: 选择合适的版本启动
2. **检查时间**: 确认数据刷新时间
3. **查看预警**: 关注预警信息
4. **分析指标**: 查看关键指标面板
5. **深入分析**: 使用图表和筛选功能
6. **导出数据**: 保存重要数据

### 性能优化建议
1. **合理刷新**: 根据需要设置刷新间隔
2. **时间过滤**: 使用时间过滤减少数据量
3. **筛选优化**: 避免过多复杂筛选条件
4. **定期清理**: 清理浏览器缓存

### 业务应用场景
1. **实时监控**: 交易时段的实时监控
2. **性能评估**: 定期的账户性能评估
3. **异常诊断**: 快速定位和分析问题
4. **策略优化**: 基于数据优化交易策略

## 🎯 系统优势

1. **功能完整**: 涵盖监控、预警、分析、导出全流程
2. **界面现代**: 基于 Streamlit 的现代化界面
3. **交互丰富**: Plotly 提供强大的图表交互功能
4. **扩展性强**: 易于添加新功能和定制
5. **稳定可靠**: 完善的错误处理和降级机制

现在您拥有了一个功能完整、性能稳定的专业级交易监控系统！🎉
