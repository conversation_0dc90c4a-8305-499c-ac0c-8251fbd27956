# 交易监控系统 - 快速启动指南

## 🚀 快速开始

### 1. 启动主监控程序（连接真实API）
```bash
# 方法1: 使用启动脚本
./run_monitor.sh

# 方法2: 直接运行
streamlit run my_monitor.py --server.port 8501
```
访问地址: http://localhost:8501

### 2. 启动测试版本（使用模拟数据）
```bash
streamlit run test_monitor.py --server.port 8502
```
访问地址: http://localhost:8502

## 📊 主要功能

### 实时监控面板
- **关键指标**: 总订单数、运行中订单、错误订单、成交率、交易品种数
- **状态分布**: 订单状态饼图
- **账户分析**: Top 10 账户订单分布
- **时间分析**: 订单时间分布图

### 预警系统
- 错误订单数量预警
- 成交率低于阈值预警  
- 单账户订单数过多预警

### 数据分析
- 按不同字段分组统计
- 多维度数据筛选
- 数据导出功能

## ⚙️ 配置选项

### 侧边栏设置
1. **自动刷新**: 开启后可设置30秒-5分钟的刷新间隔
2. **时间过滤**: 支持今天、最近1小时、最近6小时、自定义时间范围
3. **预警阈值**: 可自定义各种预警的触发条件

### 数据筛选
- 按订单状态筛选
- 按账户ID筛选
- 按交易品种筛选

## 🔧 故障排除

### 如果无法访问
1. 检查端口是否被占用
2. 确认防火墙设置
3. 尝试使用不同端口

### 如果数据无法加载
1. 检查API服务状态
2. 确认网络连接
3. 查看错误日志

### 停止服务
按 `Ctrl+C` 停止运行中的服务

## 📁 文件说明
- `my_monitor.py`: 主程序（真实数据）
- `test_monitor.py`: 测试程序（模拟数据）
- `run_monitor.sh`: 启动脚本
- `README_monitor.md`: 详细文档

## 💡 使用建议
1. 首次使用建议先运行测试版本熟悉界面
2. 根据实际需求调整预警阈值
3. 定期导出重要数据进行备份
4. 在生产环境中建议使用后台运行模式
