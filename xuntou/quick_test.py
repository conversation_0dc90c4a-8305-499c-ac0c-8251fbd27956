#!/usr/bin/env python3
"""
快速测试修复的功能
"""

def test_plotly_fix():
    """测试 plotly 修复"""
    try:
        import plotly.express as px
        import pandas as pd
        
        # 创建测试数据
        df = pd.DataFrame({
            'account': ['ACC001', 'ACC002', 'ACC003'],
            'count': [10, 20, 15]
        })
        
        # 测试修复后的代码
        fig = px.bar(
            x=df['account'],
            y=df['count'],
            title="测试图表"
        )
        
        # 这是修复后的方法
        fig.update_layout(xaxis_tickangle=45)
        
        print("✓ Plotly 修复成功 - update_layout 方法正常工作")
        return True
        
    except Exception as e:
        print(f"✗ Plotly 测试失败: {e}")
        return False

def test_dataframe_display():
    """测试 DataFrame 显示功能"""
    try:
        import pandas as pd
        
        # 创建测试数据
        data = {
            'id': ['ORDER_001', 'ORDER_002', 'ORDER_003'],
            'symbol': ['AAPL', 'GOOGL', 'MSFT'],
            'status': ['FILLED', 'RUNNING', 'ERROR'],
            'accountId': ['ACC001', 'ACC002', 'ACC001'],
            'quantity': [100, 200, 150]
        }
        df = pd.DataFrame(data)
        
        # 测试筛选功能
        status_filter = ['FILLED', 'RUNNING']
        filtered_df = df[df['status'].isin(status_filter)]
        
        # 测试分组功能
        grouped = df.groupby('accountId')['quantity'].agg(['count', 'sum', 'mean'])
        
        print("✓ DataFrame 筛选和分组功能正常")
        print(f"  原始数据: {len(df)} 行")
        print(f"  筛选后: {len(filtered_df)} 行")
        print(f"  分组结果: {len(grouped)} 组")
        
        return True
        
    except Exception as e:
        print(f"✗ DataFrame 测试失败: {e}")
        return False

def main():
    print("=" * 40)
    print("快速功能测试")
    print("=" * 40)
    
    tests = [
        ("Plotly 修复", test_plotly_fix),
        ("DataFrame 功能", test_dataframe_display)
    ]
    
    passed = 0
    for name, test_func in tests:
        print(f"\n测试 {name}...")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 40)
    print(f"测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 修复成功！可以启动监控系统了。")
    else:
        print("❌ 仍有问题需要解决。")

if __name__ == "__main__":
    main()
