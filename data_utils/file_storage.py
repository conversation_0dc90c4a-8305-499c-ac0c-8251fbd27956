
import boto3
import botocore
import obs
# from obs import obsClient
from io import BytesIO, StringIO
import os
import pandas as pd
from loguru import logger

# print('Downloading an object as a stream object')
# resp = obsClient.getObject(bucketName, objectKey, loadStreamInMemory=True)
# print('stream object content:')
# print(resp.body.buffer)
class FileStorage:
    def __init__(self, 
                 storage_type='local', 
                 prefix='', 
                 access_key_id=None, 
                 secret_access_key=None,
                 region_name=None, # aws region endpoint, ex, "cn-northwest-1"
                 endpoint=None,   # huawei server url, ex, "obs.cn-east-3.myhuaweicloud.com"
                 **kwargs
                 ):
        # kwargs for connection session: s3, region_name, like cn-northwest-1, us-east-1
        self.storage_type = storage_type
        self.prefix = prefix
        if self.storage_type == 's3':
            self.s3 = boto3.resource('s3',
                        aws_access_key_id=access_key_id,
                        aws_secret_access_key=secret_access_key,
                        region_name=region_name,
                        **kwargs
            )
                                     
        elif self.storage_type == 'hwobs':
            self.s3 = obs.ObsClient(
                        access_key_id=access_key_id, 
                        secret_access_key=secret_access_key,
                        server=endpoint
            )
        # TODO: hwobs init
        # elif self.storage_type == 'hwobs':
        #     self.obs = obsClient(access_key_id=access_key_id, secret_access_key=secret_access_key, server=server)

    def exists(self, path):
        path = os.path.join(self.prefix, path)
        if self.storage_type == 'local':
            return os.path.exists(path)
        
        elif self.storage_type == 's3':
            bucket, key = self._parse_s3_path(path)
            # root path
            if key == '':
                s3_bucket = self.s3.Bucket(bucket)
                try:
                    s3_bucket.load()
                except botocore.exceptions.ClientError as e:
                    if e.response['Error']['Code'] == "404":
                        return False
                    else:
                        raise
                return True

            s3_object = self.s3.Object(bucket, key)
            try:
                s3_object.load()
            except botocore.exceptions.ClientError as e:
                if e.response['Error']['Code'] == "404":
                    return False
                else:
                    raise
            return True
        
        # TODO: hwobs 'exists'
        elif self.storage_type == 'hwobs':
            bucket, key = self._parse_hwobs_path(path)
            response = self.s3.getObjectMetadata(bucketName=bucket, objectKey=key)
            return True if response['status'] == 200 else False

    def ls(self, path):
        relative_path = path
        path = os.path.join(self.prefix, path)
        if self.storage_type == 'local':
            return os.listdir(path)

        # list all the files in the bucket 
        elif self.storage_type == 's3':
            bucket, key = self._parse_s3_path(path)
            s3_bucket = self.s3.Bucket(bucket)
            # root path
            if key == '':
                return [obj.key for obj in s3_bucket.objects.all()]

            # check if path exists
            if not self.exists(relative_path):
                raise FileExistsError(path)
            return [obj.key for obj in s3_bucket.objects.filter(Prefix=key)]

        elif self.storage_type == 'hwobs':
            bucket, key = self._parse_hwobs_path(path)
            # check if path exists
            if not self.exists(relative_path):
                raise FileExistsError(path)
            response = self.s3.listObjects(bucketName=bucket, prefix=key)
            return [obj.key for obj in response.body.contents]
    
    def mkdir(self, path):
        path = os.path.join(self.prefix, path)
        if self.storage_type == 'local':
            os.makedirs(path, exist_ok=True)
        
        elif self.storage_type == 's3':
            bucket, key = self._parse_s3_path(path)
            s3_object = self.s3.Object(bucket, key)
            s3_object.put(Body='')
        
        elif self.storage_type == 'hwobs':
            bucket, key = self._parse_hwobs_path(path)
            self.s3.putContent(bucketName=bucket, objectKey=key, content='')
        
    def delete(self, path):
        path = os.path.join(self.prefix, path)
        if self.storage_type == 'local':
            os.remove(path)
        
        elif self.storage_type == 's3':
            bucket, key = self._parse_s3_path(path)
            s3_object = self.s3.Object(bucket, key)
            s3_object.delete()

        elif self.storage_type == 'hwobs':
            bucket, key = self._parse_hwobs_path(path)
            self.s3.deleteObject(bucketName=bucket, objectKey=key)
        

    def read(self, path, **kwargs):
        """read file from storage

        Args:
            path (str): file path
            **kwargs: kwargs for pandas.read_* functions
        """
        relative_path = path
        path = os.path.join(self.prefix, path)
        if not self.exists(relative_path):
            raise FileExistsError(path)

        logger.info('read from path: {} ...'.format(path))
        if self.storage_type == 'local':
            ext = os.path.splitext(path)[1]
            if ext in ['.feather', '.fea']:
                return pd.read_feather(path, **kwargs)
            elif ext in ['.parquet', '.par']:
                return pd.read_parquet(path, **kwargs)
            elif ext == '.csv':
                return pd.read_csv(path, **kwargs)
            elif ext in ['.xlsx', '.xls']:
                return pd.read_excel(path, **kwargs)
            else:
                # not a pandas supported file format
                with open(path, 'r') as f:
                    return f.read()

        elif self.storage_type == 's3':
            bucket, key = self._parse_s3_path(path)
            s3_object = self.s3.Object(bucket, key)
            ext = os.path.splitext(key)[1]
            buffer = BytesIO(s3_object.get()['Body'].read())
            if ext in ['.feather', '.fea']:
                return pd.read_feather(buffer, **kwargs)
            elif ext in ['.parquet', '.par']:
                return pd.read_parquet(buffer, **kwargs)
            elif ext == '.csv':
                return pd.read_csv(buffer, **kwargs)
            elif ext in ['.xlsx', '.xls']:
                return pd.read_excel(buffer, **kwargs)
            else:
                # not a pandas supported file format
                buffer = StringIO(s3_object.get()['Body'].read().decode('utf-8'))
                return buffer.read()

        # TODO: hwobs read
        elif self.storage_type == 'hwobs':
            bucket, key = self._parse_hwobs_path(path)
            resp = self.s3.getObject(bucketName=bucket, objectKey=key)
            ext = os.path.splitext(key)[1]
            if resp.status < 300:
                buffer = BytesIO(resp.body.response.read())
                if ext in ['.feather', '.fea']:
                    return pd.read_feather(buffer, **kwargs)
                elif ext in ['.parquet', '.par']:
                    return pd.read_parquet(buffer, **kwargs)
                elif ext == '.csv':
                    return pd.read_csv(buffer, **kwargs)
                elif ext in ['.xlsx', '.xls']:
                    return pd.read_excel(buffer, **kwargs)
                else:
                    # not a pandas supported file format
                    return buffer.read()

    def write(self, path, content, overwrite=False):
        relative_path = path
        path = os.path.join(self.prefix, path)
        if self.exists(relative_path):
            if overwrite == False:
                logger.warning('file already exists, skip writing: {} \
                                \npass overwrite=True to force overwrite'.format(path))
                raise FileExistsError(path)
            else:
                logger.warning('file already exists, overwriting: {}'.format(path))
        else:
            logger.info('write to path: {} ...'.format(path))

        if self.storage_type == 'local':
            ext = os.path.splitext(path)[1]
            # tmp_path = f'{path}.tmp_{uuid.uuid4()}'
            if isinstance(content, (pd.DataFrame, pd.Series)):
                if ext == '.csv':
                    content.to_csv(path, index=False)
                elif ext in ['.feather', '.fea']:
                    content.to_feather(path)
                elif ext in ['.parquet', '.par']:
                    content.to_parquet(path, engine='pyarrow')
                elif ext in ['.xls', '.xlsx']:
                    content.to_excel(path, index=False)
                else:
                    raise ValueError(f'Unsupported file format: {ext}')
            else:
                with open(path, 'w') as f:
                    f.write(content)
            # os.replace(tmp_path, path)

        elif self.storage_type == 's3':
            bucket, key = self._parse_s3_path(path)
            s3_object = self.s3.Object(bucket, key)
            ext = os.path.splitext(key)[1]
            if isinstance(content, (pd.DataFrame, pd.Series)):
                if ext == '.csv':
                    s3_object.put(Body=content.to_csv(index=False))
                elif ext in ['.feather', '.fea']:
                    with BytesIO() as f:
                        content.to_feather(f)
                        s3_object.put(Body=f.getvalue())
                elif ext in ['.parquet', '.par']:
                    with BytesIO() as f:
                        content.to_parquet(f, engine='pyarrow')
                        s3_object.put(Body=f.getvalue())
                elif ext in ['.xls', '.xlsx']:
                    with BytesIO() as f:
                        content.to_excel(f, index=False)
                        s3_object.put(Body=f.getvalue())
                else:
                    raise ValueError(f'Unsupported file format: {ext}')
            else:
                with StringIO() as f:
                    f.write(content)
                    s3_object.put(Body=f.getvalue())

        elif self.storage_type == 'hwobs':
            bucket, key = self._parse_hwobs_path(path)
            ext = os.path.splitext(key)[1]
            if isinstance(content, (pd.DataFrame, pd.Series)):
                if ext == '.csv':
                    self.s3.putContent(bucketName=bucket, objectKey=key, content=content.to_csv(index=False))
                elif ext in ['.feather', '.fea']:
                    with BytesIO() as f:
                        content.to_feather(f)
                        self.s3.putContent(bucketName=bucket, objectKey=key, content=f.getvalue())
                elif ext in ['.parquet', '.par']:
                    with BytesIO() as f:
                        content.to_parquet(f, engine='pyarrow')
                        self.s3.putContent(bucketName=bucket, objectKey=key, content=f.getvalue())
                elif ext in ['.xls', '.xlsx']:
                    with BytesIO() as f:
                        content.to_excel(f, index=False)
                        self.s3.putContent(bucketName=bucket, objectKey=key, content=f.getvalue())
                else:
                    raise ValueError(f'Unsupported file format: {ext}')
            else:
                with StringIO() as f:
                    f.write(content)
                    self.s3.putContent(bucketName=bucket, objectKey=key, content=f.getvalue())

    def upload(self, local_path, s3_path):
        if self.storage_type == 's3':
            s3_path = os.path.join(self.prefix, s3_path)
            bucket, key = self._parse_s3_path(s3_path)
            self.s3.Bucket(bucket).upload_file(local_path, key)
        elif self.storage_type == 'hwobs':
            s3_path = os.path.join(self.prefix, s3_path)
            bucket, key = self._parse_hwobs_path(s3_path)
            self.s3.putFile(bucketName=bucket, objectKey=key, file_path=local_path)
        else:
            raise ValueError(f'Unsupported storage type: {self.storage_type}')

    def download(self, s3_path, local_path):
        if self.storage_type == 's3':
            s3_path = os.path.join(self.prefix, s3_path)
            bucket, key = self._parse_s3_path(s3_path)
            self.s3.Bucket(bucket).download_file(key, local_path)
        elif self.storage_type == 'hwobs':
            s3_path = os.path.join(self.prefix, s3_path)
            bucket, key = self._parse_hwobs_path(s3_path)
            self.s3.getObject(bucketName=bucket, objectKey=key, downloadPath=local_path)
        else:
            raise ValueError(f'Unsupported storage type: {self.storage_type}')

    def upload_s3_directory(self, local_directory, s3_directory):
        if self.storage_type == 's3':
            s3_directory = os.path.join(self.prefix, s3_directory)
            bucket, prefix = self._parse_s3_path(s3_directory)
            for root, dirs, files in os.walk(local_directory):
                for file in files:
                    local_path = os.path.join(root, file)
                    s3_path = os.path.join(prefix, os.path.relpath(local_path, local_directory))
                    self.upload(local_path, os.path.join(bucket, s3_path))
        else:
            raise ValueError(f'Unsupported storage type: {self.storage_type}')

    def download_s3_directory(self, s3_directory, local_directory):
        if self.storage_type == 's3':
            s3_directory = os.path.join(self.prefix, s3_directory)
            bucket, prefix = self._parse_s3_path(s3_directory)
            os.makedirs(local_directory, exist_ok=True)
            for obj in self.s3.Bucket(bucket).objects.filter(Prefix=prefix):
                if not obj.key.endswith('/'):  # Exclude directories
                    local_path = os.path.join(local_directory, os.path.relpath(obj.key, prefix))
                    os.makedirs(os.path.dirname(local_path), exist_ok=True)
                    self.download(obj.key, local_path)
        else:
            raise ValueError(f'Unsupported storage type: {self.storage_type}')
        

    def _is_s3_path(self, path:str):
        return path.startswith("s3://")

    def _parse_s3_path(self, path:str):
        if self._is_s3_path(path):
            path = path[5:]
        elif path.startswith("s3n://"):
            path = path[6:]
        elif path.startswith("s3a://"):
            path = path[6:]
        elif path.startswith("s3p://"):
            path = path[6:]
        else:
            raise ValueError(f'Unsupported s3 path: {path}')

        bucket_and_key = path.split('/', 1)
        bucket = bucket_and_key[0]
        key = bucket_and_key[1] if len(bucket_and_key) > 1 else ''
        return bucket, key

    def _parse_hwobs_path(self, path:str):
        try:
            bucket_and_key = path.split('/', 1)
            bucket = bucket_and_key[0]
            key = bucket_and_key[1] if len(bucket_and_key) > 1 else ''
            return bucket, key
        except:
            raise ValueError(f'Unsupported s3 path: {path}')


