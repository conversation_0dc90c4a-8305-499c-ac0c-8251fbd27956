{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from loguru import logger\n", "from file_storage import FileStorage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aws_access_key_id = '********************'\n", "aws_secret_access_key = '2qXhDqmJ/q6WEHYN586onjORS7N0SxzoZOWAqPOa'\n", "fs = FileStorage('s3', 'testss0001', access_key_id=aws_access_key_id, secret_access_key=aws_secret_access_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# aws_access_key_id = '********************'\n", "# aws_secret_access_key = 'KwKvnlWsgV+MIge8uMjJx2B0qXS0RcngV2/jsI7t'\n", "# fs = FileStorage('s3', 'testss0002', access_key_id=aws_access_key_id, secret_access_key=aws_secret_access_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dir = '/data/factors/eod/v2/train/b111_b112_b113_b114_barra_b106_103_ret_10d_rank_norm/1400/'\n", "if not os.path.exists(dir):\n", "    os.make<PERSON>s(dir)\n", "fs.download('data/factors/eod/v2/train/b111_b112_b113_b114_barra_b106_b103_ret_10d_rank_norm/1400/predicts.csv', os.path.join(dir, 'predicts.csv'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!head /data/factors/eod/v2/train/b1x_b301_barra_ret_10d_rank_norm/1500/predicts.csv"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# name = \"b1x_b301\"\n", "# n_days = 10\n", "# y_suffix = \"rank_norm\"\n", "\n", "name = \"b301\"\n", "# name = \"b1x\"\n", "# n_days = 10\n", "n_days = 5\n", "# n_days = 2\n", "y_suffix = \"dmean\"\n", "# y_suffix = \"rank_norm\"\n", "if y_suffix == \"rank_norm\":\n", "    dir = '/data/factors/eod/v2/train/{}_barra_ret_{}d_{}/1500/'.format(name, n_days, y_suffix)\n", "else:\n", "    dir = '/data/factors/eod/v2/train/{}_barra_b103_b106_ret_{}d_{}/1500/'.format(name, n_days, y_suffix)\n", "\n", "if not os.path.exists(dir):\n", "    os.make<PERSON>s(dir)\n", "if y_suffix == \"rank_norm\":\n", "    src_file = 'data/factors/eod/v2/train/{}_barra_ret_{}d_{}/predicts.csv'.format(name, n_days, y_suffix)\n", "else:\n", "    src_file = 'data/factors/eod/v2/train/{}_barra_b103_b106_ret_{}d_{}/predicts.csv'.format(name, n_days, y_suffix)\n", "    \n", "dst_file = os.path.join(dir, 'predicts.csv')\n", "logger.info(\"src_file: {}, dst_file: {}\".format(src_file, dst_file))\n", "fs.download(src_file, dst_file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fs.download_directory('data/factors/eod/v2/train/b1x_b301_barra_ret_10d_rank_norm', '/data/factors/eod/v2/train/b1x_b301_barra_ret_10d_rank_norm/1500_02')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fs.upload('/root/git/train_v3/lf/opt.tgz', 'data/opt.tgz')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from glob import glob\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# files = glob('/dl1_data/factors/eod/v2/merged/lf_x_y/1400/*.fea')\n", "# files = glob('/dl1_data/factors/eod/v2/merged/lf_mv0.0_x_y/1400/202*.fea')\n", "files = glob('/data/factors/eod/v2/merged/b1x_b301_b103_b106_barra_y_mv0.0_x_y/1500/*.fea')\n", "files = sorted(files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "a = pickle.load(open('/dl1_data/factors/eod/v1/merged/x_y/x_names_map.pkl', 'rb'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# files\n", "fs.upload('/data/factors/eod/v2/x_names_map_b103_b106_b1x_b301_ls2.0_lb1.3.pkl', 'data/factors/eod/v2/x_names_map_b103_b106_b1x_b301_ls2.0_lb1.3.pkl')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for file in tqdm(files):\n", "    if file[0] == '/':\n", "        s3_file = file[1:]\n", "    else:\n", "        s3_file = file\n", "    print(file, s3_file)\n", "    fs.upload(file, s3_file)\n", "    # fs.upload('/data/v5/factors/eod/v1/merged/x_y/1400/202302.fea', 'data/v5/factors/eod/v1/merged/x_y/1400/202302.fea')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = fs.read('data/v5/factors/eod/v1/merged/x_y/1400/202302.fea')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import obs\n", "\n", "# # Create an OBS client\n", "# obs_client = obs.ObsClient(\n", "#     access_key_id='<your access key ID>',\n", "#     secret_access_key='<your secret access key>',\n", "#     server='<your endpoint URL>'\n", "# )\n", "\n", "# def read_feather_from_obs(path, **kwargs):\n", "#     bucket, key = self._parse_s3_path(path)\n", "#     resp = obs_client.getObject(bucket, key)\n", "#     body = resp.body\n", "#     ext = os.path.splitext(key)[1]\n", "#     if ext in ['.feather', '.fea']:\n", "#         return pd.read_feather(body, **kwargs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}