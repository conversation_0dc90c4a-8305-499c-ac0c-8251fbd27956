import os
import pandas as pd
from tqdm import tqdm
from loguru import logger

class DataSet:
    def __init__(self, fs, cal):
        self.fs = fs
        self.cal = cal
        
    def read_range(self, uri, start_date, end_date, date_col_name='date', suffix='feather', ignore_missing=False):
        dates = self.cal.trading_dates(start_date, end_date)
        data = pd.DataFrame()
        desc = 'read data, uri: {}, start_date:{}, end_dates: {}, n_dates: {}'.format(
            uri, start_date, end_date, len(dates))
        for date in tqdm(dates, desc):
            path = uri + '{}.{}'.format(date.strftime('%Y%m%d'), suffix)
            try:
                data_ = self.fs.read(path)
            except Exception as e:
                logger.error(e)
                if not ignore_missing:
                    raise e
            if date_col_name not in data.columns:
                data[date_col_name] = date
            data = pd.concat([data, data_], ignore_index=True)
        return data
                    
    def write_range(self, data, uri, date_col_name='date', suffix='feather'):
        start_date, end_date = data[date_col_name].min(), data[date_col_name].max()
        groups = data.groupby(date_col_name)
        desc = 'write data, uri: {}, start_date:{}, end_date: {}, suffix: {}, date_col_name: {}'.format(
            uri, start_date, end_date, suffix, date_col_name)
        for date, group in tqdm(groups, desc):
            path = uri + '{}.{}'.format(date.strftime('%Y%m%d'), suffix)
            self.fs.write(path, group.reset_index(drop=True))

    def exists(self, uri, date, suffix='feather'):
        path = uri + '{}.{}'.format(date.strftime('%Y%m%d'), suffix)
        return self.fs.exists(path)

    def read(self, uri, date, suffix='feather'):
        path = uri + '{}.{}'.format(date.strftime('%Y%m%d'), suffix)
        return self.fs.read(path)
    
    def write(self, data, uri, date, suffix='feather'):
        path = uri + '{}.{}'.format(date.strftime('%Y%m%d'), suffix)
        self.fs.write(path, data)
