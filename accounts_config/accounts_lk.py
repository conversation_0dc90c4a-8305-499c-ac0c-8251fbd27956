
from misc.ssh_conn import sftp_clent_inner, sftp_clent_outer
import pandas as pd
import os
from loguru import logger

#%%
accounts_lk = {
    'outer_ftp_host' : 'root@**************',
    'outer_ftp_root' : '/data/ftp/lk',
    'account_name_alias' : {
        '量客中性DA28H_S': 'DA28H_S',
        '量客指数增强ZY21H_S': 'ZY21H_S',
        '量客中性LingTaiDCL3Q_S': 'LingTaiDCL3Q_S',

        '量客中性LingTaiDCL6Q_S': 'LingTaiDCL6Q_S',
        '量客中性WenXingDCL6H_S': 'WenXingDCL6H_S',
        '量客中性LingTaiDCL1Q_S': 'LingTaiDCL1Q_S',
        '量客中性DA25H_S': 'DA25H_S',
        '量客中性DA22H_S': 'DA22H_S',
        # '量客中性DA17H_S': 'DA17H_S',
        '量客中性JPZGDC1H_S': 'JPZGDC1H_S',
        '量客指数增强ZY1H_S': 'ZY1H_S',
        '量客指数增强ZY6H_S': 'ZY6H_S',
        '量客指数增强ZY15H_S': 'ZY15H_S',
        

        '量客中性DA9H_S': 'DA9H_S',
        '量客中性LingTaiDCL1H_S': 'LingTaiDCL1H_S',
        '量客中性DFZMDCL1H_S': 'DFZMDCL1H_S',
        '量客中性DFZMDCL1H_C': 'DFZMDCL1H_C',
        '量客中性DA7H_S': 'DA7H_S',
        '量客中性HXZX1H_S': 'HXZX1H_S',
        '量客中性DA31H_S_SD': 'DA31H_S_SD',
        
        # '量客中性DA32H_S': 'DA32H_S',
        '量客中性ChengY1H_S': 'ChengY1H_S',
        '量客中性DA9H_S_HB': 'DA9H_S_HB',
        
        # 20230630 新增
        # '量客中性SX1H_S': 'SX1H_S',
        
        # 20230703 新增
        # '量客中性KFDCAH_S': 'KFDCAH_S',
        '量客中性DA35H_S': 'DA35H_S',
        
        # 20230911 删除
        # '量客中性DA1H_S': 'DA1H_S',
        
        '量客指数增强XTDCL1Q_S': 'XTDCL1Q_S',
        '量客指数增强ZY26H_S': 'ZY26H_S',
        '量客中性DA9H_C': 'DA9H_C',
        '量客中性ZhongY1H_S': 'ZhongY1H_S',

        # 20230702 删除
        # '量客中性DA31H_S_SX': 'DA31H_S_SX',

        # 20230704 清除
        # '量客中性HangMuDCL3H_S': 'HangMuDCL3H_S',
        
        # 20230705 新增, 20230910 删除
        # '量客指数增强ZhongY10H_S': 'ZhongY10H_S',
        
        # 0713 新增
        '量客指数增强ZY22H_S' : 'ZY22H_S',
        # 0720 新增
        '量客中性KFDCAH_S_GZ' : 'KFDCAH_S_GZ',
        
        # 删除
        # '量客中性SLZ1H_S': 'SLZ1H_S',
        
        # ******** add
        '量客中性DA5H_S_GZ' : 'DA5H_S_GZ',
        # ******** del
        # '量客中性DA16H_S': 'DA16H_S',

        # ******** add
        '量客中性DA7H_C' : 'DA7H_C',

    },
}

#%%
def get_lk_account_config(date):
    lk_config_file = 'others/lk_target/target_{}.xlsx'.format(date)
    local_file = 'tmp/lk_config_file'
    sftp_clent_inner.get(lk_config_file, local_file)
    df = pd.read_excel(local_file)
    df = df.sort_values(by='IC')
    df['product_prefix'] = df['IC'].apply(lambda x: '量客指数增强' if x ==0 else '量客中性')
    df['product_fullname'] = df['product_prefix'] + df['product_id']
    new_dict = {}

    for row, values in df.iterrows():
        new_dict.update({values['product_fullname'] : values['product_id']})
    # accounts_lk.update({'account_name_alias' : new_dict})
    # return accounts_lk
    return new_dict


# %%
# check if the new config is the same as the old one
def check_inner_lk_accounts(accounts_lk, date):
    updated_inner_lk_accounts = get_lk_account_config(date)
    
    missing_accounts = {}
    expire_accounts = {}
    # 如果更新的dict 中的key, 不存在于原来的dict中
    for k, v in updated_inner_lk_accounts.items():
        if accounts_lk['account_name_alias'].get(k, None) != v:
            missing_accounts.update({k : v})
            accounts_lk['account_name_alias'].update({k : v})
            
            # 如果文件夹不存在, 新建
            directories = sftp_clent_inner.listdir()
            if not k in directories:
                sftp_clent_inner.mkdir(k)
                sftp_clent_inner.mkdir(os.path.join(k, 'account'))
                sftp_clent_inner.mkdir(os.path.join(k, 'hold'))
                sftp_clent_inner.mkdir(os.path.join(k, 'position'))
                sftp_clent_inner.mkdir(os.path.join(k, 'weight'))
                logger.warning('inner mkdir finished : {}'.format(k))
            
            directories = sftp_clent_outer.listdir('lk')
            if not v in directories:
                sftp_clent_outer.mkdir(os.path.join('lk', v))
                sftp_clent_outer.mkdir(os.path.join('lk', v, 'target'))
                sftp_clent_outer.mkdir(os.path.join('lk', v, 'holding'))
                logger.warning('outer mkdir finished : {}'.format(v))

    # 如果现有的dict 中的key, 不存在于inner的dict中
    for k, v in accounts_lk['account_name_alias'].items():
        if updated_inner_lk_accounts.get(k, None) != v:
            expire_accounts.update({k : v})
     
    if len(missing_accounts) != 0:
        logger.warning("""
            
                    
            缺少inner服务器lk账户配置
            missing accounts:
            
            {}
                    
                    
                    """.format(missing_accounts))
        
    if len(expire_accounts) != 0:
        logger.warning("""
            
                    
            inner服务器lk账户配置已删除！
            expire accounts:
            
            {}
                    
                    
                    """.format(expire_accounts))