# note
# 如果同步计算orders 时，账户没有hold，可以设置 first_day_no_hold=True

accounts_kf = {
    'outer_ftp_host' : 'root@**************',
    'outer_ftp_root' : '/data/ftp/kf',
    'accounts' : {
        # '中信多空': {
        #     'format': 'excel',
        #     'args': {
        #         'dtype': {
        #             '代码': int, 
        #             '持仓量': int, 
        #             '最新价': float,
        #             '市值': float
        #         },
        #     },
        #     # 实际不用迅投
        #     'order_type' : 'xt1',
        # },
        
        '宽辅专享2号': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                },
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        
        '远航安心中性1号': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                },
            },
            'order_type' : 'gtrade',
            'pnl_file' : '宽辅系列.xlsx',
        },
        
        '远航安心中性1号_银河': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                },
            },
            'order_type' : 'yinhedma_kafang',
        },
        
        '远航安心中性1号_招商': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                },
            },
            'order_type' : 'zhaoshang_swap',
        },
        
        '宽辅泓涛专享1号': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'gtrade',
        },
        
        '宽辅泓涛专享1号_广发': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            # 实际 order 不使用xt1
            'order_type' : 'xt1',
        },
        
        '宽辅泓涛专享1号_东方': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'dfdma_kafang',
        },
        
        '宽辅专享1号': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'gtrade',
        },
        
        '宽辅专享1号_浙商': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            # 实际不是迅投, 不用于下单
            'order_type' : 'xt1',
        },
        
        '远航安心中性6号_中信': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                },
            },
            # 实际不是迅投, 不用于下单
            'order_type' : 'xt1',
        },

        '远航安心中性6号_浙商': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                },
            },
            # 实际不是迅投, 不用于下单
            'order_type' : 'xt1',
        },

        '远航安心中性6号_银河': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                },
            },
            # 实际不是迅投, 不用于下单
            'order_type' : 'xt1',
        },
        '远航安心中性6号_国君': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                },
            },
            # 实际不是迅投, 不用于下单
            'order_type' : 'xt1',
        },


        
        
        
        
        '宽辅联丰专享': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        
        '宽辅联丰专享_国联': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            # 实际不是xt1, 不用于下单
            'order_type' : 'xt1',
        },
        
        '宽辅思贤专享中性1号': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },

        '宽辅思贤专享中性1号_广发': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'first_day' : '20231124',
            'gt_order' : True,
        },


        '远航安心中性2号_民生': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        '远航安心中性2号_安信': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        
        '远航安心中性2号_东方': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        
        '远航安心中性3号': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        
        '远航安心中性5号': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },

        '宽辅500指增1号_安信': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        
        '宽辅500指增1号_天风': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        
        '宽辅1000指增1号_招商': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        '宽辅1000指增1号_平安': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        
        '宽辅量化选股1号_民生': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'gt_order' : True,
        },
        '宽辅量化选股1号_东北': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'first_day' : '20240126',
            'gt_order' : True,
        },
        
        '宽辅专享5号': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            # 实际不是迅投, 不用于下单
            'order_type' : 'xt1',
            'first_day' : '20231127',
            'gt_order' : False,
        },
        
        '宽辅金品中国专享': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'first_day' : '20240115',
            'gt_order' : True,
        },
        
        '宽辅泓涛专享2号': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'first_day' : '20240115',
            'gt_order' : True,
        },
        '宽辅专享6号': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'first_day' : '20240126',
            'gt_order' : True,
        },
        
        '宽辅思贤专享中性2号_广发': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'first_day' : '20240226',
            'gt_order' : True,
        },
        
        '远航安心中性6号_国联': {
            'format': 'excel',
            'args': {
                'dtype': {
                    '代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            # 实际不是迅投, 不用于下单
            'order_type' : 'xt1',
            'first_day' : '20240227',
            'gt_order' : False,
        },
    }
}
