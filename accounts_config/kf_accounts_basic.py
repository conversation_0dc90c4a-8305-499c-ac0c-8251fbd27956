
kf_pnl_file = '宽辅系列.xlsx'
kf_pnl_account = '远航安心中性1号'

account_config_file = 'others/account_config.xlsx'
account_config_src = 'dav'



gtrade_account_dir_files_dict = {
            # 'asset.{}_2.{}.csv'.format(account_id, '{}'),
            # 'suborder.{}_2.{}.csv'.format(account_id, '{}'),
            # 'order.{}_2.{}.csv'.format(account_id, '{}'),
            # 'deal.{}_2.{}.csv'.format(account_id, '{}'),
            'account_file' :      'Account-{}.csv',
            'execution_file_11' : 'Order-{}.csv',
            'execution_file_12' : 'Suborder-{}.csv',
            'execution_file_21' : 'AlgoOrder-{}.csv',
            'execution_file_22' : 'AlgoSuborder-{}.csv',
            'deal_file' :         'Deal-{}.csv',
}

gtrade_hold_dir_files_dict = {
    # 'position.{}_2.{}.csv'.format(account_id, '{}'),
    'position_file' : 'Position-{}.csv',
}

xt_qmt_account_dir_files_dict = {
    'execution_file' : 'Order-{}.csv',
    'deal_file' :      'Deal-{}.csv',
    'account_file' :   'Account-{}.csv',
}

xt_qmt_hold_dir_files_dict = {
    'position_file' : 'PositionStatics-{}.csv',
}

duotou_products = [
    '宽辅中证500指数增强1号',
    '宽辅中证1000指数增强1号',
    # '宽辅量化选股1号',
    '宽辅泓涛专享2号',
    '宽辅臻好专享'
]

yinhe_dma_black_list_accounts = [
    '远航安心中性1号_银河',
    '远航安心中性6号_银河',
    # '宽辅专享5号',
]


gtrade_future_account_dict = {
    
    '远航安心中性1号_**************' : {
        'product_name' : '远航安心中性1号',
        'account_id' : '**************',
        'account_name_investor' : '远航安心中性1号',
        'stock_account_name' : '远航安心中性1号',
        'account_alias' : '对冲端（国信证券dma）',
        # 'host_dir' : '交易软件/GTrade1.33版本-生产环境-远航安心中性1号/export_data/Future',
        'host_dir' : '数据导出/远航安心中性1号/Future',
        # 'account_file' : '',
        'future_dir_files_list' : [
            'Position-{}.csv',
            'Account-{}.csv',
            'Order-{}.csv',
            'Deal-{}.csv',
        ],
    },
    
    '宽辅泓涛专享1号_**************' : {
        'product_name' : '宽辅泓涛专享1号',
        'account_id' : '**************',
        'account_name_investor' : '宽辅泓涛专享1号',
        'stock_account_name' : '宽辅泓涛专享1号',
        'account_alias' : '对冲端（国信证券dma）',
        'host_dir' : '数据导出/宽辅泓涛专享1号/Future',
        # 'host_dir' : '交易软件/GTrade1.33版本-生产环境-宽辅泓涛专享1号/export_data/Future',
        'future_dir_files_list' : [
            'Position-{}.csv',
            'Account-{}.csv',
            'Order-{}.csv',
            'Deal-{}.csv',
        ],
    },
    
    '宽辅专享1号_**************' : {
        'product_name' : '宽辅专享1号',
        'account_id' : '**************',
        'account_name_investor' : '宽辅专享1号',
        'stock_account_name' : '宽辅专享1号',
        'account_alias' : '对冲端（国信证券dma）',
        'host_dir' : '数据导出/宽辅专享1号/Future',
        # 'host_dir' : '交易软件/GTrade1.33版本-生产环境-宽辅专享1号/export_data/Future',
        'future_dir_files_list' : [
            'Position-{}.csv',
            'Account-{}.csv',
            'Order-{}.csv',
            'Deal-{}.csv',
        ],
    }
}




# dma_accounts = [
#     '宽辅专享1号',     
#     '宽辅专享1号_浙商',   
#     # '宽辅专享5号',     
#     '宽辅泓涛专享1号',   
#     # '宽辅泓涛专享1号_东方', 
#     '宽辅泓涛专享1号_广发', 
#     '远航安心中性1号',   
#     '远航安心中性1号_银河', 
#     '远航安心中性6号_国君', 
#     # '远航安心中性6号_国联', 
#     '远航安心中性6号_银河', 
#     '宽辅泓涛专享1号_中信多空',
#     '宽辅臻好专享_华泰',
#     '远航安心中性6号_中信多空', 
#     '宽辅思贤专享中性1号_中金多空',
#     '宽辅思贤专享中性1号_华泰多空'
# ]

# credit_accounts = [
#     # '宽辅泓涛专享1号_中信两融',
# ]

# read_email_pnl_accounts = [
#     '宽辅专享1号',     
#     '宽辅专享1号_浙商',   
#     # '宽辅专享5号',     
#     '宽辅泓涛专享1号',   
#     # '宽辅泓涛专享1号_东方', 
#     '宽辅泓涛专享1号_广发', 
#     '远航安心中性1号',   
#     '远航安心中性1号_银河', 
#     '远航安心中性6号_国君', 
#     # '远航安心中性6号_国联', 
#     '远航安心中性6号_银河', 
#     '宽辅泓涛专享1号_中信多空',
#     '宽辅臻好专享_华泰',
#     # '宽辅泓涛专享1号_中信两融',
#     '远航安心中性6号_中信多空', 
# ]

# duotou_accounts = [
#     '宽辅500指增1号_安信', 
#     '宽辅500指增1号_天风', 
#     '宽辅1000指增1号_招商',
#     '宽辅1000指增1号_平安',
#     '宽辅泓涛专享2号',
#     '宽辅泓涛专享2号_中信',
#     '宽辅臻好专享_民生',
#     '宽辅臻好专享_国君',
#     '宽辅臻好专享_华泰',
#     ]






# from tempfile import NamedTemporaryFile
# from misc.ssh_conn import sftp_clent_inner
# import xml.etree.ElementTree as ET
from loguru import logger
import pandas as pd
from misc.Readstockfile import read_remote_file
# from misc.ssh_conn import sftp_clent_inner, sftp_clent_ninner
from accounts_config import general 
# import load_futures_accounts, load_products_accounts, load_stock_accounts

# def load_kf_acconts_dict():
#     config_file = 'others/account_config.xlsx'
#     config = read_remote_file(config_file, src_type='dav', sheet_name='stock')
    
#     # filter inactive account
#     config = config[config['active'] == 1]
#     config.drop(columns=['active'], inplace=True)
#     # check if account_name column duplicated, if yes, warning which account_name is duplicated
#     duplicated_account_name = config[config.duplicated(subset=['account_name'], keep=False)]['account_name'].unique()
#     if len(duplicated_account_name) > 0:
#         logger.error('Warning: stock account_name duplicated: ', duplicated_account_name)
#     # parse every line of account config into dict 
#     config = config[config['owner'] == 'kf']
#     accounts_dict = config.set_index('account_name').to_dict(orient='index')
#     return accounts_dict


# kf_accounts_dict = load_kf_acconts_dict()

# kf_accounts_dict = general.load_stock_accounts(owner='kf')
# kf_futures_accounts = general.load_futures_accounts(owner='kf')
# product_account_dict = general.load_products_accounts(owner='kf')

# futureStra_product_dict = general.load_products_accounts(owner='jeff')
# futureStra_account_dict = general.load_futures_accounts(owner='jeff')

# futureIX_product_dict = general.load_products_accounts(owner='chenyc')
# futureIX_account_dict = general.load_futures_accounts(owner='chenyc')

test_accounts_dict = general.load_stock_accounts(owner='test')

# dma_accounts = [acc for acc in kf_accounts_dict.keys() if kf_accounts_dict[acc].get('dma_account') == True]
# read_email_pnl_accounts = [acc for acc in kf_accounts_dict.keys() if kf_accounts_dict[acc].get('read_email_pnl') == True]
# duotou_accounts = [acc for acc in kf_accounts_dict.keys() if kf_accounts_dict[acc].get('duotou') == True]



zs_accounts_dict = general.load_stock_accounts(owner='zs')
zs_futures_accounts = general.load_futures_accounts(owner='zs')
product_account_dict = general.load_products_accounts(owner='zs')

























def get_account_name_by_id(account_id):
    # check duplicated account_id
    account_id = int(account_id)
    account_ids = [ int(kf_accounts_dict[account_name].get('account_id')) for account_name in kf_accounts_dict.keys()]
    
    if account_id not in account_ids:
        raise ValueError(f'account_id {account_id} not in kf_accounts_dict')
    if len(account_ids) != len(set(account_ids)):
        dp = pd.Series(account_ids).value_counts()
        dp = dp[dp>1]
        raise ValueError(f'duplicated account_id in kf_accounts_dict: {dp.index.tolist()}')
    
    for account_name in kf_accounts_dict.keys():
        if kf_accounts_dict[account_name].get('account_id') == account_id:
            return account_name


def get_account_id_by_name(account_name):
    if account_name not in kf_accounts_dict.keys():
        raise ValueError(f'account_name {account_name} not in kf_accounts_dict')
    account_id = int(kf_accounts_dict[account_name].get('account_id'))
    return account_id




def get_account_sftp(account_name):
    """
    return {
        'sftp' : sftp_ftp,
        'ftp_type' : dest_type,
    }
    """
    if account_name not in kf_accounts_dict.keys():
        sftp_ftp = sftp_clent_ninner
        dest_type = 'ninner'
    else:
        if kf_accounts_dict[account_name].get('system') == 'position':
            sftp_ftp = sftp_clent_inner
            dest_type = 'inner'
        else:
            sftp_ftp = sftp_clent_ninner
            dest_type = 'ninner'
    return {'sftp' : sftp_ftp, 'ftp_type' : dest_type}
        




# new parse =========
def parse_sub_account_config_excel(date=None):
    # 返回带有子账户手数的dict, dict的key是账户名, value是一个dict, key是子账户名, value是一个dict, key是bench, value是index_num
    # 包含了子账户和主账户同名的账户
    if date is None:
        file = 'others/sub_account_config/sub_account_config.xlsx'
    else:
        file = 'others/sub_account_config/sub_account_config_{}.xlsx'.format(date)
    df = read_remote_file(file, src_type='dav')
    df = df.fillna(0)
    df = df[df['active'] == True]
    # print(df)
    df_group = df.groupby('Account')
    
    sub_accounts = {}
    for each_groupkey in df_group.groups.keys():
        # print(each_groupkey)
        account_group = df_group.get_group(each_groupkey)
        account_group = account_group.drop_duplicates(subset=['Sub_Account'], keep='last').copy()
        account_group = account_group.set_index('Sub_Account')
        sub_accounts[each_groupkey] = {}
        for each_index in account_group.index:
            sub_accounts[each_groupkey][each_index] = {}
            sub_accounts[each_groupkey][each_index]['bench'] = account_group.loc[each_index, 'bench']
            sub_accounts[each_groupkey][each_index]['index_num'] = float(account_group.loc[each_index, 'index_num'])
    # print(sub_accounts)
    return sub_accounts

# def get_all_active_sub_accounts(kf_accounts_dict) -> list:
#     all_sub_accounts_list = []
#     for account in kf_accounts_dict.keys():
#         # print(account)
#         if kf_accounts_dict[account].get('sub_accounts') is None:
#             all_sub_accounts_list.append(account)
#         elif len(kf_accounts_dict[account].get('sub_accounts')) == 0:
#             all_sub_accounts_list.append(account)
#         else:
#             if isinstance(kf_accounts_dict[account]['sub_accounts'], list):
#                 all_sub_accounts_list.extend(kf_accounts_dict[account]['sub_accounts'])
#             elif isinstance(kf_accounts_dict[account]['sub_accounts'], dict):
#                 all_sub_accounts_list.extend(list(kf_accounts_dict[account]['sub_accounts'].keys()))
#     return all_sub_accounts_list
            
# def get_all_active_sub_accounts_by_date(date=None, accounts_dict=zs_accounts_dict) -> list:
#     from misc.ssh_conn import sftp_clent_dav
#     from misc.Readstockfile import read_remote_file
#     from datetime import datetime
#     from data_utils.trading_calendar import Calendar
#     # import pandas as pd
#     target_dir = 'others/sub_account_config'
#     # 没有日期, 计算最新交易日
#     if date is None:
#         date = datetime.now().strftime('%Y%m%d')
#         if not Calendar.is_trading_day(date):
#             date = Calendar.last_trading_day(date).strftime('%Y%m%d')
        
#     # 如果最新交易日文件不存在, 合成
#     if date == datetime.now().strftime('%Y%m%d') and f'all_sub_accounts_{date}.xlsx' not in sftp_clent_dav.listdir(target_dir):
#         latest_sub_config_file = 'others/sub_account_config/sub_account_config.xlsx'
#         sub_accounts = read_remote_file(latest_sub_config_file, src_type='dav')
#         accounts_in_excel_config = sub_accounts[sub_accounts['active'] == True]['Account'].unique()
#         accounts_not_in_excel_config = [account for account in accounts_dict.keys() if account not in accounts_in_excel_config]
#         tmp_df = pd.DataFrame({'active':[1]*len(accounts_not_in_excel_config), 'Account':accounts_not_in_excel_config, 'Sub_Account':accounts_not_in_excel_config})
#         sub_accounts = pd.concat([sub_accounts, tmp_df])
#         sub_accounts = sub_accounts.fillna(0)[['active', 'Account', 'Sub_Account']]
#         all_sub_accounts = sub_accounts[sub_accounts['active'] == True]['Sub_Account'].tolist()
#     else:
#         target_file = f'others/sub_account_config/all_sub_accounts_{date}.xlsx'
#         df = read_remote_file(target_file, src_type='dav')
#         all_sub_accounts = df[df['active']==True]['Sub_Account'].tolist()
    
#     return all_sub_accounts

def update_sub_config_in_main_config(main_config, sub_config_key, sub_config):
    # update trade_config into main config set kf_accounts_dict
    """
    main_config = {
        account_name : {
            ...
        }
    }
    sub_config = {
        account_name : {
            ...
        }
    }
    after merge:
    main_config = {
        account_name : {
            ...
            sub_config_key : sub_config
        }
    }
    """
    for account_name in sub_config.keys():
        if account_name not in main_config.keys():
            main_config[account_name] = {}
        # kf_accounts_dict[account_name].update({'trade_config' : trade_config[account_name]})
        # update trade_config into main config set kf_accounts_dict
        # if sub_config_key not in main_config[account_name].keys():
        main_config[account_name][sub_config_key] = {}
        for key in sub_config[account_name].keys():
            main_config[account_name][sub_config_key][key] = sub_config[account_name][key]
    return main_config

#########################
# old
# kf_accounts_dict = accounts_config_updated_sub()
# new


# def bak_latest_sub_config(date):
#     import os
#     # import pandas as pd
#     from misc.ssh_conn import sftp_clent_dav
#     from datetime import datetime
#     from tempfile import NamedTemporaryFile
#     latest_sub_config_file = 'others/sub_account_config/sub_account_config.xlsx'
#     target_dir = 'others/sub_account_config'
#     target_file = 'sub_account_config_{}.xlsx'.format(date)
#     # 要确保当日运行备份最新数据
#     if date == datetime.now().strftime('%Y%m%d') and target_file not in sftp_clent_dav.listdir(target_dir):
#         sub_accounts = read_remote_file(latest_sub_config_file, src_type='dav')
#         sub_accounts = sub_accounts.fillna(0)[['active', 'Account', 'Sub_Account', 'bench', 'index_num']]
#         # accounts_in_excel_config = sub_accounts[sub_accounts['active'] == True]['Account'].unique()
#         # accounts_not_in_excel_config = [account for account in kf_accounts_dict.keys() if account not in accounts_in_excel_config]
#         # tmp_df = pd.DataFrame({'active':[1]*len(accounts_not_in_excel_config), 'Account':accounts_not_in_excel_config, 'Sub_Account':accounts_not_in_excel_config})
#         # sub_accounts = pd.concat([sub_accounts, tmp_df])
#         with NamedTemporaryFile(delete=True, suffix='.xlsx') as tmp:
#             sub_accounts.to_excel(tmp.name, index=False)
#             sftp_clent_dav.put(tmp.name, os.path.join(target_dir, target_file))
#             print(f'backup latest_sub_config done {date}')

# def bak_all_sub_accounts(date, kf_accounts_dict=kf_accounts_dict):
#     import os
#     # import pandas as pd
#     from misc.ssh_conn import sftp_clent_dav
#     from datetime import datetime
#     from tempfile import NamedTemporaryFile
#     latest_sub_config_file = 'others/sub_account_config/sub_account_config.xlsx'
#     target_dir = 'others/sub_account_config'
#     target_file = 'all_sub_accounts_{}.xlsx'.format(date)
#     # 要确保当日运行备份最新数据
#     if date == datetime.now().strftime('%Y%m%d') and target_file not in sftp_clent_dav.listdir(target_dir):
#         sub_accounts = read_remote_file(latest_sub_config_file, src_type='dav')
#         accounts_in_excel_config = sub_accounts[sub_accounts['active'] == True]['Account'].unique()
#         accounts_not_in_excel_config = [account for account in kf_accounts_dict.keys() if account not in accounts_in_excel_config]
#         tmp_df = pd.DataFrame({'active':[1]*len(accounts_not_in_excel_config), 'Account':accounts_not_in_excel_config, 'Sub_Account':accounts_not_in_excel_config})
#         sub_accounts = pd.concat([sub_accounts, tmp_df])
#         sub_accounts = sub_accounts.fillna(0)[['active', 'Account', 'Sub_Account']]
#         with NamedTemporaryFile(delete=True, suffix='.xlsx') as tmp:
#             sub_accounts.to_excel(tmp.name, index=False)
#             sftp_clent_dav.put(tmp.name, os.path.join(target_dir, target_file))
#             print(f'backup all_sub_accounts done {date}')
    


# kf_accounts_dict = update_sub_config_in_main_config(kf_accounts_dict, 'sub_accounts', parse_sub_account_config_excel())









# 可以不更新了
# future_account_dict = {
#     '中性2号_兴证_********': {
#         'product_name' : '远航安心中性2号',
#         'account_id' : '********',
#         # 'account_name_investor' : '宽辅远航安心市场中性2号',
#         'account_name_investor' : '中性2号_兴证',
#         'account_alias' : '对冲端（兴证期货）',
#     },
#     '中性2号_国投_**********': {
#         'product_name' : '远航安心中性2号',
#         'account_id' : '**********',
#         # 'account_name_investor' : '宽辅远航安心2号',
#         'account_name_investor' : '中性2号_国投',
#         'account_alias' : '对冲端（国投安信期货）',
#     },
#     '中性2号_中信_*********': {
#         'product_name' : '远航安心中性2号',
#         'account_id' : '*********',
#         # 'account_name_investor' : '宽辅远航安心2号',
#         'account_name_investor' : '中性2号_中信',
#         'account_alias' : '对冲端（中信期货）',
#     },

#     '中性5号_东证_********': {
#         'product_name' : '远航安心中性5号',
#         'account_id' : '********',
#         # 'account_name_investor' : '宽辅远航安心5号',
#         'account_name_investor' : '中性5号_东证',
#         'account_alias' : '对冲端（东证期货）',
#     },
    
#     # '宽辅泓涛专享1号私募证券投资基金_*********': {
#     #     'product_name' : '宽辅泓涛专享1号',
#     #     'account_id' : '*********',
#     #     'account_name_investor' : '宽辅泓涛专享1号私募证券投资基金',
#     #     'account_alias' : '对冲端（中信期货）',
#     # },
    
#     '联丰专享_平安_*********': {
#         'product_name' : '宽辅联丰专享',
#         'account_id' : '*********',
#         # 'account_name_investor' : '联丰专享',
#         'account_name_investor' : '联丰专享_平安',
#         'account_alias' : '对冲端（平安期货）',
#     },
    
#     '思贤专享_中财_091125': {
#         'product_name' : '宽辅思贤专享中性1号',
#         'account_id' : '091125',
#         # 'account_name_investor' : '宽辅思贤专享中性1号',
#         'account_name_investor' : '思贤专享_中财',
#         'account_alias' : '对冲端（中财期货）',
#     },
    
#     '思贤专享_中信_*********': {
#         'product_name' : '宽辅思贤专享中性1号',
#         'account_id' : '*********',
#         # 'account_name_investor' : '宽辅思贤专享中性1号',
#         'account_name_investor' : '思贤专享_中信',
#         'account_alias' : '对冲端（中信期货）',
#     },
    
#     '专享2号_国贸_********': {
#         'product_name' : '宽辅专享2号',
#         'account_id' : '********',
#         # 'account_name_investor' : '宽辅专享2号私募证券投资基金',
#         'account_name_investor' : '专享2号_国贸',
#         'account_alias' : '对冲端（国贸期货）',
#     },

#     '金品中国_中信_*********': {
#         'product_name' : '宽辅金品中国专享',
#         'account_id' : '*********',
#         # 'account_name_investor' : '宽辅专享2号私募证券投资基金',
#         'account_name_investor' : '宽辅金品中国专享',
#         'account_alias' : '对冲端（中信期货）',
#     },

#     '500指增_中信_*********': {
#         'product_name' : '宽辅500指增1号',
#         'account_id' : '*********',
#         # 'account_name_investor' : '宽辅专享2号私募证券投资基金',
#         'account_name_investor' : '500指增_中信',
#         'account_alias' : '期货端（中信期货）',
#     },

#     '1000指增_华泰_********': {
#         'product_name' : '宽辅1000指增1号',
#         'account_id' : '********',
#         # 'account_name_investor' : '宽辅专享2号私募证券投资基金',
#         'account_name_investor' : '1000指增_华泰',
#         'account_alias' : '期货端（华泰期货）',
#     },
# }
