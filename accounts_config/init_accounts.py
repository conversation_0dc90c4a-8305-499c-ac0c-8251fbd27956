from misc import Account
from data_utils.trading_calendar import Calendar
from misc import tools 
from accounts_config import general

def init_accounts(trade_date, owner='all', load_close=False, load_pre_close=False):
    """
    初始化账户实例

    :param trade_date: 交易日
    :param owner: 账户所属类型，kf, fj, all
    :param close: 是否加载当日收盘价
    :param pre_close: 是否加载前一交易日收盘价
    
    return product_instances, stock_instances, futures_instances
    """
    pre_date = Calendar.last_trading_day(trade_date).strftime('%Y%m%d')
    
    # results_dict = {}
    product_instances = {}
    stock_instances = {}
    futures_instances = {}
    
    close = None
    pre_close = None
    if load_close:
        close = tools.get_stock_adj_close(date=trade_date)
        if close.empty:
            close = None
    if load_pre_close:
        pre_close = tools.get_stock_adj_close(date=pre_date)
        if pre_close.empty:
            pre_close = None
        
    pre_futures_price = tools.get_futures_price_include_index(date=pre_date)
    futures_price = tools.get_futures_price_include_index(date=trade_date)

    stock_accounts_dict = general.load_stock_accounts()
    products_dict = general.load_products_accounts()
    futures_accounts_dict = general.load_futures_accounts()

    # print(f'trade_date: {trade_date}')
    for product_name in products_dict.keys():
        conf_owner = products_dict[product_name]['owner']
        
        if owner != 'all' and conf_owner != owner:
            continue            
        product_instances[product_name] = Account.ProductAccount(product_name=product_name, trade_date=trade_date, owner=owner)
        
        
    for account_name in stock_accounts_dict.keys():
        product_name = stock_accounts_dict[account_name]['product_name']
        if product_name not in product_instances.keys():
            continue
        # owner = stock_accounts_dict[account_name]['owner']
        # bv_type = stock_accounts_dict[account_name]['bv_type']
        
        account_type = stock_accounts_dict[account_name].get('account_type')
        Stock = Account.StockAccount(product_name=product_name, account_name=account_name, trade_date=trade_date, 
                            pre_close=pre_close, 
                            close=close,
                            account_type=account_type,
                            stock_commission_rate=stock_accounts_dict[account_name].get('stock_commission_rate', 0.00012),
                            stock_tax_rate=stock_accounts_dict[account_name].get('stock_tax_rate', 0.0005),
                            # bv_type=bv_type
                            )
        stock_instances[account_name] = Stock
        Product = product_instances[product_name]
        Product.add_stock_account(Stock)
        Stock.Product = Product

    for account_name in futures_accounts_dict.keys():
        product_name = futures_accounts_dict[account_name]['product_name']
        if product_name not in product_instances.keys():
            continue
        # owner = futures_accounts_dict[account_name]['owner']
        
        Futures = Account.FuturesAccount(product_name=product_name, account_name=account_name, trade_date=trade_date,
                                pre_price=pre_futures_price,
                                price=futures_price
                                )
        futures_instances[account_name] = Futures
        Product = product_instances[product_name]
        Product.add_futures_account(Futures)
        Futures.Product = Product

    return product_instances, stock_instances, futures_instances