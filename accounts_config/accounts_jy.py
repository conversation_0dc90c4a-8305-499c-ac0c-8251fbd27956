
# note
# 如果同步计算orders 时，账户没有hold，可以设置 first_day_no_hold=True

accounts_jy = {
    'outer_ftp_host' : 'root@**************',
    'outer_ftp_root' : '/data/ftp/jy',
    'accounts' : {
        '久盈成长1号': {
            'cols': {
                '代码': 'ticker', 
                '持仓量': 'shares',  
                '最新价': 'last_price', 
                '市值': 'hold_value'
            },
            'format': 'excel',
            'args': {
                'dtype': {
                    '证券代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
            'pnl_file' : '久盈系列.xlsx',
        },
        
        '久盈成长2号': {
            'cols': {
                '代码': 'ticker', 
                '持仓量': 'shares',  
                '最新价': 'last_price', 
                '市值': 'hold_value'
            },
            'format': 'excel',
            'args': {
                'dtype': {
                    '证券代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
        },
        
        '久盈成长5号': {
            'cols': {
                '代码': 'ticker', 
                '持仓量': 'shares',  
                '最新价': 'last_price', 
                '市值': 'hold_value'
            },
            'format': 'excel',
            'args': {
                'dtype': {
                    '证券代码': int, 
                    '持仓量': int, 
                    '最新价': float,
                    '市值': float
                }
            },
            'order_type' : 'xt1',
        },
    }
}
