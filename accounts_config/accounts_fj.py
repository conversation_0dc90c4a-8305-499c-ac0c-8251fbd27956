# note
# 如果同步计算orders 时，账户没有hold，可以设置 first_day_no_hold=True

from accounts_config import general 

fj_pnl_account = '瑞远中性1号_东吴'

fj_pnl_file = '2.FJ瑞远系列.xlsx'

fj_dir_prefix = 'fj'

accounts_fj = {
    'outer_ftp_host' : 'root@**************',
    'outer_ftp_root' : '/data/ftp/fj',
    'accounts': {
        '瑞远中性1号_东吴': {
            'product_name' : '瑞远中性1号',
            'cols': {
                '代码': 'ticker', 
                '持仓量': 'shares', 
                '冻结数量': 'frozen_shares', 
                '最新价': 'last_price', 
                '市值': 'hold_value', 
                '市值(约)': 'hold_value'
            },
            'format': 'excel',
            'args': {},
            'order_type' : 'xt1',
            # 'sub_accounts' : [
            #     '瑞远中性1号_东吴_子1',
            #     '瑞远中性1号_东吴_子2',
            # ],
            # 'resid_account' : ['瑞远中性1号_东吴_差值'],
            'pnl_file': '2.FJ瑞远系列.xlsx',
        },
        
        '瑞远中性2号_恒泰': {
            'product_name' : '瑞远中性2号',
            'cols': {
                '代码': 'ticker', 
                '持仓量': 'shares', 
                '冻结数量': 'frozen_shares', 
                '最新价': 'last_price', 
                '市值': 'hold_value', 
                '市值(约)': 'hold_value',
            },
            'format': 'excel',
            'args': {},
            'order_type' : 'xt1',            
        },

        '瑞远中性3号_中金': {
            'product_name' : '瑞远中性3号',
            'cols': {
                '代码': 'ticker', 
                '持仓量': 'shares', 
                '冻结数量': 'frozen_shares', 
                '最新价': 'last_price', 
                '市值': 'hold_value', 
                '市值(约)': 'hold_value',
            },
            'format': 'excel',
            'args': {},
            'order_type' : 'xt1',            
        },

        '瑞远中性5号_华福': {
            'product_name' : '瑞远中性5号',
            'cols': {
                '代码': 'ticker', 
                '持仓量': 'shares', 
                '冻结数量': 'frozen_shares', 
                '最新价': 'last_price', 
                '市值': 'hold_value', 
                '市值(约)': 'hold_value',
            },
            'format': 'excel',
            'args': {},
            'order_type' : 'xt1',
        },
         
        '瑞远中性7号_银河': {
            'product_name' : '瑞远中性7号',
            'cols': {
                '代码': 'ticker', 
                '持仓量': 'shares', 
                '冻结数量': 'frozen_shares', 
                '最新价': 'last_price', 
                '市值': 'hold_value', 
                '市值(约)': 'hold_value'
            },
            'format': 'excel',
            'args': {},
            'order_type' : 'xt1',
        },

    }
}

fj_accounts_dict = general.load_stock_accounts(owner='fj')

products_fj = [
    '瑞远中性1号',
    '瑞远中性2号',
    '瑞远中性3号',
    '瑞远中性5号',
    '瑞远中性7号',
]

fj_products_dict = general.load_products_accounts(owner='fj')