# ax_t0_start_time = str(input("输入安信T0开始时间 HHMMSS : "))
# ax_t0_start_time = '102800'

order_algorithm_dict = {
    # '宽辅泓涛专享1号_海通': 'HX_SMART_VWAP',
    # '远航安心中性2号_民生'    :     '主动VWAP',
    
    # '宽辅500指增1号_安信'   :  '自诚主动VWAP',
    '宽辅500指增1号_安信'   :  'VWAP',
    # '宽辅500指增1号_安信'   :  'kuanfu',
        # {
        # ax_t0_start_time : 'T0',
        # 'fallback' : '自诚主动VWAP'
        # },
    
    # '远航安心中性2号_安信'   :    'kuanfu',
    # '远航安心中性2号_东方'   :    'kuanfu',
    # '远航安心中性2号_浙商'   :    'kuanfu',
    # '远航安心中性3号'    :        'kuanfu',
    # '远航安心中性5号'    :        'kuanfu',
    # '远航安心中性5号_国君'    :    'kuanfu',
    '远航安心中性5号_华金'    :    'kf_vwap_plus',
    # '宽辅1000指增1号_招商'  :     'kuanfu',
    # '宽辅1000指增1号_平安'  :     'kuanfu',
    '宽辅1000指增1号_平安'  :     'VWAP',
    '宽辅1000指增1号_国君'  :     'ld-vwap',
    # '宽辅思贤专享中性1号'    :     'kuanfu',
    # '宽辅思贤专享中性1号_广发' :   'kuanfu',
    '宽辅思贤专享中性2号_广发' :   'FT-WAP-AI-PLUS',
    # '宽辅联丰专享'          :     'kuanfu',
    # '宽辅联丰专享_平安'      :     'kuanfu',
    # '宽辅量化选股1号_民生'   :     'kuanfu',
    # '宽辅量化选股1号_东北'   :     'kuanfu',
    '宽辅专享2号'        :        'kuanfu',
    '宽辅专享2号_华金'        :        'kuanfu',
    '宽辅专享7号_华金'        :        'kuanfu',
    '宽辅专享7号_江海'        :        'JINGLE_VWAP',
    # '宽辅泓涛专享2号'        :     'kuanfu',
    '宽辅泓涛专享2号_中信'    :     'FT_AIWAP',
    '宽辅泓涛专享1号_中信'    :     'FT_AIWAP',
    '宽辅金品中国专享'       :     'ft_vwap_ai_plus',
    # '宽辅专享6号'        :        'kuanfu',
    # '宽辅专享6号_东北'    :        'kuanfu',
    '宽辅专享6号_光大'    :        'kuanfu',
    # '宽辅瑞年专享_中泰'     :        'kuanfu',
    '宽辅臻好专享_民生'     :        'kuanfu',
    '宽辅臻好专享_国君'     :        'ld-vwap',
    '宽辅臻好专享_华泰'     :        'VWAP',
    '宽辅泓涛专享2号_华泰场外'  :     'VWAP',
    '宽辅思贤专享中性1号_华泰多空'  :     'VWAP',
    
    # '远航安心中性1号_银河'    :     'kuanfu',
    # '远航安心中性6号_银河'    :     'kuanfu',
    # '宽辅专享5号'           :     'kuanfu',

    '宽辅泓涛专享1号_东方'    :     'kf_vwap_plus',
    # '远航安心中性6号_国联'    :     'kf_vwap_plus',
    '远航安心中性2号_方正'    :     'kf_vwap_plus',
    '宽辅泓涛专享1号_广发'    :     'kf_vwap_plus',
    '宽辅思贤专享中性2号_东财' :     'kf_vwap_plus',
    '宽辅泓涛专享2号_山西'    :     'kf_vwap_plus',
    # '宽辅思贤专享中性1号_中金多空'    :  'kf_pov_core',
    # '宽辅专享6号_中金场外'          :  'kf_pov_core',
    # '宽辅思贤专享中性2号_光大场外'    :  'kf_pov_core',
    # '宽辅专享6号_光大多空'          :  'kf_pov_core',
    # '宽辅专享1号_浙商'       :     'kf_vwap_plus',
    
    # '宽辅泓涛专享1号_中信多空'  :     'SmartVolumeInline3',
    # '远航安心中性6号_中信多空'  :     'SmartVolumeInline3',
    # '宽辅专享6号_中信多空'     :     'SmartVolumeInline3',
    # '宽辅泓涛专享1号_中信两融'  :     'ZJDynamic6',
    
    # '远航安心中性6号_国君'    :     'HX_SMART_VWAP',
    # '远航安心中性6号_国君'    :     'JINGLE_VWAP',
    # '远航安心中性1号'        :     'HX_SMART_VWAP',
    '宽辅泓涛专享1号'        :     'HX_SMART_VWAP',
    # '宽辅专享1号'           :     'HX_SMART_VWAP',
    '远航安心中性1号_广发'    :     'HX_SMART_VWAP',
    # '宽辅专享1号_浙商'       :     'HX_SMART_VWAP',
    # '宽辅思贤专享中性1号_华鑫'    :   'HX_SMART_VWAP',
    # '宽辅思贤专享中性1号_华鑫'    :   'kf_vwap_plus',

    # '宽辅500指增1号_天风'   :     'YLKF-smartvwap',
    '宽辅500指增1号_天风'   :     'HX_SMART_VWAP',
}
