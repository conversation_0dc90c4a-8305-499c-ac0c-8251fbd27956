from loguru import logger

from misc.Readstockfile import read_remote_file
# StockAccount, ProductAccount, FuturesAccount
# import get_stock_close, get_futures_price_include_index

inner_ftp_root = '/home/<USER>/signal/product'
# inner_ftp_root = 'root@123.60.32.97:/home/<USER>/signal/product'


def load_stock_accounts(owner=None):
    account_config_file = 'others/account_config.xlsx'
    account_config_src_type = 'zsdav'
    
    config = read_remote_file(path=account_config_file, src_type=account_config_src_type,
                              sheet_name='stock')
    # filter inactive account
    config = config[config['active'] == 1]
    config['account_id'] = config['account_id'].astype(int)
    config.drop(columns=['active'], inplace=True)
    # check if account_name column duplicated, if yes, warning which account_name is duplicated
    duplicated_account_name = config[config.duplicated(subset=['account_name'], keep=False)]['account_name'].unique()    
    if len(duplicated_account_name) > 0:
        logger.error('Warning: stock account_name duplicated: ', duplicated_account_name)
    if owner is not None:
        config = config[config['owner'] == owner]
    # parse every line of account config into dict 
    accounts_dict = config.set_index('account_name').to_dict(orient='index')
    return accounts_dict

def load_products_accounts(owner=None):
    account_config_file = 'others/account_config.xlsx'
    account_config_src_type = 'zsdav'
    
    config = read_remote_file(path=account_config_file, src_type=account_config_src_type,
                              sheet_name='product')
    # filter inactive account
    config = config[config['active'] == 1]
    config.drop(columns=['active'], inplace=True)
    # check if account_name column duplicated, if yes, warning which account_name is duplicated
    duplicated_product_name = config[config.duplicated(subset=['product_name'], keep=False)]['product_name'].unique()    
    if len(duplicated_product_name) > 0:
        logger.error('Warning: product_name duplicated: ', duplicated_product_name)
    if owner is not None:
        config = config[config['owner'] == owner]
    # parse every line of account config into dict 
    products_dict = config.set_index('product_name').to_dict(orient='index')
    return products_dict

def load_futures_accounts(owner=None):
    account_config_file = 'others/account_config.xlsx'
    account_config_src_type = 'zsdav'
    
    config = read_remote_file(path=account_config_file, src_type=account_config_src_type,
                              sheet_name='futures')
    # filter inactive account
    config = config[config['active'] == 1]
    config.drop(columns=['active'], inplace=True)
    # check if account_name column duplicated, if yes, warning which account_name is duplicated
    duplicated_account_name = config[config.duplicated(subset=['account_name'], keep=False)]['account_name'].unique()    
    if len(duplicated_account_name) > 0:
        logger.error('Warning: futures account_name duplicated: ', duplicated_account_name)
    if owner is not None:
        config = config[config['owner'] == owner]
    # parse every line of account config into dict 
    accounts_dict = config.set_index('account_name').to_dict(orient='index')
    return accounts_dict

    


    # for product_name in products_dict.keys():
    #     if products_dict[product_name]['product_attribute'] == 'dma':
    #         continue
    #     if products_dict[product_name]['owner'] == 'fj':
    #         continue
    #     if product_name == '宽辅臻好专享':
    #         continue
        
    #     print(product_name)

        # product_name = stock_accounts_dict[account_name]['product_name']
        
        
        # account_name = stock_account
        # results_dict[product_name] = {}
        # Product = product_instances[product_name]
        # Stock = stock_instances[account_name]
        
        # print(Product.stock_accounts_pnl)
        # print(Product.pre_stock_value)
    #     today_return = Product.stock_accounts_pnl / Product.pre_stock_value
    #     results_dict[product_name] = today_return 

    # results = pd.DataFrame.from_dict(results_dict, orient='index')