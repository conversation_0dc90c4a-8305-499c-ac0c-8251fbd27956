#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货价格数据处理脚本
处理MarketData.csv文件中的不规整数据，筛选CFFEX交易所数据并提取指定列
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def clean_market_data(input_file, output_file=None):
    """
    清洗期货市场数据
    
    Args:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径，如果为None则不保存文件
    
    Returns:
        pd.DataFrame: 清洗后的数据框
    """
    print("开始处理期货价格数据...")
    
    # 定义期望的列名（根据第一行标题）
    expected_columns = [
        'TradingDay', 'PreSettlementPrice', 'PreClosePrice', 'PreOpenInterest', 'PreDelta',
        'OpenPrice', 'HighestPrice', 'LowestPrice', 'ClosePrice', 'UpperLimitPrice',
        'LowerLimitPrice', 'SettlementPrice', 'CurrDelta', 'LastPrice', 'Volume',
        'Turnover', 'OpenInterest', 'BidPrice1', 'BidVolume1', 'AskPrice1',
        'AskVolume1', 'InstrumentID', 'UpdateTime', 'UpdateMillisec', 'ExchangeID',
        'VolumeAskLot', 'VolumeBidLot', 'BidPrice2', 'BidVolume2', 'BidPrice3',
        'BidVolume3', 'AskPrice2', 'AskVolume2', 'AskPrice3', 'AskVolume3',
        'BidPrice4', 'BidVolume4', 'BidPrice5', 'BidVolume5', 'AskPrice4',
        'AskVolume4', 'AskPrice5', 'AskVolume5', 'AskPrice6', 'AskVolume6',
        'AskPrice7', 'AskVolume7', 'AskPrice8', 'AskVolume8', 'AskPrice9',
        'AskVolume9', 'BidPrice6', 'BidVolume6', 'BidPrice7', 'BidVolume7',
        'BidPrice8', 'BidVolume8', 'BidPrice9', 'BidVolume9', 'AskPrice10',
        'AskVolume10', 'BidPrice10', 'BidVolume10', 'Margin', 'Fee',
        'InstrumentStatus', 'ImplyBidPrice1', 'ImplyBidVolume1', 'ImplyAskPrice1',
        'ImplyAskVolume1'
    ]
    
    try:
        # 读取CSV文件，处理不规整数据
        print("正在读取CSV文件...")

        # 尝试不同的编码方式读取CSV
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']
        df = None

        for encoding in encodings:
            try:
                print(f"尝试使用 {encoding} 编码读取文件...")
                df = pd.read_csv(
                    input_file,
                    encoding=encoding,
                    on_bad_lines='skip',  # 跳过有问题的行
                    engine='python'  # 使用python引擎处理复杂情况
                )
                print(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                print(f"{encoding} 编码失败，尝试下一个...")
                continue

        if df is None:
            raise ValueError("无法使用任何编码读取文件")
        
        print(f"原始数据行数: {len(df)}")
        print(f"原始数据列数: {len(df.columns)}")
        
        # 如果列数不匹配，尝试修复
        if len(df.columns) != len(expected_columns):
            print(f"警告: 列数不匹配。期望 {len(expected_columns)} 列，实际 {len(df.columns)} 列")
            
            # 如果实际列数更多，截断多余的列
            if len(df.columns) > len(expected_columns):
                df = df.iloc[:, :len(expected_columns)]
                print("已截断多余的列")
            
            # 如果实际列数更少，添加缺失的列
            elif len(df.columns) < len(expected_columns):
                missing_cols = len(expected_columns) - len(df.columns)
                for i in range(missing_cols):
                    df[f'missing_col_{i}'] = np.nan
                print(f"已添加 {missing_cols} 个缺失列")
        
        # 设置正确的列名
        df.columns = expected_columns
        
        print("数据列名设置完成")
        
        # 筛选CFFEX交易所数据
        print("正在筛选CFFEX交易所数据...")
        cffex_data = df[df['ExchangeID'] == 'CFFEX'].copy()
        print(f"CFFEX数据行数: {len(cffex_data)}")
        
        if len(cffex_data) == 0:
            print("警告: 没有找到CFFEX交易所的数据")
            return pd.DataFrame()
        
        # 提取指定的14个列
        target_columns = [
            'TradingDay',
            'InstrumentID', 
            'PreSettlementPrice',
            'PreClosePrice',
            'SettlementPrice',
            'ClosePrice',
            'PreOpenInterest',
            'OpenInterest',
            'OpenPrice',
            'HighestPrice',
            'LowestPrice',
            'LastPrice',
            'Volume',
            'Turnover'
        ]
        
        print("正在提取指定列...")
        result_df = cffex_data[target_columns].copy()
        
        # 数据类型转换和清洗
        print("正在进行数据类型转换...")
        
        # 数值列转换
        numeric_columns = [
            'PreSettlementPrice', 'PreClosePrice', 'SettlementPrice', 'ClosePrice',
            'PreOpenInterest', 'OpenInterest', 'OpenPrice', 'HighestPrice',
            'LowestPrice', 'LastPrice', 'Volume', 'Turnover'
        ]
        
        for col in numeric_columns:
            if col in result_df.columns:
                result_df[col] = pd.to_numeric(result_df[col], errors='coerce')
        
        # 处理TradingDay列
        result_df['TradingDay'] = result_df['TradingDay'].astype(str)
        
        # 处理InstrumentID列
        result_df['InstrumentID'] = result_df['InstrumentID'].astype(str)
        
        print("数据清洗完成")
        print(f"最终数据行数: {len(result_df)}")
        print(f"最终数据列数: {len(result_df.columns)}")
        
        # 显示数据概览
        print("\n数据概览:")
        print(result_df.head())
        
        print("\n数据信息:")
        print(result_df.info())
        
        # 保存处理后的数据
        if output_file:
            print(f"\n正在保存数据到: {output_file}")
            result_df.to_csv(output_file, index=False, encoding='utf-8')
            print("数据保存完成")
        
        return result_df
        
    except Exception as e:
        print(f"处理数据时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

def main():
    """主函数"""
    input_file = "/home/<USER>/trade/account_stat/MarketData.csv"
    output_file = "/home/<USER>/trade/account_stat/MarketData_CFFEX_cleaned.csv"
    
    # 处理数据
    cleaned_data = clean_market_data(input_file, output_file)
    
    if not cleaned_data.empty:
        print("\n" + "="*50)
        print("数据处理成功完成!")
        print(f"处理后的数据包含 {len(cleaned_data)} 行，{len(cleaned_data.columns)} 列")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("数据处理失败!")
        print("="*50)

if __name__ == "__main__":
    main()
