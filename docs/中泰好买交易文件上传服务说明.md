中泰好买交易文件上传服务说明
一、用户
1.	使用以下用户登录
用户名：xtp_haomai，密码：haomai_xtp_1。

二、接口调用
1.	上传文件前需先登录获取 token
a.	接口地址：https://horizon.superquant.fund/api/cas/user/login
b.	接口方法：POST
c.	接口参数：json， 字段为：username、password
使用上面提供的用户密码调用此接口，获取返回体中的 access_token，token 的有效期为24小时，若返回401错误则说明 token 已过期，需要重新调用登录接口获取。
2.	上传策略文件
a.	接口地址：https://horizon.superquant.fund/apps/trade-helper/api/trade/file/upload/strategy
b.	接口方法：POST
c.	接口参数：FormData，字段为： files、broker。其中 files 为上传的文件，可同时上传多个；broker 值固定为 xtp_haomai 。
d.	身份校验：将使用登录接口获取的 access_token 填入 Header 的 authorization字段中，格式为 Bearer {access_token}
3.	下载成交文件
a.	接口地址：https://horizon.superquant.fund/apps/trade-helper/api/trade/file/trade-files
b.	接口方法： GET
c.	接口参数：broker。broker 值固定为 xtp_haomai 。
d.	身份校验：将使用登录接口获取的 access_token 填入 Header 的 authorization字段中，格式为 Bearer {access_token}
