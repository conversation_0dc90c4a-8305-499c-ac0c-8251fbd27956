import datetime
import os, sys

from misc.tools import get_cons_tag_series, calc_cons_ratio, get_stock_adj_close, get_target_by_date
from misc.Readstockfile import read_remote_file
from data_utils.trading_calendar import Calendar

from run_order_zhongtai import get_hold_from_targetorder

if len(sys.argv) > 1:
    date = str(sys.argv[1])
    zhiding = True
else:

    date = datetime.datetime.now().strftime('%Y%m%d')
    zhiding = False
    
pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
cons = get_cons_tag_series(date)
preclose = get_stock_adj_close(pre_date)


# account_name = '宽辅专享6号_光大'

query_date = pre_date if not zhiding else date

# stocks = get_target_by_date(account_name, date=query_date)

hold = get_hold_from_targetorder(work_date=date)


cons_ratio = calc_cons_ratio(stocks=hold, cons=cons, price=preclose)
print(f'成分比例: \n{cons_ratio}')