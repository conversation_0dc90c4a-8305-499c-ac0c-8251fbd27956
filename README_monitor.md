# 交易监控系统

基于 Streamlit 的实时交易监控系统，提供交易订单的可视化监控、数据分析和预警功能。

## 功能特性

### 🔍 数据监控
- 实时获取交易订单数据
- 支持多种时间范围筛选（今天、最近1小时、最近6小时、自定义时间）
- 自动刷新数据（可配置刷新间隔）

### 📊 数据可视化
- **关键指标面板**: 总订单数、运行中订单、错误订单、成交率、交易品种数
- **订单状态分布饼图**: 直观显示各状态订单占比
- **账户订单数量柱状图**: Top 10 账户的订单分布
- **订单时间分布图**: 按小时统计的订单时间分布

### 🔍 数据分析
- **分组统计**: 支持按账户、品种、操作类型、状态、算法名称、用户分组
- **聚合分析**: 对数量、成交数量、成交价格进行计数、求和、平均值计算
- **数据筛选**: 支持按状态、账户等字段筛选数据

### 🚨 预警系统
- **错误订单预警**: 当错误/取消/过期订单数量超过阈值时触发
- **成交率预警**: 当整体成交率低于设定阈值时触发
- **账户订单数预警**: 当单个账户订单数量过多时触发

### 📁 数据导出
- 支持将筛选后的数据导出为 CSV 文件
- 文件名包含时间戳，便于管理

## 文件说明

### 主要文件
- `my_monitor.py`: 主监控程序，连接真实 API 获取数据
- `test_monitor.py`: 测试版本，使用模拟数据进行功能演示
- `run_monitor.sh`: 启动脚本
- `xuntou/monitor.py`: 原始监控程序（参考）

### 依赖关系
- `streamlit`: Web 应用框架
- `pandas`: 数据处理
- `plotly`: 交互式图表
- `requests`: HTTP 请求
- `numpy`: 数值计算

## 使用方法

### 方法1: 使用启动脚本
```bash
./run_monitor.sh
```

### 方法2: 直接运行
```bash
# 运行主程序（连接真实API）
streamlit run my_monitor.py --server.port 8501

# 运行测试版本（使用模拟数据）
streamlit run test_monitor.py --server.port 8502
```

### 方法3: 后台运行
```bash
nohup streamlit run my_monitor.py --server.port 8501 > monitor.log 2>&1 &
```

## 访问地址
- 主程序: http://localhost:8501
- 测试版本: http://localhost:8502

## 配置选项

### 侧边栏配置
1. **自动刷新设置**
   - 开启/关闭自动刷新
   - 设置刷新间隔（30秒、1分钟、2分钟、5分钟）

2. **时间过滤**
   - 全部数据
   - 今天
   - 最近1小时
   - 最近6小时
   - 自定义时间范围

3. **预警设置**
   - 错误订单预警及阈值
   - 成交率预警及阈值
   - 账户订单数预警及阈值

### 数据分析配置
- 选择分组字段：accountId, symbol, operation, status, algoName, user
- 选择聚合字段：quantity, filledQuantity, filledPrice
- 筛选条件：状态、账户等

## 数据字段说明

系统处理的交易订单数据包含以下字段：
- `id`: 订单ID
- `symbol`: 交易品种
- `price`: 订单价格
- `quantity`: 订单数量
- `orderType`: 订单类型
- `operation`: 操作类型（BUY/SELL）
- `filledQuantity`: 成交数量
- `filledPrice`: 成交价格
- `status`: 订单状态
- `errorId`: 错误ID
- `errorMsg`: 错误信息
- `createTime`: 创建时间
- `lastUpdTime`: 最后更新时间
- `startTime`: 开始时间
- `endTime`: 结束时间
- `algoName`: 算法名称
- `params`: 参数
- `user`: 用户
- `batchName`: 批次名称
- `group`: 分组
- `mkt`: 市场
- `remark`: 备注
- `accountId`: 账户ID
- `firm`: 公司
- `version`: 版本

## 故障排除

### 常见问题
1. **无法获取数据**: 检查 API 服务是否正常运行
2. **页面加载慢**: 尝试减少数据量或增加刷新间隔
3. **图表显示异常**: 检查数据格式是否正确

### 日志查看
```bash
# 查看运行日志
tail -f monitor.log
```

## 扩展功能

系统支持以下扩展：
1. 添加更多预警条件
2. 集成邮件/短信通知
3. 添加历史数据分析
4. 支持更多图表类型
5. 添加用户权限管理

## 技术支持

如有问题，请检查：
1. Python 环境和依赖包是否正确安装
2. API 服务是否可访问
3. 网络连接是否正常
4. 端口是否被占用
