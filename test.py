import os, sys
import datetime

from misc.ssh_conn import ftp_clent_zx_zhongtai, sftp_clent_wintrader
from misc.tools import find_latest_remote_file_on_date
from misc.Readstockfile import read_remote_file

# lst = ftp_clent_zxzhongtai.listdir('')
# print(lst)
# from data_utils.trading_calendar import Calendar

from misc.tools import get_stock_adj_close


work_date=None
work_date='20250522'


def get_target(work_date=None):
    if work_date is None:
        work_date = datetime.datetime.now().strftime("%Y%m%d")
    target_dir = os.path.join('daily_before', work_date)
    target_file = find_latest_remote_file_on_date(ftp_clent_zx_zhongtai, 
                                    dir=target_dir, 
                                    file_prefix='targetPosition_', date=work_date)
    target_path = os.path.join(target_dir, target_file)

    df = read_remote_file(path=target_path, 
                        src_type='zxzhongtai')
    return df

# df = get_target(work_date)
# print(df.head(3))

# print(sftp_clent_wintrader.listdir(''))



# df = get_stock_adj_close(work_date)

# print(f'{12313:,.1f}')
sftp_clent_wintrader.put('test.md', os.path.join('交易文件', '超量子中泰', 'test.md'))
# sftp_clent_wintrader.put('test.md', '交易文件\\超量子中泰\\test.md')