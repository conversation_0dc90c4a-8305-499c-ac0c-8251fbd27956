#%%
import os, sys
import pandas as pd
import numpy as np
from loguru import logger
from typing import List, Union
# current_dir = os.path.dirname(os.path.abspath(__file__))
from datetime import datetime
from ast import literal_eval
from pprint import pprint
from dbfread import FieldParser

# # 将项目路径添加到模块搜索路径
# project_dir = os.path.abspath(os.path.join(current_dir, ".."))  # 通过..返回上一级目录
# sys.path.append(project_dir)


from misc import utils
from data_utils.trading_calendar import Calendar
from misc.Readstockfile import read_remote_file, write_file, read_file
from misc.utils import check_split_account_data, timetag_latest_by_date, compare_diff
from misc.redis_func import df_write_redis, conn_redis, df_read_redis
from misc.tools import get_target_by_date, get_latest_hold


from accounts_config.config_files import *
from accounts_config.sys_config import ZHONGXINAPI_PATH, GUANGDAAPI_PATH


from accounts_config.kf_accounts_basic import kf_accounts_dict, kf_futures_accounts, futureStra_account_dict
from accounts_config.kf_accounts_basic import futureIX_account_dict, futureIX_product_dict

from misc.standard_table_dev import standardize_hold, standardize_deal, standardize_order, standardize_deal_minimal
from misc.standard_table_dev import standardize_marginaccount, standardize_stockaccount

from misc.standard_table_dev import standardize_hold_future, standardize_deal_future, standardize_order_future, standardize_account_future


# sys.path.insert(0, ZHONGXINAPI_PATH)
# from zhongxinliangrong import api_func as zhongxin_onsite_api_func
# sys.path.remove(ZHONGXINAPI_PATH)

# 导入指定python文件
# def load_package_from_path(pkg_path: str) :
#     """
#     ref: https://stackoverflow.com/a/********
#     """
#     import importlib.util
#     import sys

#     pkg_name = os.path.basename(pkg_path).replace('.py', '')
#     spec = importlib.util.spec_from_file_location(pkg_name, pkg_path)
#     foo = importlib.util.module_from_spec(spec)
#     sys.modules[pkg_name] = foo
#     spec.loader.exec_module(foo)
#     # foo.MyClass()

#     return sys.modules[pkg_name]

# api_func = load_package_from_path(os.path.join(ZHONGXINAPI_PATH, 'zhongxinliangrong', 'api_func.py'))


rs = conn_redis()
#%%
# if len(sys.argv) > 1:
#     date = str(sys.argv[1])
# else:
#     date = datetime.now().strftime('%Y%m%d')

# date = '********'

# ax onequant
# order_file = 'order_{}.csv'.format(date)
# deal_file = 'trade_{}.csv'.format(date)
# hold_file = 'positionInfo_{}.csv'.format(date)
# account_file = 'assetInfo_{}.csv'.format(date)

# source_dir = '数据导出/安信OneQuant交易端'
#%%
# ax_onequant_accounts_dict = {
#     # qmt账户备注  :   account_name
#     '************'      :   '宽辅500指增1号_安信',
#     '************'      :   '远航安心中性2号_安信',
# }

# ClientName

#%%
# for file in [
#     order_file,
#     deal_file,
#     hold_file,
#     account_file
# ]:
#     df = read_remote_file(os.path.join(source_dir, file), src_type='trade',
#                             dtype = {
#                                 '账号' : str,
#                                 '订单编号' : str,
#                                 '成交编号' : str,
#                                 '更新时间' : str,
#                                 '成交日期' : str,
#                                 '订单日期' : str,
#                                 '成交时间' : str,
#                                 '订单时间' : str,
#                             })
#     print(df.tail(3))
    
def split_account_df(df, accountid_list, account_col):
    # 如果总的数据为空
    if len(df) == 0:
        return pd.DataFrame()
    
    # 如果是多个账户
    try:
        # if len(accountid_list.split(',')) > 1:
        #     account_id_lst = [x.strip() for x in accountid_list.split(',')]
        #     # print(account_id_lst)
        # else:
        #     account_id_lst = [accountid_list]
        
        account_df = df[df[account_col].isin(accountid_list)]
    except KeyError as ke:
        if account_col == '资金账号':
            account_col = '账号'
            account_df = df[df[account_col].isin(accountid_list)]  # gt 账户手动导出表格
        else:
            raise KeyError(f'{account_col} 字段不存在, 无法找到 {str(accountid_list)} 的记录')
    return account_df.copy()




def get_trade_config():
    trade_config_file = 'others/trade_config.xlsx'
    trade_config = read_remote_file(trade_config_file, src_type='dav',
                                    dtype={'start_time':str, 'end_time':str}
                                    )
    if 'No.' in trade_config.columns:
        trade_config.drop(columns=['No.'], inplace=True)
    trade_config.set_index('account_name', inplace=True)
    # trade_config = trade_config.replace(np.nan, None)
    trade_config = trade_config[trade_config['active'] == True]
    
    # print(trade_config)
    trade_config = trade_config.to_dict(orient='index') 
    # return dict
    return trade_config

# %%
# exclude_list = list(accounts_dict.keys())
# check_split_account_data(df, colname='账号', exclude_list=exclude_list)


# # config
# src_dict = {
#     '国信gtrade' : {
#         'client_type' : 'gtrade',
#         'accounts_dict' : {
#             '**************'      :   '远航安心中性1号',
#             '**************'      :   '宽辅泓涛专享1号',
#             '**************'      :   '宽辅专享1号',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/国信证券GTrade/Stock/Position-{}.csv',    'src_type' : 'aws', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/国信证券GTrade/Stock/Deal-{}.csv',        'src_type' : 'aws', 'account_col' : '资金账号', 'dtype':{'资金账号':str, '委托编号':str}},
#             'order'  : {'file_path' : '数据导出/国信证券GTrade/Stock/AlgoSuborder-{}.csv','src_type' : 'aws', 'account_col' : '资产账户', 'dtype':{'资产账户':str, '委托编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/国信证券GTrade/Stock/Account-{}.csv',     'src_type' : 'aws', 'account_col' : '资产账户', 'dtype':{'资产账户':str}},
#             # {'file_path' : '数据导出/国信GTrade交易端/Stock/Suborder-{}.csv', 'src_type' : 'trade', 'account_col' : '资金账号'},
#         },
#     },
    
#     '东方证券ato' : {
#         'client_type' : 'kafangato_dbf',
#         'accounts_dict' : {
#             '1986'      : '宽辅泓涛专享1号_东方',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '交易扫单监控目录/东方dma卡方/ReportPosition_{}.dbf', 'src_type' : 'aws', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#             'deal'   : {'file_path' : '交易扫单监控目录/东方dma卡方/DealOrder_{}.dbf',      'src_type' : 'aws', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str}},
#             'order'  : {'file_path' : '交易扫单监控目录/东方dma卡方/SubOrderAlgo_{}.dbf',   'src_type' : 'aws', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str,'SysDealId':str}},
#             'account': {'file_path' : '交易扫单监控目录/东方dma卡方/ReportBalance_{}.dbf',  'src_type' : 'aws', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#         },
#     },

#     '国联证券ato' : {
#         'client_type' : 'kafangato_dbf',
#         'accounts_dict' : {
#             '*********'      : '宽辅联丰专享_国联',
#             '*********'      : '远航安心中性6号_国联',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '交易扫单监控目录/国联dma卡方/ReportPosition_{}.dbf', 'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#             'deal'   : {'file_path' : '交易扫单监控目录/国联dma卡方/DealOrder_{}.dbf',      'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str}},
#             'order'  : {'file_path' : '交易扫单监控目录/国联dma卡方/SubOrderAlgo_{}.dbf',   'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str,'SysDealId':str}},
#             'account': {'file_path' : '交易扫单监控目录/国联dma卡方/ReportBalance_{}.dbf',  'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#         },
#     },

#     # '长城证券ato' : {
#     #     'client_type' : 'kafangato_dbf',
#     #     'accounts_dict' : {
#     #         '1'      : '远航安心中性6号_长城',
#     #     },
#     #     'src_files' : {
#     #         'hold'   : {'file_path' : '交易扫单监控目录/长城dma卡方/ReportPosition_{}.dbf', 'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#     #         'deal'   : {'file_path' : '交易扫单监控目录/长城dma卡方/DealOrder_{}.dbf',      'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str}},
#     #         'order'  : {'file_path' : '交易扫单监控目录/长城dma卡方/SubOrderAlgo_{}.dbf',   'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str,'SysDealId':str}},
#     #         'account': {'file_path' : '交易扫单监控目录/长城dma卡方/ReportBalance_{}.dbf',  'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#     #     },
#     # },

#     '国君dma卡方' : {
#         'client_type' : 'kafangatx_dbf',
#         'accounts_dict' : {
#             '宽辅远航安心市场中性6号私募（DMA）'      : '远航安心中性6号_国君',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '交易扫单监控目录/国君dma卡方/ReportPosition_{}.dbf', 'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#             'deal'   : {'file_path' : '交易扫单监控目录/国君dma卡方/SubOrderAlgo_{}.dbf',   'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str}},
#             'order'  : {'file_path' : '交易扫单监控目录/国君dma卡方/SubOrderAlgo_{}.dbf',   'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str}},
#             'account': {'file_path' : '交易扫单监控目录/国君dma卡方/ReportBalance_{}.dbf',  'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#         },
#     },
    
#     '银河dma卡方' : {
#         'client_type' : 'kafangalgo_dbf',
#         'accounts_dict' : {
#             'OTC000125545'      : '远航安心中性1号_银河',
#             'OTC000127384'      : '远航安心中性6号_银河',
#             'OTC000130141'      : '宽辅专享5号',
#             'OTC000130002'      : '宽辅泓涛专享1号_银河',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '交易扫单监控目录/银河dma卡方/ReportPosition_{}.dbf', 'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#             'deal'   : {'file_path' : '交易扫单监控目录/银河dma卡方/SubOrderAlgo_{}.dbf',   'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str}},
#             'order'  : {'file_path' : '交易扫单监控目录/银河dma卡方/SubOrderAlgo_{}.dbf',   'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str}},
#             'account': {'file_path' : '交易扫单监控目录/银河dma卡方/ReportBalance_{}.dbf',  'src_type' : 'trade', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#         },
#     },
    
#     '浙商dma卡方' : {
#         'client_type' : 'kafangalgo_dbf',
#         'accounts_dict' : {
#             'KUANFU-ZHUANXIANG1'      : '宽辅专享1号_浙商',
#             'KUANFU-ANXIN6'           : '远航安心中性6号_浙商',
#         },
#         # could be aws source
#         'src_files' : {
#             'hold'   : {'file_path' : '交易扫单监控目录/浙商dma卡方/ReportPosition_{}.dbf', 'src_type' : 'aws', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#             'deal'   : {'file_path' : '交易扫单监控目录/浙商dma卡方/SubOrderAlgo_{}.dbf',   'src_type' : 'aws', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str}},
#             'order'  : {'file_path' : '交易扫单监控目录/浙商dma卡方/SubOrderAlgo_{}.dbf',   'src_type' : 'aws', 'account_col' : 'ClientName', 'dtype':{'ClientName':str,'ClOrdId':str}},
#             'account': {'file_path' : '交易扫单监控目录/浙商dma卡方/ReportBalance_{}.dbf',  'src_type' : 'aws', 'account_col' : 'ClientName', 'dtype':{'ClientName':str}},
#         },
#     },
    
#     # '中信cats' : {
#     #     'client_type' : 'zhongxin_cats',
#     #     'accounts_dict' : {
#     #         '118965--CNY--RM'         : '远航安心中性6号_中信',
#     #         '118965--CNY--RM--B'      : '中信多空',
#     #     },
#     #     'src_files' : {
#     #         'hold'   : {'file_path' : '数据导出/中信证券cats/csv/EQDPosition_{}.csv', 'src_type' : 'trade', 'account_col' : '合约编号', 'dtype':{'合约编号':str}},
#     #         'deal'   : {'file_path' : '数据导出/中信证券cats/csv/StockOrder_{}.csv',  'src_type' : 'trade', 'account_col' : '账户', 'dtype':{'账户':str,'订单号':str}},
#     #         'order'  : {'file_path' : '数据导出/中信证券cats/csv/StockOrder_{}.csv',  'src_type' : 'trade', 'account_col' : '账户', 'dtype':{'账户':str,'订单号':str}},
#     #         'account': {'file_path' : '数据导出/中信证券cats/csv/EQDFund_{}.csv',     'src_type' : 'trade', 'account_col' : '合约编号', 'dtype':{'合约编号':str}},
#     #     },
#     # },

#     # '招商dma交易大师': {
#     #     'client_type' : 'zhaoshang_master',
#     #     'accounts_dict' : {
#     #         'S8000015669'    :    '远航安心中性1号_招商',    
#     #     },
#     #     'src_files' : {
#     #         'hold'   : {'file_path' : '数据导出/远航安心中性1号_招商/{}/多空收益互换存续合约.xlsx', 'src_type' : 'trade', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#     #         'deal'   : {'file_path' : '数据导出/远航安心中性1号_招商/{}/多空收益互换成交.xlsx',    'src_type' : 'trade', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'成交编号':str}},
#     #         'order'  : {'file_path' : '数据导出/远航安心中性1号_招商/{}/多空收益互换订单.xlsx',    'src_type' : 'trade', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'委托编号':str}},
#     #         'account': {'file_path' : '数据导出/远航安心中性1号_招商/{}/多空收益互换资金.xlsx',    'src_type' : 'trade', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#     #     }
#     # },

#     # '广发': {}
#     '广发dma-泓涛专享1号': {
#         'client_type' : 'guangfa_jsd',
#         'accounts_dict' : {
#             '1'    :    '宽辅泓涛专享1号_广发',    
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/宽辅泓涛专享1号_广发/{}/PositionList.csv', 'src_type' : 'trade', 'account_col' : '产品编号', 'dtype':{'产品编号':str}},
#             'deal'   : {'file_path' : '数据导出/宽辅泓涛专享1号_广发/{}/TradeList.csv',    'src_type' : 'trade', 'account_col' : '', 'dtype':{'成交编号':str}},
#             'order'  : {'file_path' : '数据导出/宽辅泓涛专享1号_广发/{}/OrderList.csv',    'src_type' : 'trade', 'account_col' : '产品编号', 'dtype':{'产品编号':str,'意向编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/宽辅泓涛专享1号_广发/{}/FundInfo.csv',     'src_type' : 'trade', 'account_col' : '产品编号', 'dtype':{'产品编号':str}},
#         }
#     },
    
#     # 以下为平层 
#     '安信onequant' : {
#         'client_type' : 'ax_onequant',
#         'accounts_dict' : {
#             '************'      :   '宽辅500指增1号_安信',
#             '************'      :   '远航安心中性2号_安信',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/安信OneQuant交易端/positionInfo_{}.csv', 'src_type' : 'trade', 'account_col' : '账号', 'dtype':{'账号':str}},
#             'deal'   : {'file_path' : '数据导出/安信OneQuant交易端/trade_{}.csv',        'src_type' : 'trade', 'account_col' : '账号', 'dtype':{'账号':str,'订单编号':str}},
#             'order'  : {'file_path' : '数据导出/安信OneQuant交易端/order_{}.csv',        'src_type' : 'trade', 'account_col' : '账号', 'dtype':{'账号':str,'订单编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/安信OneQuant交易端/assetInfo_{}.csv',    'src_type' : 'trade', 'account_col' : '账号', 'dtype':{'账号':str}},
#         },
#     },
    
#     '东北证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '********'      :   '宽辅量化选股1号_东北',
#             '********'      :   '宽辅专享6号_东北',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/东北证券qmt/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/东北证券qmt/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/东北证券qmt/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/东北证券qmt/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '东方证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '********'      :   '远航安心中性2号_东方',
#             '********'      :   '宽辅泓涛专享2号',
#             '********'      :   '宽辅专享6号',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/东方证券qmt/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/东方证券qmt/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/东方证券qmt/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/东方证券qmt/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '广发证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '********'      :   '宽辅专享2号',
#             '********'      :   '宽辅思贤专享中性1号_广发',
#             '********'      :   '宽辅思贤专享中性2号_广发',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/广发证券qmt/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/广发证券qmt/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/广发证券qmt/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/广发证券qmt/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '民生证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '**********'      :   '远航安心中性2号_民生',
#             '**********'      :   '宽辅量化选股1号_民生',
#             '**********'      :   '宽辅臻好专享_民生',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/民生证券qmt/Stock/PositionStatics-{}.csv', 'src_type' : 'trade', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/民生证券qmt/Stock/Deal-{}.csv',            'src_type' : 'trade', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/民生证券qmt/Stock/Order-{}.csv',           'src_type' : 'trade', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/民生证券qmt/Stock/Account-{}.csv',         'src_type' : 'trade', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '国信证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '************'      :   '远航安心中性3号',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/远航安心中性3号/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/远航安心中性3号/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/远航安心中性3号/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/远航安心中性3号/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '中信证券qmt-思贤1号' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '4086413'      :   '宽辅思贤专享中性1号',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/宽辅思贤专享中性1号/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/宽辅思贤专享中性1号/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/宽辅思贤专享中性1号/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/宽辅思贤专享中性1号/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '中信证券qmt-泓涛2号' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '4104866'      :   '宽辅泓涛专享2号_中信',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/宽辅泓涛专享2号_中信/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/宽辅泓涛专享2号_中信/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/宽辅泓涛专享2号_中信/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/宽辅泓涛专享2号_中信/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '天风证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '********'      :   '宽辅500指增1号_天风',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/宽辅500指增1号_天风/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/宽辅500指增1号_天风/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/宽辅500指增1号_天风/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/宽辅500指增1号_天风/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '招商证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '**********'      :   '宽辅1000指增1号_招商',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/宽辅1000指增1号_招商/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/宽辅1000指增1号_招商/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/宽辅1000指增1号_招商/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/宽辅1000指增1号_招商/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '联储证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '********'      :   '宽辅联丰专享',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/宽辅联丰专享/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/宽辅联丰专享/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/宽辅联丰专享/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/宽辅联丰专享/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '中金证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '********'      :   '宽辅金品中国专享',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/宽辅金品中国专享/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/宽辅金品中国专享/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/宽辅金品中国专享/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/宽辅金品中国专享/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '国君证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '********'      :   '宽辅臻好专享_国君',
#             '********'      :   '远航安心中性5号_国君',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/国君证券qmt/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/国君证券qmt/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/国君证券qmt/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/国君证券qmt/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
    
#     '平安证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '************'      :   '宽辅1000指增1号_平安',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/宽辅1000指增1号_平安/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/宽辅1000指增1号_平安/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/宽辅1000指增1号_平安/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/宽辅1000指增1号_平安/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
#     '浙商证券qmt' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '*********'      :   '远航安心中性2号_浙商',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/浙商证券qmt/Stock/PositionStatics-{}.csv', 'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/浙商证券qmt/Stock/Deal-{}.csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/浙商证券qmt/Stock/Order-{}.csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/浙商证券qmt/Stock/Account-{}.csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
#     '中泰证券PB' : {
#         'client_type' : 'xuntou',
#         'accounts_dict' : {
#             '1'      :   '宽辅瑞年专享_中泰',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/中泰证券PB/Stock/PositionDetail({}).csv',  'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#             'deal'   : {'file_path' : '数据导出/中泰证券PB/Stock/Deal({}).csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#             'order'  : {'file_path' : '数据导出/中泰证券PB/Stock/Order({}).csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/中泰证券PB/Stock/Account({}).csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#         },
#     },
#     '东方财富EMC' : {
#         'client_type' : 'dfemc',
#         'accounts_dict' : {
#             '************'      :   '宽辅思贤专享中性2号_东财',
#         },
#         'src_files' : {
#             'hold'   : {'file_path' : '数据导出/东方财富EMC/{account_id}_POSITION.{date}.csv',  'src_type' : 'trade', 'dtype':{}},
#             'deal'   : {'file_path' : '数据导出/东方财富EMC/{account_id}_MATCH.{date}.csv',     'src_type' : 'trade', 'dtype':{'委托编号':str}},
#             'order'  : {'file_path' : '数据导出/东方财富EMC/{account_id}_ORDER.{date}.csv',     'src_type' : 'trade', 'dtype':{'委托编号':str,'成交编号':str}},
#             'account': {'file_path' : '数据导出/东方财富EMC/{account_id}_FUND.{date}.csv',      'src_type' : 'trade', 'dtype':{}},
#         },
#     }
    

#     # 'GT trader' : {
#     #     'client_type' : 'xuntou',
#     #     'accounts_dict' : {
#     #         '**********'      :   '远航安心中性2号_民生',
#     #         '**********'      :   '宽辅量化选股1号_民生',
#     #     },
#     #     'src_files' : {
#     #         'hold'   : {'file_path' : '数据导出/迅投GT导出/Stock/PositionDetail({}).csv',  'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#     #         'deal'   : {'file_path' : '数据导出/迅投GT导出/Stock/Deal({}).csv',            'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str}},
#     #         'order'  : {'file_path' : '数据导出/迅投GT导出/Stock/Order({}).csv',           'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str,'订单编号':str,'合同编号':str,'成交编号':str}},
#     #         'account': {'file_path' : '数据导出/迅投GT导出/Stock/Account({}).csv',         'src_type' : 'trade2', 'account_col' : '资金账号', 'dtype':{'资金账号':str}},
#     #     },
#     # }
# }



def parse_account_source_config(config_sheet='config_normal'):
    # Load the configurations
    
    config_path = os.path.join(os.path.join(account_data_source_config_dir, account_data_source_config_file))
    # df_config = pd.read_excel(config_path, sheet_name='config')
    df_config = read_remote_file(config_path, src_type=account_data_source_config_src_type, sheet_name=config_sheet)
    # Load the account details
    # df_accounts = pd.read_excel(account_details_path, sheet_name='account_details')
    df_accounts = read_remote_file(config_path, src_type=account_data_source_config_src_type, sheet_name='account_dict', dtype={'account_id':'str'})
    
    # Filter out inactive rows
    df_config_active = df_config[df_config['active'] == True]
    df_accounts_active = df_accounts[df_accounts['active'] == True]
    # pprint(df_config_active[['key', 'account_col']])
    # df_config_active.loc[:, 'account_col'] = df_config_active.loc[:, 'account_col'].replace(np.nan, None)
    # pprint(df_config_active[['key', 'account_col']])
    
    # Initialize the main dictionary that will be returned
    result_dict = {}
    
    # Process active configurations
    for index, row in df_config_active.iterrows():
        key = row['key']
        default_account_col = row['account_col']
        # print(literal_eval(row['hold_dtype']))
        
        
        result_dict[key] = {
            'client_type': row['client_type'],
            'accounts_dict': {},
            'src_files': {
                'hold': {
                    'file_path': os.path.join(row['root_dir'], row['hold_file']),
                    'src_type': row['src_type'],
                    'account_col': row['hold_account_col'] if pd.notna(row['hold_account_col']) else default_account_col,
                    'dtype': literal_eval(row['hold_dtype'])
                },
                'deal': {
                    'file_path': os.path.join(row['root_dir'], row['deal_file']),
                    'src_type': row['src_type'],
                    'account_col': row['deal_account_col'] if pd.notna(row['deal_account_col']) else default_account_col,
                    'dtype': literal_eval(row['deal_dtype'])
                },
                'order': {
                    'file_path': os.path.join(row['root_dir'], row['order_file']),
                    'src_type': row['src_type'],
                    'account_col': row['order_account_col'] if pd.notna(row['order_account_col']) else default_account_col,
                    'dtype': literal_eval(row['order_dtype'])
                },
                'account': {
                    'file_path': os.path.join(row['root_dir'], row['account_file']),
                    'src_type': row['src_type'],
                    'account_col': row['account_account_col'] if pd.notna(row['account_account_col']) else default_account_col,
                    'dtype': literal_eval(row['account_dtype'])
                }
            }
        }
    
    # Process active account details and populate accounts_dict for each key
    for index, row in df_accounts_active.iterrows():
        key = row['key']
        account_id = str(row['account_id'])
        account_name = row['account_name']
        
        if key in result_dict:
            result_dict[key]['accounts_dict'][account_id] = account_name

    return result_dict




class CustomNumberFieldParser(FieldParser):
    def parseN(self, field, data):
        data_str = data.strip()
        if data_str == b'undefined':
            return None  # or return a default value
        try:
            return int(data)
        except ValueError:
            try:
                return float(data.replace(b',', b'.'))
            except ValueError:
                return None  # or a default value


# def export_cicc_ims_short_hold(account_name, date):
#     from misc.VARS import Cicc_Ims_Short_Hold_Root, Cicc_Ims_Short_Hold_File, Cicc_Ims_Short_Hold_Src_type, Cicc_Client_Accounts
#     from misc.standardize_dev import cicc_ims
#     try:
#         tmp_df = read_remote_file(os.path.join(Cicc_Ims_Short_Hold_Root, Cicc_Ims_Short_Hold_File), src_type=Cicc_Ims_Short_Hold_Src_type, sep='|')
#         PortfolioId = Cicc_Client_Accounts[account_name]
        
#         tmp_df = tmp_df[tmp_df['投资组合'].astype('str') == PortfolioId]
#         tmp_df = cicc_ims.std_hold(tmp_df)
#         tmp_df = tmp_df[tmp_df['volume'] < 0]
#         logger.warning(f'将合并中金多空IMS客户端空头持仓: \n{tmp_df.head()}')
#     except FileNotFoundError:
#         try:
#             tmp_df = get_latest_hold(account_name, date, config={})
#             tmp_df = tmp_df[tmp_df['volume'] < 0]
            
#         except FileNotFoundError:
#             tmp_df = pd.DataFrame(columns=['ticker', 'volume', 'available_volume'])
#     return tmp_df
    

# def replace_cats_origin_deal_order_by_fullorder(src_config, date):
#     sys.path.insert(0, ZHONGXINAPI_PATH)
#     from zhongxinshort import api_func as zhongxin_offsite_api_func
#     sys.path.remove(ZHONGXINAPI_PATH)
    
#     for account_name in src_config['accounts_dict'].values():
#         try:
#             fullorder_deal = zhongxin_offsite_api_func.get_swap_orders(account_name)
#             if not isinstance(fullorder_deal, pd.DataFrame):
#                 logger.error(f'fullorder deal api error, {account_name} deal 用 0 代替')
#                 fullorder_deal = pd.DataFrame()
            
#             elif  fullorder_deal is None:
#                 fullorder_deal = pd.DataFrame()    
#         except :
#             logger.error(f'fullorder deal not found from api: {account_name}, 用 0 代替')
#             fullorder_deal = pd.DataFrame()
#         df_write_redis(rs, key=f'{date}.stock.{account_name}.deal.origin', df=fullorder_deal)
#         df_write_redis(rs, key=f'{date}.stock.{account_name}.order.origin', df=fullorder_deal)
        
# def replace_cats_origin_hold_by_api(src_config, date):
#     sys.path.insert(0, ZHONGXINAPI_PATH)
#     from zhongxinshort import api_func as zhongxin_offsite_api_func
#     sys.path.remove(ZHONGXINAPI_PATH)
    
#     for account_name in src_config['accounts_dict'].values():
#         try:
#             latest_hold = zhongxin_offsite_api_func.get_latesthold(account_name)
#             if not isinstance(latest_hold, pd.DataFrame):
#                 logger.error(f'hold api error, {account_name} hold 用 0 代替')
#                 latest_hold = pd.DataFrame(columns=['ticker', 'volume', 'available_volume'])

#         except :
#             logger.error(f'latest hold not found from api: {account_name}, 用 0 代替')
#             latest_hold = pd.DataFrame(columns=['ticker', 'volume', 'available_volume'])
#         df_write_redis(rs, key=f'{date}.stock.{account_name}.hold.origin', df=latest_hold)
        
        

def format_accountid_list(account_id, client_key, client_type, cate):
    if len(account_id.split(',')) > 1:
        account_id_lst = [x.strip() for x in account_id.split(',')]
        
        # 特殊处理, 光大多空ATX, hold, account要单账户, deal, order 多账户
        if client_type == 'kafangatx_dbf' and client_key == '光大场外多空ATX' and cate in ['hold', 'account']:
            account_id_lst = account_id_lst[:1]
        # print(account_id_lst)
    else:
        account_id_lst = [account_id]
    return account_id_lst

def type_to_file_path(date, account_type, account_name, file_cate, suffix):
    """key=f'{date}.{account_type}.{account_name}.{file_cate}{suffix}'"""
    if suffix != '':
        suffix = '_' + suffix
        
    if account_type == 'stock':
        if file_cate in ['hold']:
            file_path = os.path.join(account_name, file_cate, f'{file_cate}{suffix}_{date}.csv')
        else:
            file_path = os.path.join(account_name, 'account', f'{file_cate}{suffix}_{date}.csv')
            
    else: # future
        file_path = os.path.join('期货账户', account_name, f'{file_cate}{suffix}_{date}.csv')
    return file_path
    


def load_src_into_redis(
        scr_dict, 
        date, 
        cate:Union[List[str], str]='all',
        suffix='origin',
        client_name=None,  # 筛选只需要的客户端配置名称key, 如"中信cats"
        account_type='stock'    # stock, future
        ):
    
    def split_multi_account_in_one_file(src, src_config, date, cate, suffix=''):
        client_type = src_config['client_type']
        accounts_dict = src_config['accounts_dict']
        src_files = src_config['src_files']
        if cate == 'all':
            cate = list(src_files.keys())
        elif isinstance(cate, list):
            pass
        else:
            cate = [cate]
        
        for file_cate in cate:
            file_path = src_files[file_cate]['file_path']
            src_type = src_files[file_cate]['src_type']
            account_col = src_files[file_cate]['account_col']
            dtype_args = src_files[file_cate].get('dtype', {})
            kwargs = {}
            
            # TODO file not found
            try:
                if client_type == 'zhongxin_cats' or client_type == 'zhongxin_cats_credit':
                    file_path = file_path.format('-'.join([date[0:4], date[4:6], date[6:8]]))
                else:
                    file_path = file_path.format(date)

                # 如果是宇量客户端 dbf, 需要修正一个parser
                # print(client_type)
                if client_type == 'tf_yulia_dbf':
                    kwargs['parserclass'] = CustomNumberFieldParser
                
                df = read_remote_file(path=file_path, src_type=src_type, dtype=dtype_args, **kwargs)
            except FileNotFoundError:
                logger.error(f'file not found: {file_path}')
                continue
            # print(df.head(2))
            # split account
            # if len(accounts_dict) == 1:
            #     account_name = list(accounts_dict.values())[0]
            #     df_write_redis(rs, key=f'{date}.stock.{account_name}.{file_cate}{suffix}', df=df)
            
            # write_path = type_to_file_path(date, account_type, account_name, file_cate, suffix)
            # print(account_col)
            if not pd.notna(account_col):
                account_name = list(accounts_dict.values())[0]
                
                # write_file(df, dest_type='dav', dest_path=write_path, file_type='csv', index=False)
                
                df_write_redis(rs, key=f'{date}.{account_type}.{account_name}.{file_cate}{suffix}', df=df)
                
            else:
                for account_id in accounts_dict.keys():
                    account_name = accounts_dict[account_id]
                    # 将1个或多个账户id 对应到账户名account_name
                    accountid_list = format_accountid_list(account_id=account_id, client_key=src, client_type=client_type, cate=file_cate)
                    
                    account_df = split_account_df(df, accountid_list, account_col)
                    # print('account_df', account_df.head(2))
                    df_write_redis(rs, key=f'{date}.{account_type}.{account_name}.{file_cate}{suffix}', df=account_df)
                    # write_file(account_df, dest_type='dav', dest_path=write_path, file_type='csv', index=False)
                # check 完整性
                else:
                    exclude_list = [ x.strip() for x in ','.join(list(accounts_dict.keys())).split(',') ]
                    check_split_account_data(df, colname=account_col, exclude_list=exclude_list)

    def read_multi_account_from_multi_files(src, src_config, date, cate, suffix=''):
        client_type = src_config['client_type']
        accounts_dict = src_config['accounts_dict']
        src_files = src_config['src_files']
        kwargs = {}
        
        # 宇量的dbf 解析错误, 需要修正一个parser
        if client_type == 'tf_yulia_dbf':
            kwargs['parserclass'] = CustomNumberFieldParser
        
        # 完整账户文件
        for account_id in src_config['accounts_dict'].keys():
            account_name = src_config['accounts_dict'][account_id]
            
            if cate == 'all':
                cate = list(src_files.keys())
            elif isinstance(cate, list):
                pass
            else:
                cate = [cate]
            for file_cate in cate:
                file_path = src_files[file_cate]['file_path'].format(account_id=account_id, date=date)
                src_type = src_files[file_cate]['src_type']
                # account_col = src_files[file_cate]['account_col'] ##
                dtype_args = src_files[file_cate].get('dtype', {})
                
                # write_path = type_to_file_path(date, account_type, account_name, file_cate, suffix)
                try:
                    df = read_remote_file(path=file_path, src_type=src_type, dtype=dtype_args, **kwargs)
                except FileNotFoundError:
                    logger.error(f'file not found: {file_path}')
                    continue            
                df_write_redis(rs, key=f'{date}.{account_type}.{account_name}.{file_cate}{suffix}', df=df)
                # write_file(df, dest_type='dav', dest_path=write_path, file_type='csv', index=False)

    if pd.notna(client_name) or client_name is not None:
        scr_dict_cp = {client_name: scr_dict[client_name]}
    else:
        scr_dict_cp = scr_dict
    for src in scr_dict_cp:
        print(f'read origin data: {src} ...')

        src_config = scr_dict[src]
        # 多账户, 多文件
        if src in ['东方财富EMC', '天风宇量TRAX']:
            read_multi_account_from_multi_files(src, src_config, date, cate, suffix=suffix)
        # 多账户, 一个文件
        else:
            split_multi_account_in_one_file(src, src_config, date, cate, suffix=suffix)
        
        # 替换 cats 的 deal 数据为 api 获取的数据
        # if src in ['中信cats'] and (cate == 'all' or 'deal' in cate or 'order' in cate) and suffix == '.origin':
        #     replace_cats_origin_deal_order_by_fullorder(src_config, date)

        # if src in ['中信cats'] and (cate == 'all' or 'hold' in cate) and suffix == '.origin':
        #     replace_cats_origin_hold_by_api(src_config, date)
        
        # client_type = src_config['client_type']
        # accounts_dict = src_config['accounts_dict']
        # src_files = src_config['src_files']
        # if cate == 'all':
        #     cate = list(src_files.keys())
        # elif isinstance(cate, list):
        #     pass
        # else:
        #     cate = [cate]
        
        # for file_cate in cate:
        #     file_path = src_files[file_cate]['file_path']
        #     src_type = src_files[file_cate]['src_type']
        #     account_col = src_files[file_cate]['account_col']
        #     dtype_args = src_files[file_cate].get('dtype', {})
        #     # TODO file not found
        #     try:
        #         if client_type == 'zhongxin_cats':
        #             df = read_remote_file(path=file_path.format('-'.join([date[0:4], date[4:6], date[6:8]])),
        #                                 src_type=src_type, 
        #                                 dtype=dtype_args, 
        #                                 )
        #         else:
        #             df = read_remote_file(path=file_path.format(date), src_type=src_type, dtype=dtype_args)
        #     except FileNotFoundError:
        #         logger.warning(f'file not found: {file_path.format(date)}')
        #         continue
        #     # print(df.head(2))
        #     if len(accounts_dict) == 1:
        #         account_name = list(accounts_dict.values())[0]
        #         df_write_redis(rs, key=f'{date}.stock.{account_name}.{file_cate}{suffix}', df=df)
        #     else:
        #         for account_id in accounts_dict.keys():
        #             account_name = accounts_dict[account_id]
        #             account_df = split_account_df(df, account_id, account_col)
        #             # print('account_df', account_df.head(2))
        #             df_write_redis(rs, key=f'{date}.stock.{account_name}.{file_cate}{suffix}', df=account_df)
        #         # check 完整性
        #         else:
        #             exclude_list = list(accounts_dict.keys())
        #             check_split_account_data(df, colname=account_col, exclude_list=exclude_list)
            
# %%
# date = '********'
# #%%
# load_src_into_redis(src_dict, date=date, cate=['hold', 'deal'], suffix='.origin')
# %%
# accounts = ['宽辅500指增1号_安信', '远航安心中性2号_安信',
#             '远航安心中性1号', '宽辅泓涛专享1号', '宽辅专享1号']



def load_std_hold_deal_position_redis(date):
    write_file_time_tag = timetag_latest_by_date(date=date)
    
    load_src_into_redis(src_dict, date, cate=['deal', 'hold'], suffix='.origin')
    # trade_config = get_trade_config()
    
    for account_name in kf_accounts_dict.keys():
        print(f'standardize {account_name} data ...')
        
        if 'client_type_dev' in kf_accounts_dict[account_name]:
            client_type = kf_accounts_dict[account_name]['client_type_dev']
        else:
            client_type = kf_accounts_dict[account_name]['client_type']
            
        # hold, deal
        # cates_dict = {
        #     'hold' : standardize_hold,
        #     'deal' : standardize_deal,
        # }
        # for cate in cates_dict:
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.hold.origin')
        
        # df_origin_path = type_to_file_path(date, 'stock', account_name, 'hold', 'origin')
        # try:
        #     df_origin = read_remote_file(df_origin_path, src_type='dav')
        # except FileNotFoundError:
        
        if df_origin is None:
            df_origin = pd.DataFrame()
            logger.error(f'{account_name} hold data not found, 用 0 代替')
        # print(df_origin)
        df = standardize_hold(df_origin, client_type=client_type)
        # 增加中信融券空头
        # if client_type == 'zhongxin_cats_credit':
        #     sys.path.insert(0, ZHONGXINAPI_PATH)
        #     from zhongxinliangrong import api_func as zhongxin_onsite_api_func
        #     sys.path.remove(ZHONGXINAPI_PATH)
        #     df = zhongxin_onsite_api_func.merge_zhongxin_credit_account_short_hold(account_name, std_hold=df)
        
        # 合并中金多空IMS客户端空头持仓
        # from misc.VARS import Cicc_Client_Accounts
        # if account_name in Cicc_Client_Accounts.keys():        
        #     tmp_df = export_cicc_ims_short_hold(account_name, date)
        #     df = _check_short_hold_duplicated(std_hold=df, short_hold=tmp_df)
        #     df = pd.concat([df, tmp_df], axis=0, ignore_index=True)
            
        # 光大多空账户, 根据日初持仓和成交进行调整
        # from misc.VARS import GuangDa_LS_Client_Accounts
        # if account_name in GuangDa_LS_Client_Accounts:
        #     sys.path.insert(0, GUANGDAAPI_PATH)
        #     import GuangdaLS_Service
        #     tmp_df = GuangdaLS_Service.get_ls_latest_hold(account_name)
        #     sys.path.remove(GUANGDAAPI_PATH)
        #     df = tmp_df
        
        df_write_redis(rs, key=f'{date}.stock.{account_name}.hold', df=df)
        # print(account_name, 'hold:\n', df.head(2))
        
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join(account_name, 'hold', 'hold_{}.csv'.format(write_file_time_tag)), 
                   dest_type='dav',
                   index=False)
        
        
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.deal.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
        # print(df_origin)
        try:
            df = standardize_deal(df_origin, date, client_type=client_type)
        except KeyError as ke:
            df = standardize_deal(pd.DataFrame(), date, client_type=client_type)
            logger.error(f'KeyError, {account_name} 的 deal 文件的 {ke} key not found, deal 用 0 代替')
        df_write_redis(rs, key=f'{date}.stock.{account_name}.deal', df=df)
        # print(account_name, 'deal:\n', df.head(2))

        df_pos = get_target_by_date(account_name, date, config=None)
        # try:
        #     flag_new_system = trade_config.get(account_name).get('new_system')
        #     if flag_new_system == True:
        #         df_pos = read_remote_file(path=os.path.join(account_name, 'position', 'position.{}091000.csv'.format(date)),
        #                                 src_type='ninner', header=None, names=['ticker', 'volume'])
        #     else: 
        #         df_pos = read_remote_file(path=os.path.join(account_name, 'position', 'position.{}091000.csv'.format(date)),
        #                                 src_type='inner', header=None, names=['ticker', 'volume'])
        # except FileNotFoundError:
        #     df_pos = pd.DataFrame({'ticker': [1], 'volume': [0]})                                      
        df_write_redis(rs, key=f'{date}.stock.{account_name}.position', df=df_pos)
        # print(account_name, 'position:\n', df_pos.head(2))
        
def load_std_hold_redis(date):
    write_file_time_tag = timetag_latest_by_date(date=date)

    load_src_into_redis(src_dict, date, cate=['hold'], suffix='.origin')
    
    for account_name in kf_accounts_dict.keys():
        print(f'standardize {account_name} hold ...')
        
        if 'client_type_dev' in kf_accounts_dict[account_name]:
            client_type = kf_accounts_dict[account_name]['client_type_dev']
        else:
            client_type = kf_accounts_dict[account_name]['client_type']
            
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.hold.origin')
        write_file(df_origin, 
                   file_type='csv',
                   dest_path=os.path.join(account_name, 'hold', 'hold_origin_{}.csv'.format(date)), 
                   dest_type='dav',
                   index=False)

        if df_origin is None:
            # df_origin = pd.DataFrame()
            df_origin = get_latest_hold(account_name, date, config={})
            logger.error(f'{account_name} hold file not found, 用 get_latest_hold 获取')
        # print(df_origin)
        df = standardize_hold(df_origin, client_type=client_type)
        # 增加中信融券空头
        # if client_type == 'zhongxin_cats_credit':
        #     sys.path.insert(0, ZHONGXINAPI_PATH)
        #     from zhongxinliangrong import api_func as zhongxin_onsite_api_func
        #     sys.path.remove(ZHONGXINAPI_PATH)
        #     df = zhongxin_onsite_api_func.merge_zhongxin_credit_account_short_hold(account_name, std_hold=df)

        # 合并中金多空IMS客户端空头持仓
        # from misc.VARS import Cicc_Client_Accounts
        # if account_name in Cicc_Client_Accounts.keys():        
        #     tmp_df = export_cicc_ims_short_hold(account_name, date)
        #     df = _check_short_hold_duplicated(std_hold=df, short_hold=tmp_df)
        #     df = pd.concat([df, tmp_df], axis=0, ignore_index=True)

        # # 光大多空账户, 根据日初持仓和成交进行调整
        # from misc.VARS import GuangDa_LS_Client_Accounts
        # if account_name in GuangDa_LS_Client_Accounts:
        #     sys.path.insert(0, GUANGDAAPI_PATH)
        #     import GuangdaLS_Service
        #     tmp_df = GuangdaLS_Service.get_ls_latest_hold(account_name)
        #     sys.path.remove(GUANGDAAPI_PATH)
        #     df = tmp_df
            
        df_write_redis(rs, key=f'{date}.stock.{account_name}.hold', df=df)
        # print(account_name, 'hold:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join(account_name, 'hold', 'hold_{}.csv'.format(write_file_time_tag)), 
                   dest_type='dav',
                   index=False)
        

def _check_short_hold_duplicated(std_hold, short_hold, ignore_duplicated=True):
    """
    检查std_hold 已经包含的空头和 short_hold 的ticker是否匹配
    """

    # check for duplicated ticker
    if std_hold[std_hold['volume'] < 0].shape[0] > 0:
        logger.warning(f"已包含空头持仓: \n\n{std_hold[std_hold['volume'] < 0]}")
        # 检查std_hold 已经包含的空头和 short_hold 的ticker是否匹配
        df_1 = std_hold[std_hold['volume'] < 0][['ticker', 'volume']].set_index('ticker').sort_index().astype('int')
        df_2 = short_hold[['ticker', 'volume']].set_index('ticker').sort_index().astype('int')
        
        if df_1.equals(df_2):
            # std_hold 修正为多头部分
            logger.warning('std_hold 已经包含空头, 并且和将合并的空头short_hold 匹配')
            std_hold = std_hold[std_hold['volume'] > 0].copy()
        else:
            logger.error('std_hold 已经包含空头, 并且和将合并的空头short_hold 不匹配')
            if ignore_duplicated:
                std_hold = std_hold[std_hold['volume'] > 0].copy()
            else:
                # compare_diff(std_hold[std_hold['volume'] < 0].copy(), short_hold.copy())
                raise ValueError(f"std_hold 已经包含空头, 差异比例 {compare_diff(std_hold[std_hold['volume'] < 0].copy(), short_hold.copy())}")
    return std_hold


def load_single_client_hold(client_name, date):
    write_file_time_tag = timetag_latest_by_date(date=date)

    load_src_into_redis(src_dict, date, cate=['hold'], suffix='.origin', client_name=client_name)
    
    for account_name in src_dict[client_name]['accounts_dict'].values():

        print(f'standardize {account_name} hold ...')
        
        # if 'client_type_dev' in kf_accounts_dict[account_name]:
        #     client_type = kf_accounts_dict[account_name]['client_type_dev']
        # else:
        client_type = kf_accounts_dict[account_name]['client_type']
            
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.hold.origin')
        if df_origin is None:
            # df_origin = pd.DataFrame()
            df_origin = get_latest_hold(account_name, date, config={})
            logger.error(f'{account_name} hold file not found, 用 get_latest_hold 获取')
        # print(df_origin)
        df = standardize_hold(df_origin, client_type=client_type)
        # 增加中信融券空头
        # if client_type == 'zhongxin_cats_credit':
        #     if date != Calendar.last_trading_day(Calendar.next_trading_day(datetime.now())).strftime('%Y%m%d'):
        #         logger.error(f'{date} 不是最新日期, 只能获取最新交易日的空头持仓, 跳过合并')
        #         continue
        #     sys.path.insert(0, ZHONGXINAPI_PATH)
        #     from zhongxinliangrong import api_func as zhongxin_onsite_api_func
        #     sys.path.remove(ZHONGXINAPI_PATH)
        #     df = zhongxin_onsite_api_func.merge_zhongxin_credit_account_short_hold(account_name, std_hold=df)
            
        # 合并中金多空IMS客户端空头持仓
        # from misc.VARS import Cicc_Client_Accounts
        # if account_name in Cicc_Client_Accounts.keys():        
        #     tmp_df = export_cicc_ims_short_hold(account_name, date)
        #     df = _check_short_hold_duplicated(std_hold=df, short_hold=tmp_df)
        #     df = pd.concat([df, tmp_df], axis=0, ignore_index=True)
            
        # # 光大多空账户, 根据日初持仓和成交进行调整
        # from misc.VARS import GuangDa_LS_Client_Accounts
        # if account_name in GuangDa_LS_Client_Accounts:
        #     sys.path.insert(0, GUANGDAAPI_PATH)
        #     import GuangdaLS_Service
        #     tmp_df = GuangdaLS_Service.get_ls_latest_hold(account_name)
        #     sys.path.remove(GUANGDAAPI_PATH)
        #     df = tmp_df
            
        df_write_redis(rs, key=f'{date}.stock.{account_name}.hold', df=df)
        # print(account_name, 'hold:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join(account_name, 'hold', 'hold_{}.csv'.format(write_file_time_tag)), 
                   dest_type='dav',
                   index=False)
        
        
def load_single_client_minimal_deal(client_name, date):
    # write_file_time_tag = timetag_latest_by_date(date=date)

    load_src_into_redis(src_dict, date, cate=['deal'], suffix='.origin', client_name=client_name)
    
    for account_name in src_dict[client_name]['accounts_dict'].values():

        print(f'standardize {account_name} deal ...')
        
        if 'client_type_dev' in kf_accounts_dict[account_name]:
            client_type = kf_accounts_dict[account_name]['client_type_dev']
        else:
            client_type = kf_accounts_dict[account_name]['client_type']
            
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.deal.origin')
        if df_origin is None:
            # df_origin = pd.DataFrame()
            df_origin = pd.DataFrame()
            logger.error(f'{account_name} deal file not found, 用 0 代替')
        # print(df_origin)
        df = standardize_deal_minimal(df_origin, date, client_type=client_type)

        df_write_redis(rs, key=f'{date}.stock.{account_name}.deal.minimal', df=df)
        # print(account_name, 'hold:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join(account_name, 'account', 'deal.minimal_{}.csv'.format(date)), 
                   dest_type='dav',
                   index=False)
        
        
def load_std_minimal_deal_position_redis(date):
    load_src_into_redis(src_dict, date, cate=['deal'], suffix='.origin')
    # trade_config = get_trade_config()
    
    for account_name in kf_accounts_dict.keys():
        print(f'standardize {account_name} minimal deal and position ...')
        
        if 'client_type_dev' in kf_accounts_dict[account_name]:
            client_type = kf_accounts_dict[account_name]['client_type_dev']
        else:
            client_type = kf_accounts_dict[account_name]['client_type']
        
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.deal.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
        # print(df_origin)
        try:
            df = standardize_deal_minimal(df_origin, date, client_type=client_type)
        except KeyError as ke:
            df = standardize_deal_minimal(pd.DataFrame(), date, client_type=client_type)
            logger.error(f'KeyError, {account_name} 的 deal 文件的 {ke} key not found, deal 用 0 代替')
        df_write_redis(rs, key=f'{date}.stock.{account_name}.deal.minimal', df=df)
        # print(account_name, 'deal:\n', df.head(2))

        df_pos = get_target_by_date(account_name, date, config=None)
        # try:
        #     flag_new_system = trade_config.get(account_name).get('new_system')
        #     if flag_new_system == True:
        #         df_pos = read_remote_file(path=os.path.join(account_name, 'position', 'position.{}091000.csv'.format(date)),
        #                                 src_type='ninner', header=None, names=['ticker', 'volume'])
        #     else: 
        #         df_pos = read_remote_file(path=os.path.join(account_name, 'position', 'position.{}091000.csv'.format(date)),
        #                                 src_type='inner', header=None, names=['ticker', 'volume'])
        # except FileNotFoundError:
        #     df_pos = pd.DataFrame({'ticker': [1], 'volume': [0]})                                      
        df_write_redis(rs, key=f'{date}.stock.{account_name}.position', df=df_pos)
        # print(account_name, 'position:\n', df_pos.head(2))
        
# def load_morning_hold(date):
#     # 如果程序运行时间晚于当日的 09:25, 跳过读取
#     now_time = datetime.now()
#     if (now_time.strftime('%Y%m%d') == date and now_time.hour <= 9 and now_time.minute < 25) and \
#         (now_time.strftime('%Y%m%d') == date and now_time.hour >= 9 and now_time.minute >= 0) :
#         load_src_into_redis(
#             src_dict,
#             date=date,
#             cate=['hold'],
#             suffix='.origin.091000'
#         )
#         logger.info('loading morning hold files.')
#     # 否则不读取原文件
#     pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
#     for account_name in kf_accounts_dict.keys():
        
#         if 'client_type_dev' in kf_accounts_dict[account_name]:
#             client_type = kf_accounts_dict[account_name]['client_type_dev']
#         else:
#             client_type = kf_accounts_dict[account_name]['client_type']
            
#         # 读取 redis 中原始hold数据, 如果不存在, 读取前一日最新数据, 如果文件不存在, 用 0 替代
#         # TODO
#         # 变成先读取 standard 数据, 没有的话再从上面的逻辑找
#         # hold.origin.091000 只存在于 redis 中 , 不保存文件
#         df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.hold.origin.091000')
        
#         if df_origin is None or df_origin.empty:
#             try:
#                 df = get_latest_hold(account_name=account_name, date=pre_date, config={})

#             except FileNotFoundError:
#                 logger.warning(f'{account_name} 各地没有找到 morning hold_file, 用 0 代替')
#                 df = standardize_hold(pd.DataFrame())
#         else:
#             df = standardize_hold(df_origin, client_type=client_type)

#         # 增加中信融券空头
#         if client_type == 'zhongxin_cats_credit':
#             # print(df)
#             sys.path.insert(0, ZHONGXINAPI_PATH)
#             from zhongxinliangrong import api_func as zhongxin_onsite_api_func
#             sys.path.remove(ZHONGXINAPI_PATH)
#             df = zhongxin_onsite_api_func.merge_zhongxin_credit_account_short_hold(account_name, std_hold=df)

#         # 合并中金多空IMS客户端空头持仓
#         from misc.VARS import Cicc_Client_accounts
#         if account_name in Cicc_Client_accounts:        
#             tmp_df = export_cicc_ims_short_hold(account_name, date)
#             df = _check_short_hold_duplicated(std_hold=df, short_hold=tmp_df)
#             df = pd.concat([df, tmp_df], axis=0, ignore_index=True)
            
#         # 光大多空账户, 根据日初持仓和成交进行调整
#         from misc.VARS import GuangDa_LS_Client_Accounts
#         if account_name in GuangDa_LS_Client_Accounts:
#             sys.path.insert(0, GUANGDAAPI_PATH)
#             import GuangdaLS_Service
#             tmp_df = GuangdaLS_Service.get_ls_latest_hold(account_name)
#             sys.path.remove(GUANGDAAPI_PATH)
#             df = tmp_df
            
#         df['available_volume'] = df['volume']
#         # write_file(df, 
#         #            file_type='xls',
#         #            dest_path=os.path.join(account_name, 'hold', 'hold_{}091000.xls'.format(date)), 
#         #            dest_type='dav',
#         #            index=False)
#         df_write_redis(rs, key=f'{date}.stock.{account_name}.hold.091000', df=df)
            
def load_after_market_data(date):
    write_file_time_tag = timetag_latest_by_date(date=date)

    # trade_config = get_trade_config()
    load_src_into_redis(
        src_dict,
        date=date,
        cate='all',
        suffix='.origin'
    )
    logger.info('loading after market account files.')
    
    for account_name in kf_accounts_dict.keys():
    # for account_name in ['宽辅思贤专享中性1号_华鑫']:
        print(f'standardize {account_name} data ...')
        
        if 'client_type_dev' in kf_accounts_dict[account_name]:
            client_type = kf_accounts_dict[account_name]['client_type_dev']
        else:
            client_type = kf_accounts_dict[account_name]['client_type']
            
        # hold, deal
        # cates_dict = {
        #     'hold' : standardize_hold,
        #     'deal' : standardize_deal,
        # }
        # for cate in cates_dict:
        
        # hold file
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.hold.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
            logger.error(f'{account_name} hold data not found, 用 0 代替')
        write_file(df_origin, 
                   file_type='csv', 
                   dest_path=os.path.join(account_name, 'hold', 'hold.origin_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False
                   )
        # print(df_origin)
        df = standardize_hold(df_origin, client_type=client_type)
        # 增加中信融券空头
        # if client_type == 'zhongxin_cats_credit':
        #     sys.path.insert(0, ZHONGXINAPI_PATH)
        #     from zhongxinliangrong import api_func as zhongxin_onsite_api_func
        #     sys.path.remove(ZHONGXINAPI_PATH)
        #     df = zhongxin_onsite_api_func.merge_zhongxin_credit_account_short_hold(account_name, std_hold=df)
        
        # 合并中金多空IMS客户端空头持仓
        # from misc.VARS import Cicc_Client_Accounts
        # if account_name in Cicc_Client_Accounts.keys():        
        #     tmp_df = export_cicc_ims_short_hold(account_name, date)
        #     df = _check_short_hold_duplicated(std_hold=df, short_hold=tmp_df)
        #     df = pd.concat([df, tmp_df], axis=0, ignore_index=True)
            
        # # 光大多空账户, 根据日初持仓和成交进行调整
        # from misc.VARS import GuangDa_LS_Client_Accounts
        # if account_name in GuangDa_LS_Client_Accounts:
        #     sys.path.insert(0, GUANGDAAPI_PATH)
        #     import GuangdaLS_Service
        #     tmp_df = GuangdaLS_Service.get_ls_latest_hold(account_name)
        #     sys.path.remove(GUANGDAAPI_PATH)
        #     df = tmp_df
            
        df_write_redis(rs, key=f'{date}.stock.{account_name}.hold', df=df)
        print(account_name, 'hold:\n', df.head(2))
        # 特殊的, 保存hold 文件为最新时间戳
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join(account_name, 'hold', 'hold_{}.csv'.format(write_file_time_tag)), 
                   dest_type='dav',
                   index=False)
        
        # deal file        
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.deal.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
        write_file(df_origin, 
                   file_type='csv', 
                   dest_path=os.path.join(account_name, 'account', 'deal.origin_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False
                   )
        # print(df_origin)
        df = standardize_deal(df_origin, date, client_type=client_type)
        df_write_redis(rs, key=f'{date}.stock.{account_name}.deal', df=df)
        print(account_name, 'deal:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join(account_name, 'account', 'deal_{}.csv'.format(date)), 
                   dest_type='dav',
                   index=False)

        # order file
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.order.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
        write_file(df_origin, 
                   file_type='csv', 
                   dest_path=os.path.join(account_name, 'account', 'execution.origin_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False
                   )
        # print(df_origin)
        df = standardize_order(df_origin, date, client_type=client_type)
        df_write_redis(rs, key=f'{date}.stock.{account_name}.order', df=df)
        print(account_name, 'order:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join(account_name, 'account', 'execution_{}.csv'.format(date)), 
                   dest_type='dav',
                   index=False)
        
        
        # account file
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.account.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
        write_file(df_origin, 
                   file_type='csv', 
                   dest_path=os.path.join(account_name, 'account', 'account.origin_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False
                   )
        # print(df_origin)
        account_type =  kf_accounts_dict[account_name]['account_type']
        if account_type == 'marginaccount':
            df = standardize_marginaccount(df_origin, date, client_type=client_type)
        else:
            df = standardize_stockaccount(df_origin, date, client_type=client_type)
        
        df_write_redis(rs, key=f'{date}.stock.{account_name}.accountinfo', df=df)
        print(account_name, 'accountinfo:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join(account_name, 'account', 'accountinfo_{}.csv'.format(date)), 
                   dest_type='dav',
                   index=False)

        df_pos = get_target_by_date(account_name, date, config=None)

        # try:
        #     flag_new_system = trade_config.get(account_name).get('new_system')
        #     if flag_new_system == True:
        #         df_pos = read_remote_file(path=os.path.join(account_name, 'position', 'position.{}091000.csv'.format(date)),
        #                                 src_type='ninner', header=None, names=['ticker', 'volume'])
        #     else: 
        #         df_pos = read_remote_file(path=os.path.join(account_name, 'position', 'position.{}091000.csv'.format(date)),
        #                                 src_type='inner', header=None, names=['ticker', 'volume'])
        # except FileNotFoundError:
        #     df_pos = pd.DataFrame({'ticker': [1], 'volume': [0]})                                      
        df_write_redis(rs, key=f'{date}.stock.{account_name}.position', df=df_pos)
        print(account_name, 'position:\n', df_pos.head(2))


def load_account_info_data(date):
    load_src_into_redis(
        src_dict,
        date=date,
        cate='account',
        suffix='.origin'
    )
    logger.info('loading account files.')
    
    for account_name in kf_accounts_dict.keys():
        print(f'standardize {account_name} data ...')
        
        if 'client_type_dev' in kf_accounts_dict[account_name]:
            client_type = kf_accounts_dict[account_name]['client_type_dev']
        else:
            client_type = kf_accounts_dict[account_name]['client_type']
            
        # account file
        df_origin = df_read_redis(rs, f'{date}.stock.{account_name}.account.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
        write_file(df_origin, 
                   file_type='csv', 
                   dest_path=os.path.join(account_name, 'account', 'account.origin_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False
                   )
        # print(df_origin)
        account_type =  kf_accounts_dict[account_name]['account_type']
        if account_type == 'marginaccount':
            df = standardize_marginaccount(df_origin, date, client_type=client_type)
        else:
            df = standardize_stockaccount(df_origin, date, client_type=client_type)
        
        df_write_redis(rs, key=f'{date}.stock.{account_name}.accountinfo', df=df)
        # print(account_name, 'accountinfo:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join(account_name, 'account', 'accountinfo_{}.csv'.format(date)), 
                   dest_type='dav',
                   index=False)
        



def load_std_deal_future_redis(date):
    account_type = 'future'
    
    # trade_date = utils.nowtime_to_tradedate(bytime='210000')
    # write_file_time_tag = timetag_latest_by_date(date=date)
    
    load_src_into_redis(src_dict_future, date, cate=['deal', ], suffix='.origin', account_type=account_type)
    # trade_config = get_trade_config()
    
    for account_name in kf_futures_accounts.keys():
        # print(f'standardize {account_name} data ...')
        
        # if 'client_type_dev' in kf_accounts_dict[account_name]:
        #     client_type = kf_accounts_dict[account_name]['client_type_dev']
        # else:
            
        client_type = kf_futures_accounts[account_name]['client_type']
            
        # hold, deal
        # cates_dict = {
        #     'hold' : standardize_hold,
        #     'deal' : standardize_deal,
        # }
        # for cate in cates_dict:
        df_origin = df_read_redis(rs, f'{date}.{account_type}.{account_name}.deal.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
            logger.warning(f'{account_name} deal data not found, 用 0 代替')
        # print(df_origin)
        df = standardize_deal_future(df_origin, date, client_type=client_type)

        df_write_redis(rs, key=f'{date}.{account_type}.{account_name}.deal', df=df)
        


# =================================== futureStra
def load_std_futureStra_future_hold_redis(date):
    write_file_time_tag = timetag_latest_by_date(date=date)
    account_type = 'future'

    load_src_into_redis(src_dict_future, date, cate=['hold'], suffix='.origin', account_type=account_type)

    all_accounts = {**futureStra_account_dict, **futureIX_account_dict}


    for account_name in all_accounts.keys():
        print(f'standardize {account_name} data ...')
        
        client_type = all_accounts[account_name]['client_type']
        
        df_origin = df_read_redis(rs, f'{date}.{account_type}.{account_name}.hold.origin')

        if df_origin is None:
            df_origin = pd.DataFrame()
            logger.error(f'{account_name} hold data not found, 用 0 代替')

        df = standardize_hold_future(df_origin, client_type=client_type)
        
        df_write_redis(rs, key=f'{date}.{account_type}.{account_name}.hold', df=df)
        
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join('期货策略', account_name, 'hold', 'hold_{}.csv'.format(write_file_time_tag)), 
                   dest_type='dav',
                   index=False)


def load_std_futureStra_future_deal_redis(date):
    account_type = 'future'
    
    # write_file_time_tag = timetag_latest_by_date(date=date)
    
    load_src_into_redis(src_dict_future, date, cate=['deal', ], suffix='.origin', account_type=account_type)
    # trade_config = get_trade_config()
    
    # 用了这个之后就只能更新最新数据, 不匹配历史数据
    # trade_date = utils.nowtime_to_tradedate(bytime='210000')
    
    all_accounts = {**futureStra_account_dict, **futureIX_account_dict}
    
    for account_name in all_accounts.keys():
        # print(f'standardize {account_name} data ...')
            
        client_type = all_accounts[account_name]['client_type']
            
        # for cate in cates_dict:
        df_origin = df_read_redis(rs, f'{date}.{account_type}.{account_name}.deal.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
            logger.warning(f'{account_name} deal data not found, 用 0 代替')
        # print(df_origin)
        df = standardize_deal_future(df_origin, date, client_type=client_type)

        df_write_redis(rs, key=f'{date}.{account_type}.{account_name}.deal', df=df)
        

def load_std_futureStra_future_data_after_market(date):
    account_type = 'future'
    write_file_time_tag = timetag_latest_by_date(date=date)
    # trade_date = utils.nowtime_to_tradedate(bytime='210000')
    
    # write_file_time_tag = timetag_latest_by_date(date=date)
    all_accounts = {**futureStra_account_dict, **futureIX_account_dict}
    
    load_src_into_redis(src_dict_future, date, cate=['hold', 'deal', 'order', 'account'], suffix='.origin', account_type=account_type)
    # trade_config = get_trade_config()
    
    for account_name in all_accounts.keys():
        # print(f'standardize {account_name} data ...')
            
        client_type = all_accounts[account_name]['client_type']
        
        # hold file
        df_origin = df_read_redis(rs, f'{date}.{account_type}.{account_name}.hold.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
            logger.warning(f'{account_name} hold data not found, 用 0 代替')
        write_file(df_origin, 
                   file_type='csv', 
                   dest_path=os.path.join('期货策略', account_name, 'hold', 'hold.origin_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False
                   )
        # print(df_origin)
        df = standardize_hold_future(df_origin, client_type=client_type)
        df_write_redis(rs, key=f'{date}.{account_type}.{account_name}.hold', df=df)
        print(account_name, 'hold:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join('期货策略', account_name, 'hold', 'hold_{}.csv'.format(write_file_time_tag)),
                   dest_type='dav',
                   index=False)
        
        # for cate in cates_dict:
        # deal file
        df_origin = df_read_redis(rs, f'{date}.{account_type}.{account_name}.deal.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
            logger.warning(f'{account_name} deal data not found, 用 0 代替')
        write_file(df_origin, 
                   file_type='csv', 
                   dest_path=os.path.join('期货策略', account_name, 'account', 'deal.origin_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False
                   )
        # print(df_origin)
        df = standardize_deal_future(df_origin, date, client_type=client_type)
        df_write_redis(rs, key=f'{date}.{account_type}.{account_name}.deal', df=df)
        print(account_name, 'deal:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join('期货策略', account_name, 'account', 'deal_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False)

        
        # order file
        df_origin = df_read_redis(rs, f'{date}.{account_type}.{account_name}.order.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
            logger.warning(f'{account_name} order data not found, 用 0 代替')
        write_file(df_origin, 
                   file_type='csv', 
                   dest_path=os.path.join('期货策略', account_name, 'account', 'execution.origin_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False
                   )
        # print(df_origin)
        df = standardize_order_future(df_origin, date, client_type=client_type)
        df_write_redis(rs, key=f'{date}.{account_type}.{account_name}.order', df=df)
        print(account_name, 'order:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join('期货策略', account_name, 'account', 'execution_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False)
        

        # account file
        df_origin = df_read_redis(rs, f'{date}.{account_type}.{account_name}.account.origin')
        if df_origin is None:
            df_origin = pd.DataFrame()
            logger.warning(f'{account_name} account data not found, 用 0 代替')
        write_file(df_origin, 
                   file_type='csw', 
                   dest_path=os.path.join('期货策略', account_name, 'account', 'account.origin_{}.csv'.format(date)),
                   dest_type='dav',
                   index=False
                   )

        # TODO 
        df = standardize_account_future(df_origin, date, client_type=client_type)
        
        df_write_redis(rs, key=f'{date}.{account_type}.{account_name}.accountinfo', df=df)
        print(account_name, 'accountinfo:\n', df.head(2))
        write_file(df, 
                   file_type='csv',
                   dest_path=os.path.join('期货策略', account_name, 'account', 'accountinfo_{}.csv'.format(date)), 
                   dest_type='dav',
                   index=False)
            
        
            
        # df_write_redis(rs, key=f'{date}.stock.{account_name}.hold', df=df)
        # print(account_name, 'hold:\n', df.head(2))
        # # 特殊的, 保存hold 文件为最新时间戳
        # write_file(df, 
        #            file_type='xls',
        #            dest_path=os.path.join(account_name, 'hold', 'hold_{}.xls'.format(write_file_time_tag)), 
        #            dest_type='dav',
        #            index=False)
        

# ===================================
# config file
src_dict = parse_account_source_config(config_sheet='config_normal')
src_dict_future = parse_account_source_config(config_sheet='config_future_account')
# pprint(src_dict)
# backup situation
# src_dict = parse_account_source_config(config_sheet='config_bak')
