import paramiko
import datetime

def ftpUploadRemote(work_date):
    target_path = "/list/10.99.9.109_sftp/daily_before/target/{}".format(work_date)
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        private_key = paramiko.RSAKey.from_private_key_file('/home/<USER>/.ssh/chaolz2go')
        ssh.connect(hostname='************', port=16888, username='chaolz2go', pkey=private_key)
        sftp = ssh.open_sftp()
        # target_dir = os.path.dirname(target_path)
        try:
            sftp.stat(target_path)
        except FileNotFoundError:
            sftp.mkdir(target_path)
        # file_to_upload_t0 = "zhongtai_t0_target_{}.csv".format(work_date)
        # file_to_upload_vwap = "zhongtai_vwap_target_{}.csv".format(work_date)
        file_to_upload_vwap = "zhongtai_t0_target_{}.csv".format(work_date)
        # sftp.put(file_to_upload_t0, target_path+"/"+file_to_upload_t0)
        sftp.put(file_to_upload_vwap, target_path+"/"+file_to_upload_vwap)
        sftp.close()
        ssh.close()
    except Exception as e:
        print(f"连接失败: {e}")
        return False
    return True

    
if __name__ == "__main__":
    work_date=None
    # work_date='20250522'
    if work_date is None:
        work_date = datetime.datetime.now().strftime("%Y%m%d")
    ftpUploadRemote(work_date)