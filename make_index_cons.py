
from misc.tools import get_last_month_last_trading_day, get_stock_close, get_index_constitutions_last_month

import pandas as pd
import numpy as np
file = f'cons_1000_30M.xlsx'

total_value = 30000000

for date in [
    '20240101',
    '20240201',    
    '20240301',    
    '20240401',    
    '20240501',    
    '20240601',    
    '20240701',    
    '20240801',    
    '20240901',    
    '20241001',    
    '20241101',    
    '20241201',    
    '20250101',    
    '20250201',    
    '20250301',    
    '20250401',    
    '20250501',    
    '20250601',    
    '20250701',    
]:
# date = '20240201'
    df = get_index_constitutions_last_month('000852', date)
    pre_date = get_last_month_last_trading_day(date)
    close = get_stock_close(pre_date)
    close['ticker'] = close['ticker'].astype(str).str.zfill(6)
    df = df.merge(close, on='ticker', how='left')
    df['volume'] = np.floor(df['WEIGHT']/100 * total_value / df['close'] /100).astype(int) * 100
    
    
    print(df)

    try:
        with pd.ExcelWriter(file, engine='openpyxl', mode='a') as writer:
            df.to_excel(writer, sheet_name=date, index=False)
    except FileNotFoundError:
        with pd.ExcelWriter(file, engine='openpyxl', mode='w') as writer:
            df.to_excel(writer, sheet_name=date, index=False)

    # print(close)
