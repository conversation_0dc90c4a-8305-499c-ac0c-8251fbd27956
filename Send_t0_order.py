# -*- coding: utf-8 -*-
"""
Created on Fri Jul 15 14:43:00 2022

@author: zzw

生成 T0 母单
"""

import os, sys
import time
import datetime
from loguru import logger
import pandas as pd
import numpy as np
from tqdm import tqdm
import sshtunnel
import json
import tempfile

# 将项目路径添加到模块搜索路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.abspath(os.path.join(current_dir, ".."))  # 通过..返回上一级目录
sys.path.append(project_dir)


from misc.Readstockfile import read_remote_file, write_file
# from misc.ssh_conn import sftp_clent_work
# from misc.zzw_tun import tun_zhongxinapi_dict, tun_zhongxinliangrong_dict
from data_utils.trading_calendar import Calendar
from misc.tools import get_stock_close, find_latest_remote_file_on_date, get_account_sftp
from misc.ssh_conn import sftp_clent_inner, sftp_clent_dav
from misc.utils import nowtime_to_tradedate, compare_diff
from misc import tools
from misc import Account
from misc.feishu import msgBot


from misc import email
from misc import utils
from misc.Readstockfile import read_file, append_dbf
# from accounts_config.kf_accounts_basic import get_account_sftp
from misc.convert_orders import convert_gtrade_t0_orders, convert_kafang_atx_T0_scan_orders, convert_cats_generic_t0_orders


execute_email_host = 'imap.feishu.cn'
execute_email_addr = '<EMAIL>'
execute_email_password = 'V4VwSVVGCK8xanGT'


def read_zheshang_t0_ticker_list_file(trade_date):
    search_start = Calendar.last_trading_day(trade_date).strftime('%Y%m%d')
    search_end = trade_date
    today = nowtime_to_tradedate(bytime='235959')
    files_path = email.download_excel_attachments(
        server=execute_email_host,
        email_user=execute_email_addr,
        email_pass=execute_email_password,
        folder='浙商证券皓兴T0券池',
        start_date=search_start,
        end_date=search_end,
        title_reg=f'{trade_date}',    # f'{trade_date} - Haoxing T0股票票池',
        attachment_reg='T0_StockList.csv',
    )
    if len(files_path) == 0:
        raise FileNotFoundError(f' {trade_date} 没有找到附件')
    elif len(files_path) > 1:
        files_path.sort(reverse=True)
        logger.error(f'{trade_date} 找到多个邮件附件')
        print(files_path)
    file_path = files_path[0]
    df = read_file(file_path, src_type='local')
    os.remove(file_path)
    return df   # ['symbol', 'lable_date']

# def read_feitu_t0_ticker_list_file(account_name, trade_date):
#     file = f'{trade_date}.csv'
#     path = os.path.join(account_name, 'account', file)
#     df = read_remote_file(path, src_type='dav')
#     return df[['股票代码']].rename(columns={'股票代码':'symbol'})




# def generate_atx_t0_order(account_name, trade_date, hold, ticker_list, **kwargs):
#     account_cn = kwargs.get('account_cn')
#     algorithm = kwargs.get('algorithm')
#     start_time = kwargs.get('start_time', '093000')
#     default_endtime = kwargs.get('default_endtime', '145700')
#     preference = kwargs.get('preference', '保守')
#     now_str_short = datetime.datetime.now().strftime('%H%M')
#     algorithm_params = kwargs.get('algorithm_params', {})
#     algorithm_params.update({'风险偏好': preference, '篮子编号': now_str_short})
#     algorithm_params = ':'.join([f'{key}={value}' for key, value in algorithm_params.items()])
    
#     target = tools.get_target_by_date(account_name, trade_date)
#     t0 = pd.merge(hold, target, on='ticker', how='outer', suffixes=('', '_target'))
#     t0 = t0.fillna(0)
#     t0['t0_volume'] = t0[['available_volume', 'volume_target']].min(axis=1)
#     t0['t0_volume'] = t0['t0_volume'].div(100).astype('int').mul(100)
#     # 筛选 688 股票股数小于 200 的不要
#     t0['t0_volume'] = np.where((t0['ticker'] >= 688000) & (t0['t0_volume'] < 200), 0, t0['t0_volume'])
#     t0 = t0[t0['t0_volume'] > 0][['ticker', 't0_volume']]
#     # t0['volume'] = t0['volume'].astype(int)
#     # t0['available_volume'] = t0['volume']
    
    
#     t0 = t0[t0['ticker'].astype(int).isin(ticker_list['symbol'])]
#     cols = [
#         '算法类型',
#         '账户名称',
#         '算法实例',
#         '证券代码',
#         '任务数量',
#         '买入方向',
#         '卖出方向',
#         '开始时间',
#         '结束时间',
#         '涨跌停设置',
#         '过期后执行',
#         '其他参数',
#         '交易市场',
#     ]
#     order = pd.DataFrame(columns=cols)
#     order['证券代码'] = t0['ticker'].apply(utils.symbol_with_market).tolist()
#     order['任务数量'] = t0['t0_volume'].tolist()
#     order['买入方向'] = '买入'
#     order['卖出方向'] = '卖出'
#     order['算法类型'] = 'T0'
#     order['账户名称'] = account_cn
#     order['算法实例'] = algorithm
#     order['开始时间'] = '{}T{}000'.format(trade_date, start_time)
#     order['结束时间'] = '{}T{}000'.format(trade_date, default_endtime)
#     order['涨跌停设置'] = '涨停不买跌停不卖'
#     order['过期后执行'] = '否'
#     order['其他参数'] = algorithm_params
#     order['交易市场'] = order['证券代码'].apply(utils.symbol_to_exchange_cn)
#     return order
    
# def send_order(account_name, order, **kwargs):
#     date = datetime.datetime.now().strftime('%Y%m%d')
#     order_dir = 'order'
#     order_file_suffix = kwargs.get('order_file_suffix', '')
#     order_file = f'{account_name}_{date}_T0{order_file_suffix}.csv'
#     order_path = os.path.join(account_name, order_dir, order_file)
    
#     # sftp_type = get_account_sftp(account_name)['ftp_type']
#     write_file(order, file_type='csv', dest_path=order_path, dest_type='dav', index=False)
    
    
# send order
def send_scan_order(account_name, order, **config):
    suffix = datetime.datetime.now().strftime('%H%M%S')
    
    if config.get('send_type') == 'newfile':
        
        dest_type = config.get('dest_type')
        dest_dir = config.get('dest_dir')
        order_filename = config.get('order_filename').format(account=account_name, date=trade_date, suffix=suffix)
        dest_path = os.path.join(dest_dir, order_filename)

        extension = os.path.splitext(order_filename)[-1].lower().replace('.', '')
        # backup csv file
        write_file(order, file_type='csv', dest_path=os.path.join(account_name, 'order', f'T0_{trade_date}_{suffix}.csv'), dest_type='dav', index=False)
        # send real order
        write_file(order, file_type=extension, dest_path=dest_path, dest_type=dest_type, put_confirm=False, index=False)
        logger.info(f'{account_name} send order file sent: {dest_path}')
    
    elif config.get('send_type') == 'kf_dbf':
        
        dest_type = config.get('dest_type')
        dest_dir = config.get('dest_dir')
        order_filename = config.get('dbf_filename').format(date=trade_date)
        dest_path = os.path.join(dest_dir, order_filename)

        # backup csv file
        write_file(order, file_type='csv', dest_path=os.path.join(account_name, 'order', f'T0_{trade_date}_{suffix}.csv'), dest_type='dav', index=False)
        # send real order
        append_dbf(order, dest_type=dest_type, dest_path=dest_path, dbf_fields_list=None)
        logger.info(f'{account_name} send order file sent: {dest_path}')
    

# generate t0 order
def generate_t0_order(account_name, trade_date):
    # config
    order_args = account_args[account_name]
    
    # hold
    hold = tools.get_latest_hold(account_name, trade_date, config={})

    # target 
    target = tools.get_target_by_date(account_name, trade_date)
    
    # calculate t0 volume
    t0 = tools.calc_t0_volume(hold=hold, target=target)
    t0 = t0[t0['t0_volume'] != 0]
    
    # convert t0 orders
    if order_args['order_type'] == 'gtrade_t0':
        order = convert_gtrade_t0_orders(t0, **order_args)
    elif order_args['order_type'] == 'kafang_t0':
        order = convert_kafang_atx_T0_scan_orders(t0, **order_args)
    elif order_args['order_type'] == 'cats_t0':
        order = convert_cats_generic_t0_orders(t0, **order_args)
        
    else:
        logger.warning(f'{account_name} {trade_date} T0 order not converted')
        # continue
    
    print(f'{account_name} {trade_date} T0 order:')
    print(order.head(2))
    print(order.shape)
    
    return order
    

# # generate gtrade t0 order
# def generate_gtrade_t0_order(account_name, trade_date):
    
    
if __name__ == '__main__':
    trade_date = nowtime_to_tradedate(bytime='235959')
    
    account_args = {
        # '远航安心中性2号_浙商' : {
        #     'ClientName' : '宽辅远航安心市场中性2号',
        #     'start_time' : '093000',
        #     'order_type' : 'kafang_t0',
        #     'algorithm'  : 'HX_SMART_T0',
        #     'AlgoParam'  : {'buyside': 1, 'sellside': 2},

        #     'send_type' : 'kf_dbf',
        #     'dest_type' : 'trade',
        #     'dest_dir' : '交易软件/浙商证券经纪户卡方交易端_金桥/OrderScan',
        #     'dbf_filename' : 'OrderAlgo_{date}.dbf',
        # },

        # '远航安心中性6号_国君' : {
        #     'ClientName' : '宽辅远航安心市场中性6号私募（DMA）',
        #     'start_time' : '093000',
        #     'order_type' : 'kafang_t0',
        #     'algorithm'  : 'JINGLE_T0',
        #     'AlgoParam'  : {'buyside': 1, 'sellside': 2},
            
        #     'send_type' : 'kf_dbf',
        #     'dest_type' : 'trade',
        #     'dest_dir' : '交易扫单监控目录/国君dma卡方',
        #     'dbf_filename' : 'OrderAlgo_{date}.dbf',
        # },

        '宽辅臻好专享_国君' : {
            'ClientName' : '宽辅臻好专享证券',
            'start_time' : '093000',
            'order_type' : 'kafang_t0',
            'algorithm'  : 'ld_t0',
            'AlgoParam'  : {'buyside': 1, 'sellside': 2},
            
            'send_type' : 'kf_dbf',
            'dest_type' : 'trade',
            'dest_dir' : '交易扫单监控目录/国君经纪户卡方ATX',
            'dbf_filename' : 'OrderAlgo_{date}.dbf',
        },
        '宽辅1000指增1号_国君' : {
            'ClientName' : '宽辅中证1000指数增强1号证券',
            'start_time' : '093000',
            'order_type' : 'kafang_t0',
            'algorithm'  : 'ld_t0',
            'AlgoParam'  : {'buyside': 1, 'sellside': 2},
            
            'send_type' : 'kf_dbf',
            'dest_type' : 'trade',
            'dest_dir' : '交易扫单监控目录/国君经纪户卡方ATX',
            'dbf_filename' : 'OrderAlgo_{date}.dbf',
        },
        '远航安心中性5号_华金' : {
            'ClientName' : '********',
            'start_time' : '093000',
            'order_type' : 'kafang_t0',
            'algorithm'  : 'kf_t0',
            'AlgoParam'  : {'buyside': 1, 'sellside': 2},
            
            'send_type' : 'kf_dbf',
            'dest_type' : 'trade',
            'dest_dir' : '交易扫单监控目录/华金证券卡方ATX',
            'dbf_filename' : 'OrderAlgo_{date}.dbf',
        },


        
        # '远航安心中性1号' : {
        #     'order_type' : 'gtrade_t0',
        #     'account_id' : '**************',
        #     'strategy_type' : 'YR_T0',
        #     # 'strategy_paras' : 'BN=a;BTI=1;STI=2;MAA={fund}'.format(fund='2.5e+06'),
        #     'strategy_paras' : 'BN=a;BTI=1;STI=2;MAA={fund}'.format(fund='2e+06'),
        #     'no_trade_extreme' : '1',
            
        #     'send_type' : 'newfile',
        #     'dest_type' : 'trade',
        #     'dest_dir' : '交易扫单监控目录/国信Gtrade',
        #     'order_filename' : '{account}.{date}.T0_交易{suffix}.csv',
        # },
        
        '宽辅泓涛专享1号' : {
            'order_type' : 'gtrade_t0',
            'account_id' : '**************',
            'strategy_type' : 'YR_T0',
            # 'strategy_paras' : 'BN=b;BTI=1;STI=2;MAA={fund}'.format(fund='7.5e+06'),
            'strategy_paras' : 'BN=b;BTI=1;STI=2;MAA={fund}'.format(fund='4e+06'),
            'no_trade_extreme' : '1',
            
            'send_type' : 'newfile',
            'dest_type' : 'trade',
            'dest_dir' : '交易扫单监控目录/国信Gtrade',
            'order_filename' : '{account}.{date}.T0_交易{suffix}.csv',
        },
    
        # '宽辅专享1号': {
        #     'order_type' : 'gtrade_t0',
        #     'account_id' : '**************',
        #     'strategy_type' : 'YR_T0',
        #     # 'strategy_paras' : 'BN=c;BTI=1;STI=2;MAA={fund}'.format(fund='6e+06'),
        #     'strategy_paras' : 'BN=c;BTI=1;STI=2;MAA={fund}'.format(fund='2e+06'),
        #     'no_trade_extreme' : '1',
            
        #     'send_type' : 'newfile',
        #     'dest_type' : 'trade',
        #     'dest_dir' : '交易扫单监控目录/国信Gtrade',
        #     'order_filename' : '{account}.{date}.T0_交易{suffix}.csv',
        # },
        'test' : {
            'order_type' : 'cats_t0',
            'tradingaccount_type' : 'S0',
            'tradingaccount' : '********',
            'algorithm' : 'KF_DTrade_Plus',    # 'HX_DTrade',  'HX_DTrade_Stable',  'KF_DTrade', 'KF_DTrade_Plus'
            'start_time' : '093000',
            'default_endtime' : '145500',
            'arg_remark' : None,
            'arg_extra_param' : None,
            
            
            'send_type' : 'newfile',
            'dest_type' : 'work',
            'dest_dir' : '../交易测试/中信证券CATS测试交易端/UniversalInstruction/InPut',
            # 'dest_dir' : '../..',
            'order_filename' : '{account}.{date}.T0_交易{suffix}.csv',
        },
        '宽辅泓涛专享2号_中信' : {
            'order_type' : 'cats_t0',
            'tradingaccount_type' : 'SHHTS0',
            'tradingaccount' : '4104866',
            'algorithm' : 'KF_DTrade_Plus',    # 'HX_DTrade',  'HX_DTrade_Stable',  'KF_DTrade', 'KF_DTrade_Plus'
            'start_time' : '093000',
            'default_endtime' : '145500',
            'arg_remark' : None,
            'arg_extra_param' : None,
            
            'send_type' : 'newfile',
            'dest_type' : 'trade',
            'dest_dir' : '交易软件/中信CATS_场内/UniversalInstruction/InPut',
            # 'dest_dir' : '../..',
            'order_filename' : '{account}.{date}.T0_交易{suffix}.csv',
        },
        '宽辅泓涛专享1号_中信' : {
            'order_type' : 'cats_t0',
            'tradingaccount_type' : 'SHHTS0',
            'tradingaccount' : '4102976',
            'algorithm' : 'HX_DTrade_Stable',    # 'HX_DTrade',  'HX_DTrade_Stable',  'KF_DTrade', 'KF_DTrade_Plus'
            'start_time' : '093000',
            'default_endtime' : '145500',
            'arg_remark' : '',
            'arg_extra_param' : '',
            
            'send_type' : 'newfile',
            'dest_type' : 'trade',
            'dest_dir' : '交易软件/中信CATS_场内/UniversalInstruction/InPut',
            # 'dest_dir' : '../..',
            'order_filename' : '{account}.{date}.T0_交易{suffix}.csv',
        },
    }
    

    for account_name in account_args.keys():
        if account_name not in [
            '宽辅泓涛专享1号',
            '宽辅臻好专享_国君',
            '宽辅1000指增1号_国君',
            '宽辅泓涛专享2号_中信',
            '宽辅泓涛专享1号_中信',
            # '远航安心中性5号_华金',
            # 'test',
        ]:
            continue
        
        # config
        order_args = account_args[account_name]

        # generate t0 order
        order = generate_t0_order(account_name, trade_date)
        
        # send_order(account_name, order, **order_args)
        confirm_flag = input(f'\n       {account_name} Confirm T0 order? [Y/N] \n\n         ').lower()
        if confirm_flag == 'y':
            send_scan_order(account_name, order, **order_args)
        else:
            logger.warning(f'{account_name} {trade_date} T0 order not sent')
            # time.sleep(1)
            


        
    # # 国信 t0 合并扫单
    # gtrade_sending_accounts = []
    # total_t0_order = []
    # for account_name in account_args.keys():
    #     if account_name not in [
    #         '远航安心中性1号',
    #         '宽辅泓涛专享1号',
    #         # '宽辅专享1号',
    #     ]:
    #         continue

    #     # config
    #     order_args = account_args[account_name]

    #     # generate t0 order
    #     tmp_order = generate_t0_order(account_name, trade_date)

    #     confirm_flag = input(f'\n       {account_name} Confirm T0 order? [Y/N] \n\n         ').lower()
    #     if confirm_flag == 'y':
    #         gtrade_sending_accounts.append(account_name)
    #         total_t0_order.append(tmp_order)

    # if len(gtrade_sending_accounts) > 0:
    #     account_name = gtrade_sending_accounts[0]
    #     order_args = account_args[gtrade_sending_accounts[0]]
    #     total_t0_order = pd.concat(total_t0_order, ignore_index=True)

    #     send_scan_order(account_name, total_t0_order, **order_args)
    #     logger.info(f'国信 t0 合并扫单 : {", ".join(gtrade_sending_accounts)} \n                  {trade_date} 发送成功')