#%%
from tempfile import NamedTemporaryFile
import os, sys
import pandas as pd
from datetime import datetime
import ast
import copy
from loguru import logger

import openpyxl
from openpyxl.formula.translate import Translator
# from openpyxl.styles import NamedStyle
from openpyxl.utils import get_column_letter


from misc.Readstockfile import read_remote_file, write_file
from misc.utils import re_find
from misc.ssh_conn import sftp_clent_dav
from accounts_config.kf_accounts_basic import product_account_dict, kf_accounts_dict, kf_futures_accounts
#%%
# date = '********'
if len(sys.argv) > 1:
    date = str(sys.argv[1])
else:
    date = datetime.now().strftime('%Y%m%d')
#%%

pnl_config_file = 'others/pnl_update_config.xlsx'
pnl_config = read_remote_file(pnl_config_file, src_type='dav')
pnl_config = pnl_config.fillna(0)
pnl_config = pnl_config[pnl_config['active'] != 'N']

filter_stock_mask = (pnl_config['类型'] == '股票') & (~pnl_config['account_name'].isin(kf_accounts_dict.keys())) & (~pnl_config['note'].isin(['必须']))
filter_futures_mask = (pnl_config['类型'] == '期货') & (~pnl_config['account_name'].isin(kf_futures_accounts.keys())) & (~pnl_config['note'].isin(['必须']))

if filter_stock_mask.any():
    logger.warning(f"\n股票账户已过滤: \n{pnl_config['account_name'][filter_stock_mask]}")
# pnl_config = pnl_config[~filter_stock_mask]

if filter_futures_mask.any():
    logger.warning(f"\n期货账户已过滤: \n{pnl_config['account_name'][filter_futures_mask]}")
pnl_config = pnl_config[~(filter_futures_mask | filter_stock_mask)]

for account_name in kf_accounts_dict.keys():
    if account_name not in pnl_config[ pnl_config['类型'] == '股票']['account_name'].unique():
        logger.error(f'股票账户 {account_name} 在 pnl_config 中不存在')
for account_name in kf_futures_accounts.keys():
    if account_name not in pnl_config[ pnl_config['类型'] == '期货']['account_name'].unique():
        logger.error(f'期货账户 {account_name} 在 pnl_config 中不存在')

print(pnl_config.shape)
# filter out inactive account
# valid_stock_accounts = [(kf_futures_accounts[key]['product_name'], key) for key in kf_futures_accounts.keys()]
# valid_futures_accounts = [( kf_futures_accounts[key]['product_name'], key) for key in kf_futures_accounts.keys()]
# valid_accounts = valid_stock_accounts + valid_futures_accounts
# valid_accounts = pd.DataFrame(valid_accounts, columns=['product_name', 'account_name'])
# valid_accounts['tmp_name'] = valid_accounts['product_name'] + '_' + valid_accounts['account_name']

# pnl_config['tmp_name'] = pnl_config['sheet_name'] + '_' + pnl_config['account_name']
# pnl_config = pnl_config[pnl_config['tmp_name'].isin(valid_accounts['tmp_name'])]
# pnl_config.drop(columns=['tmp_name'], inplace=True)
# print(pnl_config.tail())
# print(pnl_config.shape)


product_account_dict.pop('模拟股票')
    

pnl_file = '远航安心中性1号/pnl/宽辅系列{}更新前.xlsx'.format(date)
local_pnl_file = 'tmp/tmp_pnl.xlsx'
# dav_pnl_file = '远航安心中性1号/pnl/宽辅系列{}更新前.xlsx'.format(date)
# local_pnl_file = 'tmp/tmp_pnl.xlsx'


def test_update(local_pnl_file, pnl_config:pd.DataFrame):
    
    reader = openpyxl.load_workbook(local_pnl_file, data_only=False)
    writer = pd.ExcelWriter(local_pnl_file,
                        mode='a',
                        if_sheet_exists='overlay',
                        engine='openpyxl')
    
    def updating_fields(source_df, updating_df, column, block_alias, field, account_idx, **kwargs):
        
        if kwargs.get('is_account', 0):
            try:
                    
                
                # updating_df['date'] = pd.to_datetime(updating_df['date']).apply(lambda x: x.strftime('%Y%m%d'))
                updating_df = updating_df.set_index('date')
                # print('updating data: ', updating_df)
                updating_data = updating_df.loc[date, column]
                # print(source_df.tail(2))
                # print('block alias :', block_alias, 'field: ', field)
                if field == '指数':
                    source_df.loc[date, (block_alias, field)] = updating_data
                else:
                    source_df.loc[len(source_df)-1, (block_alias, field)] = updating_data
            except:
                print(f'error at account_idx: {account_idx}')
                print('updating_df: ', updating_df)
                print('updating_data: ', updating_data)
                print('error at updating_fields')
                print(source_df.tail(2))
                print('block alias :', block_alias, 'field: ', field)
                print('updating data: ', updating_data)
                raise
            
        if kwargs.get('is_futurehold', 0):
            # print(kwargs)
            
            # 更新期货持仓为 IC, IF, IM, IH
            updating_df['ticker'] = updating_df['ticker'].map(lambda x: x[0:2] if re_find('^IC|^IM|^IF|^IH', x) else x)
            updating_df['direction'] = updating_df['longshort'].map(lambda x: -1 if x == '多头' else 1)
            updating_df['volume'] = updating_df['volume'] * updating_df['direction']
            updating_df = updating_df[['ticker', 'volume']].groupby('ticker').sum()
            for ticker in updating_df.index:
                # if ticker in ICtickers:
                updating_data = updating_df.loc[ticker, 'volume']
                # print('block alias :', block_alias, 'ticker: ', ticker)
                # print('updating data: ', updating_data)
                source_df.loc[len(source_df)-1, (block_alias, ticker)] = updating_data
            
            
            # ICtickers = ast.literal_eval(kwargs['ICtickers'])
            # # updating_df = updating_df.set_index('ticker')
            # for ICticker in ICtickers:
            #     # if re_find('^IC|^IM|^IF|^IH', ICticker):
            #     #     source_ticker = ICticker[0:2]
            #     # else:
            #     #     source_ticker = ICticker
            #     if ICticker in updating_df.index:
            #         updating_data = updating_df.loc[ICticker, 'volume']
            #         print('block alias :', block_alias, 'ICticker: ', ICticker)
            #         print('updating data: ', updating_data)
            #         source_df.loc[len(source_df)-1, (block_alias, ICticker)] = updating_data
            
        return source_df
    
    # group configs
    grouped_sheets = pnl_config.groupby(['sheet_name',]) # 'account_name', 'src_type', 'updated_data_dir', 'updated_data_file'])
    # for each group , get sheet_name, dir_path, file, src_type
    for sheet_idx, grouped_sheets_configs in grouped_sheets:
        sheet_name = sheet_idx[0]
        # print(sheet_name)
        
        source_df = pd.read_excel(reader, sheet_name=sheet_name, engine='openpyxl', header=[1,2])
                
        # source_df['tmp_date'] = pd.to_datetime(source_df.iloc[:, 0])
        
        # 每个sheet 中的不同账号对应的文件的不同数据
        grouped_accounts = grouped_sheets_configs.groupby(['account_name', 'src_type', 'updated_data_dir', 'updated_data_file'])
        for account_idx, grouped_account_configs in grouped_accounts:
            # print(account_idx)
            account_name = account_idx[0]            
            src_type = account_idx[1]
            updated_data_dir = account_idx[2]
            updated_data_file = account_idx[3]
    
        # source_df.set_index(source_df.columns[0], inplace=True)
        # source_df.index.name = '日期'
        # print('source date', source_df.index[0])

            try:
                updating_df = read_remote_file(os.path.join(updated_data_dir, updated_data_file).format(account_name=account_name, date=date), src_type=src_type, dtype={'date':str})
            except FileNotFoundError:
                # logger.warning(f'{account_name} not in kf_accounts_dict, skip')
                logger.warning(f'FileNotFoundError: {os.path.join(updated_data_dir, updated_data_file).format(account_name=account_name, date=date)}')
                continue
                # raise
            # updating_df.set_index('date', inplace=True)
        # print('data date', updating_df.index[0])
    
        # for block alias, field, column in configs
            for idx, item_config in grouped_account_configs.iterrows():
                config = item_config.to_dict()
                column = config.pop('column')
                block_alias = config.pop('block_alias')
                field = config.pop('field')
                kwargs = config
                source_df = updating_fields(source_df, updating_df, column, block_alias, field, account_idx, **kwargs)
        # print('before', source_df)
        # source_df = source_df.reset_index()
            # print('after', source_df.head(2))
        
        # source_df.drop(columns=['tmp_date'], inplace=True)
        source_df = source_df.droplevel(level=0, axis=1)
        source_df.to_excel(writer, sheet_name=sheet_name, index=False, header=False, startrow=3)
    writer.close()

# %%


def copy_sheet_last_row_value_formula_format(local_pnl_file):
    # 找到最后一行
    def find_last_row(sheet):
        last_row = None
        for col in sheet.iter_cols(min_col=1, max_col=sheet.max_column):
            for cell in reversed(col):
                if cell.value is not None:
                    last_row = cell.row
                    break
            if last_row is not None:
                break
        return last_row
    #%%

    # copy the last row 复制最后一行
    wb = openpyxl.load_workbook(local_pnl_file, data_only=False)
    #%%
    for sheet_name in product_account_dict.keys():
        # sheet_name = '远航安心中性3号'
    # account_name = sheet_name
        # print(sheet_name)

        sheet = wb[sheet_name]
    # 检查每一个sheet的最后一行首列,是不是当前日期date, 倒数第二行是不是 pre_date

        max_col = sheet.max_column
        max_row = find_last_row(sheet=sheet)
        # print(max_col, max_row)

    # 复制数据
        for col in range(1, max_col+1):
            row = max_row
            cell_value = sheet.cell(row, col).value
            
            if sheet.cell(row, col).data_type == 'f':
                
                source_cell_loc = get_column_letter(col) + str(row)
                target_cell_loc = get_column_letter(col) + str(row+1)
                # print(source_cell_loc)
            
                target_formula = Translator(cell_value, origin=source_cell_loc).translate_formula(target_cell_loc)
                cell_value = target_formula
            # print(cell_value)
                sheet.cell(row+1, col).value = cell_value
    wb.save(local_pnl_file)


    # 复制最后一行的格式
    # 复制格式
    for sheet_name in product_account_dict.keys():

        sheet = wb[sheet_name]

        max_col = sheet.max_column
        max_row = find_last_row(sheet=sheet)
        
        for col in range(1, max_col+1):
            row = max_row
            source_cell = sheet.cell(row-1, col)
            target_cell = sheet.cell(row, col)
            if source_cell.has_style:
                target_cell._style = copy.copy(source_cell._style)
                target_cell.font = copy.copy(source_cell.font)
                target_cell.border = copy.copy(source_cell.border)
                target_cell.fill = copy.copy(source_cell.fill)
                target_cell.number_format = copy.copy(source_cell.number_format)
                target_cell.protection = copy.copy(source_cell.protection)
                target_cell.alignment = copy.copy(source_cell.alignment)

        # 复制行高
        # sheet.row_dimensions[max_row].height = sheet.row_dimensions[max_row-1].height
        for row_num in range(4, max_row+1):
            sheet.row_dimensions[row_num].height = 22
    wb.save(local_pnl_file)



#%%
sftp_clent_dav.get(pnl_file, local_pnl_file)
copy_sheet_last_row_value_formula_format(local_pnl_file)
test_update(local_pnl_file, pnl_config)
# %%
sftp_clent_dav.put(local_pnl_file, '远航安心中性1号/pnl/宽辅系列{}更新后.xlsx'.format(date))
# %%
