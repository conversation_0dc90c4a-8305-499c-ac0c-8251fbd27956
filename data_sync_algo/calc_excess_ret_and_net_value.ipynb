{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/trade\n"]}], "source": ["from utils import mysql\n", "from utils import trading_calendar\n", "import pandas as pd\n", "import datetime\n", "import os, sys\n", "from pathlib import Path\n", "\n", "notebook_dir = Path.cwd() \n", "print(str(notebook_dir.parent))\n", "sys.path.insert(0, str(notebook_dir.parent))\n", "from misc.ssh_conn import ftp_clent_zx_zhongtai\n", "from misc.Readstockfile import read_remote_file, write_file\n", "sys.path.remove(str(notebook_dir.parent))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def get_index_ret(symbol,date):\n", "    df=mysql.query(mysql.get_datayes_db_connection(),\"select * from mkt_idxd where TICKER_SYMBOL='{}' and TRADE_DATE={}\".format(symbol,date))\n", "    return df['CLOSE_INDEX'].values[0],df['CHG_PCT'].values[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["select * from mkt_idxd where TICKER_SYMBOL='000905' and TRADE_DATE=20250610\n", "select * from mkt_idxd where TICKER_SYMBOL='000852' and TRADE_DATE=20250610\n", "        date      net_val    stock_val  close_idx_500  idx_500_ret  \\\n", "0   20250428  10927313.94  10927313.94      5598.2951    -0.005133   \n", "1   20250429  11036778.58  11036778.58      5604.9057     0.001181   \n", "2   20250430  11124517.29  11124517.29      5631.8249     0.004803   \n", "3   20250506  11430107.10  11430107.10      5740.3338     0.019267   \n", "4   20250507  11510956.92  11510956.92      5750.2911     0.001735   \n", "5   20250508  11615113.12  11615113.12      5773.8056     0.004089   \n", "6   20250509  11512561.74  11512561.74      5721.7225    -0.009021   \n", "7   20250512  11620828.56  11620828.56      5793.6743     0.012575   \n", "8   20250513  11617507.25  11617507.25      5781.6683    -0.002072   \n", "9   20250514  11633497.65  11633497.65      5799.1191     0.003018   \n", "10  20250515  11573181.87  11573181.87      5715.2966    -0.014454   \n", "11  20250516  11686206.18  11686206.18      5715.8491     0.000097   \n", "12  20250519  11785565.08  11785565.08      5720.7949     0.000865   \n", "13  20250520  11887367.33  11887367.33      5747.3670     0.004645   \n", "14  20250521  11832618.99  11832618.99      5757.9225     0.001837   \n", "15  20250522  11742299.45  11742299.45      5703.2797    -0.009490   \n", "16  20250523  11627435.50  11627435.50      5653.0436    -0.008808   \n", "17  20250526  11760613.46  11760613.46      5669.4600     0.002900   \n", "18  20250527  11789452.19  11789452.19      5652.1500    -0.003100   \n", "19  20250528  11752176.37  11752176.37      5637.2400    -0.002600   \n", "20  20250529  11933634.63  11933634.63      5719.9100     0.014700   \n", "21  20250530  11770222.07  11770222.07      5671.0700    -0.008500   \n", "22  20250603  11877887.92  11877887.92      5694.8385     0.004191   \n", "23  20250604  12018298.14  12018298.14      5739.0058     0.007756   \n", "24  20250605  12044544.68  12044544.68      5769.9675     0.005395   \n", "25  20250606  12033237.08  12033237.08      5762.0778    -0.001367   \n", "26  20250609  12212541.84  12012541.84      5805.6543     0.007563   \n", "0   20250610  11071603.49          NaN      5757.9921    -0.008210   \n", "\n", "    close_idx_1000  idx_1000_ret  net_val_chg  excess_ret_500  \\\n", "0        5877.0566     -0.010514    -0.007981       -0.002863   \n", "1        5903.3812      0.004479     0.010018        0.008826   \n", "2        5950.1169      0.007917     0.007950        0.003132   \n", "3        6102.9221      0.025681     0.027470        0.008048   \n", "4        6111.4946      0.001405     0.007073        0.005329   \n", "5        6158.0774      0.007622     0.009048        0.004939   \n", "6        6082.0817     -0.012341    -0.008829        0.000194   \n", "7        6167.4606      0.014038     0.009404       -0.003131   \n", "8        6151.0098     -0.002667    -0.000286        0.001790   \n", "9        6160.3697      0.001522     0.001376       -0.001637   \n", "10       6057.0357     -0.016774    -0.005185        0.009405   \n", "11       6068.1165      0.001829     0.009766        0.009668   \n", "12       6095.3369      0.004486     0.008502        0.007631   \n", "13       6146.0166      0.008315     0.008638        0.003974   \n", "14       6132.1809     -0.002251    -0.004606       -0.006431   \n", "15       6066.1031     -0.010776    -0.007633        0.001875   \n", "16       5989.6752     -0.012599    -0.009782       -0.000983   \n", "17       6028.7900      0.006500     0.011454        0.008529   \n", "18       6008.4600     -0.003400     0.002452        0.005569   \n", "19       5984.4600     -0.004000    -0.003162       -0.000563   \n", "20       6089.5800      0.017600     0.015440        0.000730   \n", "21       6026.5600     -0.010300    -0.013693       -0.005238   \n", "22       6070.0368      0.007214     0.009147        0.004936   \n", "23       6123.1720      0.008754     0.011821        0.004034   \n", "24       6167.0149      0.007160     0.002184       -0.003194   \n", "25       6152.8446     -0.002298    -0.000939        0.000429   \n", "26       6218.9637      0.010746     0.014901        0.007283   \n", "0        6161.6919     -0.009209    -0.093423       -0.085919   \n", "\n", "    excess_ret_1000  net_val_idx_500  net_val_idx_1000  futures_val_begin  \\\n", "0          0.002559         0.997137          1.002559                NaN   \n", "1          0.005514         1.005938          1.008087                NaN   \n", "2          0.000032         1.009088          1.008120                NaN   \n", "3          0.001744         1.017209          1.009878                NaN   \n", "4          0.005660         1.022630          1.015595                NaN   \n", "5          0.001416         1.027681          1.017032                NaN   \n", "6          0.003556         1.027880          1.020649                NaN   \n", "7         -0.004570         1.024661          1.015985                NaN   \n", "8          0.002388         1.026495          1.018410                NaN   \n", "9         -0.000145         1.024815          1.018262                NaN   \n", "10         0.011787         1.034454          1.030265                NaN   \n", "11         0.007923         1.044455          1.038427                NaN   \n", "12         0.003998         1.052425          1.042579                NaN   \n", "13         0.000320         1.056608          1.042913                NaN   \n", "14        -0.002360         1.049813          1.040452                NaN   \n", "15         0.003177         1.051781          1.043757                NaN   \n", "16         0.002853         1.050747          1.046735                NaN   \n", "17         0.004922         1.059709          1.051887                NaN   \n", "18         0.005872         1.065611          1.058064                NaN   \n", "19         0.000842         1.065011          1.058954                NaN   \n", "20        -0.002122         1.065788          1.056707                NaN   \n", "21        -0.003429         1.060205          1.053084                NaN   \n", "22         0.001919         1.065438          1.055105                NaN   \n", "23         0.003041         1.069736          1.058313                NaN   \n", "24        -0.004941         1.066319          1.053084                NaN   \n", "25         0.001362         1.066777          1.054519                NaN   \n", "26         0.004111         1.071162          1.058853           200000.0   \n", "0         -0.084997         0.979129          0.968854                NaN   \n", "\n", "    futures_pnl  futures_val_end  \n", "0           NaN              NaN  \n", "1           NaN              NaN  \n", "2           NaN              NaN  \n", "3           NaN              NaN  \n", "4           NaN              NaN  \n", "5           NaN              NaN  \n", "6           NaN              NaN  \n", "7           NaN              NaN  \n", "8           NaN              NaN  \n", "9           NaN              NaN  \n", "10          NaN              NaN  \n", "11          NaN              NaN  \n", "12          NaN              NaN  \n", "13          NaN              NaN  \n", "14          NaN              NaN  \n", "15          NaN              NaN  \n", "16          NaN              NaN  \n", "17          NaN              NaN  \n", "18          NaN              NaN  \n", "19          NaN              NaN  \n", "20          NaN              NaN  \n", "21          NaN              NaN  \n", "22          NaN              NaN  \n", "23          NaN              NaN  \n", "24          NaN              NaN  \n", "25          NaN              NaN  \n", "26          0.0         200000.0  \n", "0           NaN              NaN  \n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/trade/data_sync_algo/utils/mysql.py:86: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.\n", "  return pd.read_sql(sql,conn)\n"]}], "source": ["date=datetime.datetime.now().strftime('%Y%m%d')\n", "pre_date=trading_calendar.get_prev_trade_date(date)\n", "asserts=pd.read_csv(r\"/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/Asset_{}.csv\".format(date,date))\n", "close_idx_500,idx_500_ret=get_index_ret(\"000905\",date)\n", "close_idx_1000,idx_1000_ret=get_index_ret(\"000852\",date)\n", "net_val=asserts['净资产'].values[0]\n", "data=pd.read_csv(r\"/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/clz_zz1000.csv\")\n", "data=data[data['date']<int(date)]\n", "preday_data=data[data['date']==int(pre_date)].iloc[0]\n", "d={}\n", "d['date']=int(date) \n", "d['net_val']=net_val\n", "d['close_idx_500']=close_idx_500\n", "d['idx_500_ret']=idx_500_ret\n", "d['close_idx_1000']=close_idx_1000\n", "d['idx_1000_ret']=idx_1000_ret\n", "d['net_val_chg']=(net_val/preday_data['net_val'])-1\n", "d['excess_ret_500']=((1+d['net_val_chg'])/(1+d['idx_500_ret'])) -1\n", "d['excess_ret_1000']=((1+d['net_val_chg'])/(1+d['idx_1000_ret'])) -1\n", "d['net_val_idx_500']=preday_data['net_val_idx_500']*(1+d['excess_ret_500'])\n", "d['net_val_idx_1000']=preday_data['net_val_idx_1000']*(1+d['excess_ret_1000'])\n", "df=pd.concat([data,pd.DataFrame([d])])\n", "print(df)\n", "\n", "write_file(df, file_type='csv', dest_type='zx_zhongtai', dest_path=os.path.join('daily_after', 'clz_zz1000.csv'), index=False)\n", "\n", "# df.to_csv(r\"/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/clz_zz1000.csv\",index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 如果idx数据没有更新可以手动计算"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# date=\"20250530\"\n", "date=datetime.datetime.now().strftime('%Y%m%d')\n", "pre_date=trading_calendar.get_prev_trade_date(date)\n", "asserts=pd.read_csv(r\"/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/Asset_{}.csv\".format(date,date))\n", "# close_idx_500,idx_500_ret=get_index_ret(\"000905\",date)\n", "# close_idx_1000,idx_1000_ret=get_index_ret(\"000852\",date)\n", "\n", "# ------------------\n", "close_idx_500=5671.07\n", "idx_500_ret=-0.0085\n", "close_idx_1000=6026.56\n", "idx_1000_ret=-0.0103\n", "# ------------------\n", "net_val=asserts['净资产'].values[0]\n", "data=pd.read_csv(r\"/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/clz_zz1000.csv\")\n", "preday_data=data[data['date']==int(pre_date)].iloc[0]\n", "d={}\n", "d['date']=int(date) \n", "d['net_val']=net_val\n", "d['close_idx_500']=close_idx_500\n", "d['idx_500_ret']=idx_500_ret\n", "d['close_idx_1000']=close_idx_1000\n", "d['idx_1000_ret']=idx_1000_ret\n", "d['net_val_chg']=(net_val/preday_data['net_val'])-1\n", "d['excess_ret_500']=((1+d['net_val_chg'])/(1+d['idx_500_ret'])) -1\n", "d['excess_ret_1000']=((1+d['net_val_chg'])/(1+d['idx_1000_ret'])) -1\n", "d['net_val_idx_500']=preday_data['net_val_idx_500']*(1+d['excess_ret_500'])\n", "d['net_val_idx_1000']=preday_data['net_val_idx_1000']*(1+d['excess_ret_1000'])\n", "df=pd.concat([data,pd.DataFrame([d])])\n", "\n", "write_file(df, file_type='csv', dest_type='zx_zhongtai', dest_path=os.path.join('daily_after', 'clz_zz1000.csv'), index=False)\n", "# df.to_csv(r\"/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/clz_zz1000.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from utils import trading_calendar"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dates=trading_calendar.get_trading_dates(\"20250425\",\"20250523\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l=[]\n", "for date in dates:\n", "    close_idx_500,idx_500_ret=get_index_ret(\"000905\",date)\n", "    close_idx_1000,idx_1000_ret=get_index_ret(\"000852\",date)\n", "    asserts=pd.read_csv(r\"/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/Asset_{}.csv\".format(date,date))\n", "    net_val=asserts['净资产'].values[0]\n", "    l.append([date,net_val,close_idx_500,idx_500_ret,close_idx_1000,idx_1000_ret])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df=pd.DataFrame(l,columns=['date','net_val','close_idx_500','idx_500_ret','close_idx_1000','idx_1000_ret'])\n", "df['net_val_chg']=df['net_val'].pct_change().fillna(0)\n", "df=df[df['date']>\"20250425\"]\n", "df['excess_ret_500']=((1+df['net_val_chg'])/(1+df['idx_500_ret'])) -1\n", "df['excess_ret_1000']=((1+df['net_val_chg'])/(1+df['idx_1000_ret'])) -1\n", "df['net_val_idx_500']=(1+df['excess_ret_500']).cumprod()\n", "df['net_val_idx_1000']=(1+df['excess_ret_1000']).cumprod()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["write_file(df, file_type='csv', dest_type='zx_zhongtai', dest_path=os.path.join('daily_after', 'clz_zz1000.csv'), index=False)\n", "# df.to_csv(r\"/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/clz_zz1000.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}