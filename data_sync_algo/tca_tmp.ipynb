{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/lib/python3.12/site-packages/py_mini_racer/py_mini_racer.py:13: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html\n", "  import pkg_resources\n"]}], "source": ["import json\n", "import datetime\n", "import efinance as ef\n", "from utils import mysql\n", "import pandas as pd\n", "# from data.raw_data import mins_data\n", "import requests\n", "from pqdm.processes import pqdm\n", "import datetime\n", "import adata"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def calc_cost(avg_px,bm_px,side):\n", "    return  side*(avg_px/bm_px -1 )*10000\n", "def calc(mktdata,po,f):\n", "    idata=mktdata[(mktdata['time']>po['start_time'])&(mktdata['time']<=po['end_time'])]\n", "    if idata['qty'].sum()<=0:\n", "        vwap=po['filled_price']\n", "    else:\n", "        if f:\n", "            vwap=idata['amt'].sum()/idata['qty'].sum()\n", "        else:\n", "            vwap=idata['amt'].sum()/idata['qty'].sum()/100\n", "    if vwap<=0:\n", "        po.update({'vwap':0,'vwap_cost':0})\n", "        return po\n", "    vwap_cost=calc_cost(po['filled_price'],vwap,po['side'])\n", "    po.update({'vwap':vwap,'vwap_cost':vwap_cost})\n", "    return po\n", "\n", "def stats(res):\n", "    d={}\n", "    d['wavg_cost']=-(res['vwap_cost']*res['executed_notional']/res['executed_notional'].sum()).sum()\n", "    d['avg_cmp_rate']=res['comp_rate'].mean()\n", "    d['wavg_cmp_rate']=(res['comp_rate']*(res['vwap']*res['quantity'])/(res['vwap']*res['quantity']).sum()).sum()\n", "    d['exe_value']=res['executed_notional'].sum()\n", "    return d\n", "\n", "def show(datas):\n", "    l=[]\n", "    d=stats(datas)\n", "    d['group']='total'\n", "    l.append(d)\n", "    for gn in [['operation','opt'],['algo_name','algo'],['target_exe_time','exe-time'],['user','batch'],['account_id','account']]:\n", "        for n,g in datas.groupby(gn[0]):\n", "            r=stats(g)\n", "            r['group']='{}-{}'.format(gn[1],n)\n", "            l.append(r)\n", "    return pd.DataFrame(l)\n", "\n", "def show2(datas):\n", "    l=[]\n", "    d=stats(datas)\n", "    d['group']='total'\n", "    l.append(d)\n", "    for gn in [['operation','opt'],['firm','user'],['account_id','account']]:\n", "        for n,g in datas.groupby(gn[0]):\n", "            r=stats(g)\n", "            r['group']='{}-{}'.format(gn[1],n)\n", "            l.append(r)\n", "    return pd.DataFrame(l)\n", "\n", "def get_klines(syms):\n", "    results=pqdm([[_] for _ in syms],adata.stock.market.get_market_min,n_jobs=10,argument_type='args')\n", "    df=pd.concat(results)\n", "    df=df.rename(columns={'stock_code':'symbol','amount':'amt','volume':'qty','trade_time':'time'})\n", "    df['amt']=df['amt'].astype(float)\n", "    df['qty']=df['qty'].astype(float)\n", "    df['time']=pd.to_datetime(df['time'])\n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 用互联网行情计算trading cost"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 用efinance 数据 （现在有点问题）"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# date=\"********\"\n", "# date=datetime.datetime.now().strftime('%Y%m%d')\n", "# pos=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from algo_parentorder where date={}\".format(date))\n", "# pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)\n", "# df = ef.stock.get_quote_history(list(pos['symbol'].unique()), klt=1)\n", "# stk_dict={}\n", "# for k,v in df.items():\n", "#     v=v.rename(columns={'股票代码':'symbol', '日期':'time', '开盘':'open', '收盘':'close', '最高':'high', '最低':'low', '成交量':'qty', '成交额':'amt'})\n", "#     v=v[['symbol','time','open','close','high','low','qty','amt']]\n", "#     v['time']=pd.to_datetime(v['time'])\n", "#     stk_dict[k]=v\n", "# results=[]\n", "# for po in pos.to_dict('records'):\n", "#     r=calc(stk_dict[po['symbol']],po,False)\n", "#     results.append(r)\n", "# res=pd.DataFrame(results)\n", "# res['executed_notional']=res['filled_price']*res['filled_quantity']\n", "# res['comp_rate']=res['filled_quantity']/res['quantity']\n", "# res['target_exe_time']=res['end_time']-res['start_time'] \n", "# show2(res)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# date=\"********\"\n", "# pos=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from algo_parentorder where date={}\".format(date))\n", "# pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)\n", "# df=get_klines(list(pos['symbol'].unique()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### 用adata 计算"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/trade/data_sync_algo/utils/mysql.py:88: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.\n", "  return pd.read_sql(sql,conn)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["select * from algo_parentorder where date=20250627\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e35d00e9478b4b9f94d95af05d5e4031", "version_major": 2, "version_minor": 0}, "text/plain": ["QUEUEING TASKS | :   0%|          | 0/1406 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/lib/python3.12/multiprocessing/popen_fork.py:66: DeprecationWarning: This process (pid=3685343) is multi-threaded, use of fork() may lead to deadlocks in the child.\n", "  self.pid = os.fork()\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1d2ba062c5274ea9944655b68734fc8f", "version_major": 2, "version_minor": 0}, "text/plain": ["PROCESSING TASKS | :   0%|          | 0/1406 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9b8beb32419945898dacff1c473e47c5", "version_major": 2, "version_minor": 0}, "text/plain": ["COLLECTING RESULTS | :   0%|          | 0/1406 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>wavg_cost</th>\n", "      <th>avg_cmp_rate</th>\n", "      <th>wavg_cmp_rate</th>\n", "      <th>exe_value</th>\n", "      <th>group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.686463</td>\n", "      <td>0.998475</td>\n", "      <td>0.993183</td>\n", "      <td>1.514631e+07</td>\n", "      <td>total</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>8.564079</td>\n", "      <td>1.000000</td>\n", "      <td>0.999105</td>\n", "      <td>7.529270e+06</td>\n", "      <td>opt-0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-7.100383</td>\n", "      <td>0.996624</td>\n", "      <td>0.987397</td>\n", "      <td>7.617037e+06</td>\n", "      <td>opt-1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.846775</td>\n", "      <td>0.994766</td>\n", "      <td>0.990466</td>\n", "      <td>1.010228e+07</td>\n", "      <td>user-ch<PERSON><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-1.118805</td>\n", "      <td>1.000000</td>\n", "      <td>0.997250</td>\n", "      <td>2.448354e+06</td>\n", "      <td>user-kuanfu</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1.765343</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2.595677e+06</td>\n", "      <td>user-wuzhi</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>-1.118805</td>\n", "      <td>1.000000</td>\n", "      <td>0.997250</td>\n", "      <td>2.448354e+06</td>\n", "      <td>account-2____10313____111____49____07200000213...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>-6.584432</td>\n", "      <td>0.997869</td>\n", "      <td>0.980534</td>\n", "      <td>3.123157e+06</td>\n", "      <td>account-2____10336____103361____49____06500328...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1.765343</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2.595677e+06</td>\n", "      <td>account-2____10355____10355____49____888355888...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>4.172242</td>\n", "      <td>0.992998</td>\n", "      <td>0.994976</td>\n", "      <td>6.979119e+06</td>\n", "      <td>account-clz_zhongtai_109156033251</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   wavg_cost  avg_cmp_rate  wavg_cmp_rate     exe_value  \\\n", "0   0.686463      0.998475       0.993183  1.514631e+07   \n", "1   8.564079      1.000000       0.999105  7.529270e+06   \n", "2  -7.100383      0.996624       0.987397  7.617037e+06   \n", "3   0.846775      0.994766       0.990466  1.010228e+07   \n", "4  -1.118805      1.000000       0.997250  2.448354e+06   \n", "5   1.765343      1.000000       1.000000  2.595677e+06   \n", "6  -1.118805      1.000000       0.997250  2.448354e+06   \n", "7  -6.584432      0.997869       0.980534  3.123157e+06   \n", "8   1.765343      1.000000       1.000000  2.595677e+06   \n", "9   4.172242      0.992998       0.994976  6.979119e+06   \n", "\n", "                                               group  \n", "0                                              total  \n", "1                                              opt-0  \n", "2                                              opt-1  \n", "3                                   user-ch<PERSON><PERSON><PERSON><PERSON>  \n", "4                                        user-kuanfu  \n", "5                                         user-wuzhi  \n", "6  account-2____10313____111____49____07200000213...  \n", "7  account-2____10336____103361____49____06500328...  \n", "8  account-2____10355____10355____49____888355888...  \n", "9                  account-clz_zhongtai_109156033251  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# date=\"********\"\n", "date=datetime.datetime.now().strftime('%Y%m%d')\n", "pos=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from algo_parentorder where date={}\".format(date))\n", "pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))\n", "pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))\n", "pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)\n", "df=get_klines(list(pos['symbol'].unique()))\n", "stk_dict={}\n", "for k,v in df.groupby('symbol'):\n", "    stk_dict[k]=v\n", "results=[]\n", "for po in pos.to_dict('records'):\n", "    try:\n", "        r=calc(stk_dict[po['symbol']],po,True)\n", "        results.append(r)\n", "    except KeyError:\n", "        print('no data for symbol {}'.format(po['symbol']))\n", "res=pd.DataFrame(results)\n", "res['executed_notional']=res['filled_price']*res['filled_quantity']\n", "res['comp_rate']=res['filled_quantity']/res['quantity']\n", "res['target_exe_time']=res['end_time']-res['start_time'] \n", "show2(res)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['clz_zhongtai_109156033251'\n", " '2____10336____103361____49____0650032828____'\n", " '2____10313____111____49____072000002135____'\n", " '2____10355____10355____49____8883558888____']\n"]}], "source": ["print(res['account_id'].unique())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['20250627743402110977002506' '20250627743402110977600638'\n", " '20250627743402110977600676' ...\n", " '00008883558888_1002_2_94@guojinQMT20250627'\n", " '00008883558888_1002_2_96@guojinQMT20250627'\n", " '00008883558888_1002_2_98@guojinQMT20250627']\n"]}], "source": ["print(res['id'].unique())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 用本地L1行情计算trading cost"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# from data.raw_data import mins_data\n", "# import pandas as pd"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# def read_datas_from_zipfile(date,symbols):\n", "#     datas=mins_data.read_mindata_from_zip_file(date,1)\n", "#     datas['time']=pd.to_datetime(datas['datadate'].astype(str)+\" \"+datas['bartime']+\":00\")\n", "#     datas=datas.rename(columns={'ticker':'symbol','openprice':'open', 'closeprice':'close', 'highprice':'high', 'lowprice':'low', 'volume':'qty', 'value':'amt'})\n", "#     datas=datas[['symbol','time','open','close','high','low','qty','amt']]\n", "#     datas=datas[datas['symbol'].isin(symbols)]\n", "#     d={}\n", "#     for sym,g in datas.groupby('symbol'):\n", "#         g=g[['time','open','close','high','low','qty','amt']]\n", "#         d[sym]=g\n", "#     return d"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# date=\"20250530\"\n", "# pos=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from algo_parentorder where date={}\".format(date))\n", "# pos=pos.fillna(0)\n", "# pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)\n", "# stk_dict=read_datas_from_zipfile(date,pos['symbol'].unique())\n", "# results=[]\n", "# for po in pos.to_dict('records'):\n", "#     r=calc(stk_dict[po['symbol']],po,True)\n", "#     results.append(r)\n", "# res=pd.DataFrame(results)\n", "# res['executed_notional']=res['filled_price']*res['filled_quantity']\n", "# res['comp_rate']=res['filled_quantity']/res['quantity']\n", "# res['target_exe_time']=res['end_time']-res['start_time'] "]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# show2(res)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# df=ef.stock.get_realtime_quotes()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# results=[]\n", "# for po in pos.to_dict('records'):\n", "#     r=calc(stk_dict[po['symbol']],po,True)\n", "#     results.append(r)\n", "# res=pd.DataFrame(results)\n", "# res['executed_notional']=res['filled_price']*res['filled_quantity']\n", "# res['comp_rate']=res['filled_quantity']/res['quantity']\n", "# res['target_exe_time']=res['end_time']-res['start_time'] "]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# show2(res)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}