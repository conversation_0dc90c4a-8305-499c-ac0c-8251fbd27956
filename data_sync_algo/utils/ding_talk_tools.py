from dingtalkchatbot.chatbot import DingtalkChatbot, ActionCard, CardItem
# WebHook地址

def init_daily_run_bot():
    webhook = 'https://oapi.dingtalk.com/robot/send?access_token=803ad97ef5843c7dfc0ec2a57dc0df2d49c9c9f1cbbd8f1ad7794d296f166527'
    secret = 'SEC3f16c87f59bcc8af2dc9bd6b19d39efa10519ca743143db95009949609dd54e8'  # 可选：创建机器人勾选“加签”选项时使用
    # 初始化机器人小丁
    # xiaoding = DingtalkChatbot(webhook)  # 方式一：通常初始化方式
    xiaoding = DingtalkChatbot(webhook, secret=secret)  # 方式二：勾选“加签”选项时使用（v1.5以上新功能）
    # xiaoding = DingtalkChatbot(webhook, pc_slide=True)  # 方式三：设置消息链接在PC端侧边栏打开（v1.5以上新功能）
    # Text消息@所有人
    # xiaoding.send_text(msg='我就是小丁，小丁就是我！', is_at_all=True)
    return xiaoding

def init_g1_bot():
    webhook = 'https://oapi.dingtalk.com/robot/send?access_token=287dbb90f61b1897127a624235b4bdfcc5515b3c557cb3636c300e3daf24a9f8'
    secret = 'SECe67c91d34f42cc47b37550914e7f460052fe397f33f1b4f9a934b3ea17c23288'  # 可选：创建机器人勾选“加签”选项时使用
    # 初始化机器人小丁
    # xiaoding = DingtalkChatbot(webhook)  # 方式一：通常初始化方式
    xiaoding = DingtalkChatbot(webhook, secret=secret)  # 方式二：勾选“加签”选项时使用（v1.5以上新功能）
    # xiaoding = DingtalkChatbot(webhook, pc_slide=True)  # 方式三：设置消息链接在PC端侧边栏打开（v1.5以上新功能）
    # Text消息@所有人
    # xiaoding.send_text(msg='我就是小丁，小丁就是我！', is_at_all=True)
    return xiaoding

def init_intraday_stats_bot():
    webhook = 'https://oapi.dingtalk.com/robot/send?access_token=6b5d368a76cb1aef3eed880950f87570d15518ea3de6b69769bb33e0c1e4f132'
    secret = 'SEC4fa03723be40fd394accd3f251a5e0b40f8e4732bcc746af3d58865b0c90d2fb'  # 可选：创建机器人勾选“加签”选项时使用
    # 初始化机器人小丁
    # xiaoding = DingtalkChatbot(webhook)  # 方式一：通常初始化方式
    xiaoding = DingtalkChatbot(webhook, secret=secret)  # 方式二：勾选“加签”选项时使用（v1.5以上新功能）
    # xiaoding = DingtalkChatbot(webhook, pc_slide=True)  # 方式三：设置消息链接在PC端侧边栏打开（v1.5以上新功能）
    # Text消息@所有人
    # xiaoding.send_text(msg='我就是小丁，小丁就是我！', is_at_all=True)
    return xiaoding