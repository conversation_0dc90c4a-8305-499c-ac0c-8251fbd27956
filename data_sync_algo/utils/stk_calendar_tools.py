from utils import mysql

def read_trade_calendar(start_date,end_date):
    return mysql.query(mysql.get_datayes_db_connection(),"select * from md_trade_cal where CALENDAR_DATE>={} and CALENDAR_DATE<={} and EXCHANGE_CD='XSHG'".format(start_date,end_date))


def get_trading_dates(start_date,end_date):
    df=read_trade_calendar(start_date,end_date)
    df=df[df['IS_OPEN']==1]
    if df.empty:
        return None
    dates=df['CALENDAR_DATE'].values
    return [_.strftime("%Y%m%d") for _ in dates]


def get_prev_trade_date(date):
    data=mysql.query(mysql.get_datayes_db_connection(),"select PREV_TRADE_DATE from md_trade_cal where EXCHANGE_CD='XSHG' and CALENDAR_DATE={}".format(date),False)
    return data[0][0].strftime("%Y%m%d")

def get_next_trade_date(date):
    data=mysql.query(mysql.get_datayes_db_connection(),"select CALENDAR_DATE from md_trade_cal where EXCHANGE_CD='XSHG' and PREV_TRADE_DATE={} and IS_OPEN=1".format(date),False)
    return data[0][0].strftime("%Y%m%d")

def is_trading_day(date):
    return mysql.query(mysql.get_datayes_db_connection(),"select IS_OPEN from md_trade_cal where CALENDAR_DATE={} and EXCHANGE_CD='XSHG'".format(date),False)[0][0]==1

def get_trading_days_before_date(date,n):
    return mysql.query(mysql.get_datayes_db_connection(),"select CALENDAR_DATE from md_trade_cal where CALENDAR_DATE<{} and EXCHANGE_CD='XSHG' and IS_OPEN=1 order by CALENDAR_DATE desc limit {}".format(date,n))

#返回date之前n天的交易日
def get_trading_day_before_date(date,n):
    return get_trading_days_before_date(date,n).values[-1][0].strftime("%Y%m%d")

if __name__=="__main__":
   print(get_trading_days_before_date("20220915",30).values[-1][0].strftime("%Y%m%d"))