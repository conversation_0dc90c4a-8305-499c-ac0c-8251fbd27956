from typing import Tuple
# from py import log
from loguru import logger
import pymysql
import pandas as pd
from sqlalchemy import create_engine,text

def get_connection(host,db,user,passwd,port=3306,engine=False):
    if engine:
        return create_engine("mysql+pymysql://{}:{}@{}/{}?charset={}".format(user,passwd,host,db,'utf8mb4'))
    conn=pymysql.connect(host=host,db=db,user=user,passwd=passwd,port=port)
    return conn

def get_liangke_db_connection(engine=False):
    return get_connection("*************","liangke","root","llys3,ysykR",engine=engine)

def get_trading_data_db_connection(engine=False):
    return get_connection("*************","trading_data","root","llys3,ysykR",engine=engine)

def get_zs_trading_data_db_connection(engine=False):
    return get_connection("************","zs_trading_data","root","jtwmy,dt4gx",engine=engine)

def get_trading_data_db_connection_new(engine=False):
    return get_connection("************","trading_data","root","jtwmy,dt4gx",engine=engine)

def get_trading_data_db_connectio_tmp(engine=False):
    return get_connection("*************","trading_data_tmp","root","llys3,ysykR",engine=engine)

def get_luoding_trading_db_connection(engine=False):
    return get_connection("*************","luoding_trading","root","llys3,ysykR",engine=engine)

def get_kuanfu_trading_db_connection(engine=False):
    return get_connection("*************","kuanfu_trading","root","llys3,ysykR",engine=engine)

def get_kuanfu_db_connection(engine=False):
    return get_connection("*************", "kuanfu_trading", "root", "llys3,ysykR", engine=engine)

def get_stk_vol_connection(engine=False):
    return get_connection("*************","stk_vol","root","llys3,ysykR",engine=engine)

def get_stk_basic_connection(engine=False):
    return get_connection("************","stk_basic","root","jtwmy,dt4gx",engine=engine)

def get_algo_db_connection(engine=False):
    return get_connection("************","algo","root","jtwmy,dt4gx",engine=engine)
   

def get_crypto_currency_db_connection(engine=False):
    return get_connection("*************","crypto_currency","root","llys3,ysykR",engine=engine)

def get_datayes_db_connection(engine=False):
    return get_connection("************","datayes","root","ZS@tonglian2025",port=3307,engine=engine)

def get_kf_db_connection(engine=False):
    return get_connection("61.147.97.21","trade","tradeRO","tradeRO@KF123",63306,engine=engine)


def get_equ_mkt_data(date):
    conn=get_datayes_db_connection()
    data=query(conn,"select * from mkt_equd where TRADE_DATE='{}'".format(date))
    return data

def insert(conn,tbl,data):
    keys=','.join(data.keys())
    values=','.join(['%s']*len(data))
    sql=f'insert into {tbl} ({keys}) values ({values})'
    execute(conn,sql,data.values())

def execute(conn,sql,values=None):
    print(sql)
    cur=conn.cursor()
    try:
        if values is None:
            cur.execute(sql)
        else:
            cur.execute(sql,tuple(values))
    except Exception as e:
        conn.rollback()
        raise Exception("execute sql failed ",e)
    finally:
        cur.close()                

def query(conn,sql,DataFrame=True):
    print(sql)
    cur=conn.cursor()
    try:
        if DataFrame:
            return pd.read_sql(sql,conn)
        cur.execute(sql)
        return cur.fetchall()
    except Exception as e:
        raise Exception("execute sql failed {}".format(e))       
    finally:
        cur.close()     

def upsert_dataframe(engine,df,tbl,key_dict):
    if df.empty:
        return
    if not key_dict=={}:
        try:
            s=""
            first=True
            for k,v in key_dict.items():
                if not first:
                    s=s+ "and"
                if type(v)==tuple:
                    s=s+" {} in {} ".format(k,v)
                elif type(v)==list:
                    s=s+ " {} in {} ".format(k,tuple(v))    
                else:
                    s=s+ " {} = '{}' ".format(k,v)    
                first=False
            with engine.begin() as conn:
                conn.execute(text("delete from {}.{} where {}".format(engine.url.database,tbl,s)))
                logger.warning(f'delete data from {engine.url.database}.{tbl}')

        except Exception as e:    
            print("delete failed...{}".format(e))        
    ret=df.to_sql(tbl,engine,if_exists="append",index=False)
    # if ret is not None:
    #     raise Exception("insert dataframe failed {}".format(ret))
    print("upsert dataframe ret:{}".format(ret))


    
if __name__=="__main__":
    result=query(get_datayes_db_connection(False),"select * from block_trading limit 10")
    print(result)
    