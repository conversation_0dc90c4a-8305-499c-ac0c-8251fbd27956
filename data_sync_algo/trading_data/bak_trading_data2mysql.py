from utils import mysql
import pandas as pd

account_firm_map={'28131077_3104_3104':'kuanfu','9225553_1102_1102':'wuzhi'}
def int2str(x):
    return str(int(x))

def operation_map(x):
    if x==1:
        return 0
    if x==2:
        return 1
    return None

def status_map(s):
    if s=='1':
        return 1
    if s=='2':
        return 2
    if s=='3':
        return 5
    if s=='4':
        return 7
    if s=='5':
        return 3
    if s=='6':
        return 6
    if s=='7':
        return 7
    if s=='8':
        return 9
    return 0

def dump_trading_algo_parentorders(date):
    df=pd.read_csv(r"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsest_strategyorder_{}.csv".format(date))
    df=df[df['strategy_exec_broker']=='ldsmart01']
    new_df=pd.DataFrame()
    new_df['date']=df['create_date'].astype(int)
    new_df['id']='guojun_ld_'+df['create_date'].apply(int2str)+df['strategy_id'].apply(int2str)
    new_df['operation']=df['strategy_dir'].apply(operation_map)
    new_df['create_time']=df['create_time'].astype(int)
    new_df['start_time']=df['begin_time'].astype(int)
    new_df['end_time']=df['end_time'].astype(int)
    new_df['symbol']=df['stock_code'].apply(lambda x:str(int(x)).zfill(6))
    new_df['quantity']=df['strategy_qty'].astype(int)
    new_df['filled_quantity']=df['strike_qty'].astype(int)
    new_df['filled_price']=df['strike_amt']/df['strike_qty']
    new_df['account_id']=df['fund_account']
    new_df['algo_name']='vwap'
    new_df['firm']=df['fund_account'].apply(lambda x:account_firm_map.get(x,x))
    new_df['algo_provider']='zhishu'
    new_df['pm']='xuj'
    new_df['broker']='guotaijunan'
    new_df['params']=""
    new_df['sys_type']="kafang_zongxian"
    new_df['remark1']=""
    new_df['remark2']=""
    new_df['remark3']=""
    mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),new_df,"algo_parentorder",{'date':date,'sys_type':"kafang_zongxian","broker":"guotaijunan"})

def dump_intraday_parentorders(date):
    df=pd.read_csv(r"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsest_strategyorder_{}.csv".format(date))
    df=df[df['strategy_exec_broker']=='ldsmart02']
    if df.empty:
        print("intraday parentorder is empty")
        return
    new_df=pd.DataFrame()
    new_df['date']=df['create_date'].astype(int)
    new_df['id']='guojun_ld_'+df['create_date'].apply(int2str)+df['strategy_id'].apply(int2str)
    new_df['create_time']=df['create_time'].astype(int)
    new_df['start_time']=df['begin_time'].astype(int)
    new_df['end_time']=df['end_time'].astype(int)
    new_df['symbol']=df['stock_code'].apply(lambda x:str(x).zfill(6))
    new_df['quantity']=df['strategy_qty'].astype(int)
    new_df['account_id']=df['fund_account']
    new_df['algo_name']='aaron_t0_v1'
    new_df['firm']=df['fund_account'].apply(lambda x:account_firm_map.get(x,x))
    new_df['algo_provider']='zhishu'
    new_df['pm']='aaron'
    new_df['broker']='guotaijunan'
    new_df['params']=""
    new_df['sys_type']="kafang_zongxian"
    new_df['remark1']=""
    new_df['remark2']=""
    new_df['remark3']=""
    mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),new_df,"intraday_parentorder",{'date':date,'sys_type':"kafang_zongxian","broker":"guotaijunan"})


def dump_orders(date):
    df=pd.read_csv(r"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsetd_order_{}.csv".format(date))
    new_df=pd.DataFrame()
    new_df['id']='guojun_ld_'+df['create_date'].apply(int2str)+df['order_id'].apply(int2str)
    new_df['pid']='guojun_ld_'+df['create_date'].apply(int2str)+df['strategy_id'].apply(int2str)
    new_df['create_time']=pd.to_datetime(df['create_date'].apply(int2str)+df['create_time'].apply(lambda x:str(int(x)).zfill(6))).astype('int64') // 1e9
    new_df['last_upd_time']=pd.to_datetime(df['create_date'].apply(int2str)+df['update_time'].apply(lambda x:str(int(x)).zfill(6))).astype('int64') // 1e9
    new_df['symbol']=df['stock_code'].apply(lambda x:str(x).zfill(6))
    new_df['account_id']=df['fund_account']
    new_df['operation']=df['order_dir'].apply(operation_map)
    new_df['price']=df['order_price']
    new_df['quantity']=df['order_qty'].astype(int)
    new_df['filled_price']=df['strike_amt']/df['strike_qty']
    new_df['filled_quantity']=df['strike_qty'].astype(int)
    new_df['status']=df['order_status'].astype(str).apply(status_map)
    new_df['order_id']=''
    new_df['order_type']=0
    new_df['err_msg']=''
    new_df['remark1']=''
    new_df['remark2']=''
    mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),new_df,"orders",{'id':tuple(new_df['id'].values)})


if __name__=="__main__":
    date="********"
    dump_trading_algo_parentorders(date)
    dump_intraday_parentorders(date)
    dump_orders(date)