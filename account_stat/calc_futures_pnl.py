from operator import mul
import os, sys
import pandas as pd
import numpy as np
from loguru import logger
import datetime

current_dir = os.path.dirname(os.path.abspath(__file__))

# 将项目路径添加到模块搜索路径
project_dir = os.path.abspath(os.path.join(current_dir, ".."))  # 通过..返回上一级目录
sys.path.append(project_dir)

from misc.Readstockfile import read_remote_file
from data_utils.trading_calendar import Calendar




def calcu_estimate_pnl(pre_hold, hold, pre_price, price, deal, multiplier):
    # hold = self.hold
    # # pre_hold = self.pre_hold
    # pre_hold = self.pre_hold
    # price = self.price
    # pre_price = self.pre_price
    # deal = self.today_deal
    
    
    # contracts_info = self.contracts_info
    
    
    # multiplier = pd.DataFrame(data={'ticker' : ['中证500', '中证1000', '沪深300', '上证50']})
    # multiplier['multiplier'] = multiplier['ticker'].apply(cffex_multiplier)
    # # print(multiplier)
    # multiplier = pd.concat([multiplier, contracts_info[['ticker', 'multiplier']]], axis=0, ignore_index=True)
    
    # print(deal)
    # print(deal)
    # if 'fill_volume' not in deal.columns:
    #     deal.rename(columns={'volume': 'fill_volume', 'price': 'fill_price'}, inplace=True)

    deal["fill_volume"] = (
        (deal["longshort"] + deal["operation"])
        .apply(lambda x: 1 if x in ["多头开仓", "空头平仓"] else -1)
        * deal["fill_volume"]
    )
    deal = deal[deal["fill_volume"] != 0]
    print(f'----- deal ------')
    print(deal.head(2))
    # print((deal[deal['fill_volume'] > 0].copy())[['ticker', 'fill_volume', 'fill_price']])
    deal_buy = deal[deal['fill_volume'] > 0].copy()[['ticker', 'fill_volume', 'fill_price']].rename(columns={'fill_volume': 'buy_volume', 'fill_price': 'buy_price'})
    deal_sell = deal[deal['fill_volume'] < 0].copy()[['ticker', 'fill_volume', 'fill_price']].rename(columns={'fill_volume': 'sell_volume', 'fill_price': 'sell_price'})

    # 对deal 进行汇总, price为加权平均
    deal_buy = deal_buy.groupby(['ticker']).agg(
        {'buy_volume': 'sum', 'buy_price': lambda x: np.average(x, weights=deal_buy.loc[x.index, "buy_volume"])}
    ).reset_index()
    # print((deal_buy['buy_volume']*deal_buy['buy_price']).sum())
    
    deal_sell = deal_sell.groupby(['ticker']).agg(
        {'sell_volume': 'sum', 'sell_price': lambda x: np.average(x, weights=deal_sell.loc[x.index, "sell_volume"])}
    ).reset_index()
    # print((deal_sell['sell_volume']*deal_sell['sell_price']).sum())

    hold['volume'] = hold['longshort'].map({'多头':1, '空头':-1}) * hold['volume']
    pre_hold['volume'] = pre_hold['longshort'].map({'多头':1, '空头':-1}) * pre_hold['volume']
    hold = hold[['ticker', 'volume']].groupby(['ticker']).sum().reset_index()
    pre_hold = pre_hold[['ticker', 'volume']].groupby(['ticker']).sum().reset_index()

    data = pd.merge(hold, pre_hold, on='ticker', how='outer', suffixes=('', '_pre'))
    data = pd.merge(data, price[['ticker', 'close', 'settle']], on='ticker', how='left' )
    data = pd.merge(data, pre_price[['ticker', 'close', 'settle']], on='ticker', how='left', suffixes=('', '_pre'))
    data = pd.merge(data, deal_buy, on='ticker', how='outer')
    data = pd.merge(data, deal_sell, on='ticker', how='outer')
    data = pd.merge(data, multiplier, on='ticker', how='left')

    # write_file(data, file_type='xls', dest_type='dav', dest_path=os.path.join(self.account_name, 'account', 'estimate_pnl_{}.xls'.format(self.trade_date)), index=False)
    data = data.fillna(0)
    estimate_pnl_by_close = (
        data["volume"].mul(data["close"]).mul(data["multiplier"]).sum()
        - data["volume_pre"].mul(data["close_pre"]).mul(data["multiplier"]).sum()
        - data["buy_volume"].mul(data["buy_price"]).mul(data["multiplier"]).sum()
        - data["sell_volume"].mul(data["sell_price"]).mul(data["multiplier"]).sum()
    )
    
    estimate_pnl_by_settle = (
        data["volume"].mul(data["settle"]).mul(data["multiplier"]).sum()
        - data["volume_pre"].mul(data["settle_pre"]).mul(data["multiplier"]).sum()
        - data["buy_volume"].mul(data["buy_price"]).mul(data["multiplier"]).sum()
        - data["sell_volume"].mul(data["sell_price"]).mul(data["multiplier"]).sum()
    )
    return estimate_pnl_by_close, estimate_pnl_by_settle


if __name__ == '__main__':
    if len(sys.argv) > 1:
        date = str(sys.argv[1])
    else:
        date = datetime.datetime.now().strftime('%Y%m%d')
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    mkt_data_file = f'trade_data/futures_mktdata/client_data_{date}.csv'


    hold_dir = f'accounts/中性对冲_申万期货/hold'
    pre_hold_path = os.path.join(hold_dir, f'hold_{pre_date}.csv')
    hold_path = os.path.join(hold_dir, f'hold_{date}.csv')

    deal_dir = f'accounts/中性对冲_申万期货/account'
    deal_path = os.path.join(deal_dir, f'futuredeal_{date}.csv')  # 可能不存在


    pre_hold = read_remote_file(pre_hold_path, src_type='zsdav')
    hold = read_remote_file(hold_path, src_type='zsdav')
    mkt_data = read_remote_file(mkt_data_file, src_type='zsdav')
    try:
        deal = read_remote_file(deal_path, src_type='zsdav')
    except FileNotFoundError:
        
        deal = pd.DataFrame(columns=['date', 'time', 'ticker', 'longshort', 'operation', 'fill_price', 'fill_volume', 'order_ref', 'deal_ref', 'file_type'])
        logger.warning(f'No deal file for {date} , replace with 0')
        



    price = mkt_data[['合约代码', '今收', '今结']].rename(columns={'合约代码': 'ticker', '今收': 'close', '今结': 'settle'})
    pre_price = mkt_data[['合约代码', '昨收', '昨结']].rename(columns={'合约代码': 'ticker', '昨收': 'close', '昨结': 'settle'})
    multiplier = mkt_data[['合约代码', '合约乘数']].rename(columns={'合约代码': 'ticker', '合约乘数': 'multiplier'})

    pnl_close, pnl_settle = calcu_estimate_pnl(pre_hold=pre_hold, hold=hold, pre_price=pre_price, price=price, deal=deal, multiplier=multiplier)

    print(f'pnl_close: {pnl_close}')
    print(f'pnl_settle: {pnl_settle}')