import logging
from qpython import qconnection


def kdb_frame_type_adjust(frame):
    columns = frame.columns
    dtypes = frame.dtypes
    if 'date' in columns:
        frame['date'] = frame['date'].astype(int)

    if 'code_int' in frame.columns:
        frame['code_int'] = frame['code_int'].map(lambda x: x.decode() if isinstance(x, bytes) else x)
        frame['code_int'] = frame['code_int'].astype(int)
    return frame


class Kdb:
    def __init__(self, ip, port, logger=None):
        if logger:
            self.logger = logger
        else:
            self.logger = logging.getLogger(__name__)

        self.ip = ip
        self.port = port
        self.q = qconnection.QConnection(host=self.ip, port=self.port, timeout=3.0)
        self.default_query_stock_kline_1day = '''select date:nActionDay, code_int:sym, pre_close:nPreClose%10000, 
            open:nOpen%10000, high:nHigh%10000, low:nLow%10000, close:nMatch%10000, volume:iVolume, 
            turnover:iTurnover, deal_amount:nNumTrades, status:nStatus from Market'''
        self.default_query_index_kline_1day = '''select date:nActionDay, code_int:(`$"." vs' string szWindCode)[;0], 
            pre_close:nPreCloseIndex%10000, open:nOpenIndex%10000, high:nHighIndex%10000, 
            low:nLowIndex%10000, close:nLastIndex%10000, volume:iTotalVolume*100, turnover:iTurnover*100
            from Index'''

    def query(self, query, pandas=True):
        result = None
        try:
            self.q.open()
            result = self.q(query, pandas=pandas)
        finally:
            self.q.close()
        result = kdb_frame_type_adjust(result)
        return result

    def get_real_stock_kline_1d(self, stock_list=None):
        query = self.default_query_stock_kline_1day
        if stock_list is not None and isinstance(stock_list, list):
            stock_list = [str(x).zfill(6) for x in stock_list]
            stock_list = '`' + '`'.join(stock_list)
            query = "{} where sym in {}".format(query, stock_list)

        result = self.query(query, pandas=True)
        # todo status, maybe useless
        # {0: 正常交易, 66: 整天停牌, 67: 全天收市 68:暂停交易 86:结束交易 88:停牌
        # result['is_open'] = result['volume'].map(lambda x: 1 if x > 0 else 0)
        result['status'] = result['status'].astype(int)
        result['is_open'] = result['status'].map(lambda x: 1 if x not in [66, 88] else 0)
        return result

    def get_real_index_kline_1d(self, stock_list=None):
        query = self.default_query_index_kline_1day
        if stock_list is not None and isinstance(stock_list, list):
            stock_list = [str(x).zfill(6) for x in stock_list]
            stock_list = '`' + '`'.join(stock_list)
            query = "{} where sym in {}".format(query, stock_list)

        result = self.query(query, pandas=True)
        return result