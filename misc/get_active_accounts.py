
from accounts_config.kf_accounts_basic import kf_accounts_dict
# %%
print(kf_accounts_dict.keys())
# %%
all_sub_accounts_list = []
for account in kf_accounts_dict.keys():
    print(account)
    if kf_accounts_dict[account].get('sub_accounts') is None:
        all_sub_accounts_list.append(account)
    elif len(kf_accounts_dict[account].get('sub_accounts')) == 0:
        all_sub_accounts_list.append(account)
    else:
        all_sub_accounts_list.extend(kf_accounts_dict[account]['sub_accounts'])

