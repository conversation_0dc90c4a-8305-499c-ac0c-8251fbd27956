import pandas as pd
import numpy as np
from loguru import logger
import datetime

pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.width', 180)

from misc.hold_position_to_orders import adjust_changes

def cal_changes(hold, target, pre_close, info={}):
    """
    hold:    symbol, hold_shares, direction, available_shares
    target:  symbol, target_shares, direction
    """
    changes = pd.concat([hold.set_index(['symbol', 'direction']), target.set_index(['symbol', 'direction'])], axis=1, join='outer')
    
    changes = changes.fillna(0)
    
    changes['order_shares'] = changes['target_shares'] - changes['hold_shares']
    changes = changes.reset_index()
    changes['order_shares_bf_adjust'] = changes['order_shares']
    changes = changes.apply(adjust_changes, axis=1)
    changes['order_shares'] = changes['order_shares'].astype(int)
    changes['symbol'] = changes['symbol'].astype(int)

    # check available_shares 是否大于 order_shares
    changes['cant_close_shares'] = changes['available_shares'] + changes['order_shares']
    mask_check_available = changes['cant_close_shares'] < 0
    if mask_check_available.any():
        cant_close = changes[mask_check_available].merge(pre_close[['symbol','close']], on='symbol', how='left')
        cant_close_value_long = (np.where(cant_close['direction']==1, cant_close['cant_close_shares'].abs(), 0) * cant_close['close']).sum()
        cant_close_value_short = (np.where(cant_close['direction']==-1, cant_close['cant_close_shares'].abs(), 0) * cant_close['close']).sum()
        logger.error(f'当前持仓可用不足: \n{changes[mask_check_available]}')
        logger.error(f'当前多头不能平仓: {cant_close_value_long:,.2f}, 空头不能平仓: {cant_close_value_short:,.2f}')

        changes.loc[mask_check_available, 'order_shares'] =  - (changes.loc[mask_check_available, 'available_shares']).abs()
    changes.drop(columns=['cant_close_shares'], inplace=True)
    # ------------------    

    changes = changes.set_index('symbol').merge(pre_close[['symbol','close']].set_index('symbol'), left_index=True, right_index=True, how='left')
    changes = changes.reset_index()
    
    
    # 过滤不交易股票
    # if account_name in ['test']:
    
    # non_buy_stock_lst = non_buy_stock_lst_all_accounts.copy() + non_buy_stock_by_account.get(account_name, []).copy()
    # non_sell_stock_lst = non_sell_stock_lst_all_accounts.copy() + non_sell_stock_by_account.get(account_name, []).copy()

    # if len(non_buy_stock_lst) > 0 and changes['symbol'].isin(non_buy_stock_lst).any():
    #     logger.warning(f'\n过滤不买入股票: \n{account_name}\n     {changes[(changes["symbol"].isin(non_buy_stock_lst)) & (changes["direction"] == 1) & (changes["order_shares"] > 0)]}\n\n')
    #     # changes = changes[~changes['symbol'].isin(non_buy_stock_lst)]
    #     changes.loc[(changes['symbol'].isin(non_buy_stock_lst)) & (changes["direction"] == 1) & (changes["order_shares"] > 0), 'order_shares'] = 0
        
    # if len(non_sell_stock_lst) > 0 and changes['symbol'].isin(non_sell_stock_lst).any():
    #     logger.warning(f'\n过滤不卖出股票: \n{account_name}\n     {changes[(changes["symbol"].isin(non_sell_stock_lst)) & (changes["direction"] == 1) & (changes["order_shares"] < 0)]}\n\n')
    #     # changes = changes[~changes['symbol'].isin(non_sell_stock_lst)]
    #     changes.loc[(changes['symbol'].isin(non_sell_stock_lst)) & (changes["direction"] == 1) & (changes["order_shares"] < 0), 'order_shares'] = 0
    
    # =========== 检查空头是否有反向交易
    # try get morning hold
    # if failed, use current hold and warns
    
    # hold - morning_hold
    # filter short hold direction
    # compare to changes direction
    
    # warn the t0 trades 
    # flag confirm to replace the t0 trades to 0    
    # morning_hold = get_stock_morning_hold(account_name)

    
    # ===========
    
    # num_filter_small_order_amount = trade_config.get('filter_small_order_amount', 0)
    # if num_filter_small_order_amount is not None and num_filter_small_order_amount > 0:
    #     changes['order_shares'] = np.where(changes['order_shares'].mul(changes['close'], axis=0).abs() < num_filter_small_order_amount,
    #                 0,
    #                 changes['order_shares']
    #     )
    # changes = changes.reset_index()
        
    # 记录 order info
    # mask_adjust = changes['order_shares'] != changes['order_shares_bf_adjust']
    # order_diffs = changes[mask_adjust].copy()
    # order_diffs['order_diffs'] = order_diffs['order_shares'] - order_diffs['order_shares_bf_adjust']
    # order_over_buy_shares = int(order_diffs[order_diffs['order_diffs'] > 0]['order_diffs'].sum())
    # order_over_sell_shares = int(order_diffs[order_diffs['order_diffs'] < 0]['order_diffs'].sum())
    # logger.warning('调整过的委托行数 :  {}'.format(len(order_diffs)))
    # logger.warning('调整后的少卖股数 :  {}'.format(order_over_buy_shares))
    # logger.warning('调整后的少买股数 :  {}'.format(order_over_sell_shares))
    
    changes['order_value'] = changes['order_shares'].mul(changes['close'], axis=0)
    
    # changes['exchange'] = changes['symbol'].apply(symbol_to_exchange_cn)
    # SH_netValue = changes['order_value'][changes['exchange'] == '上交所'].sum()
    # SZ_netValue = changes['order_value'][changes['exchange'] == '深交所'].sum()
    trade_size = changes['order_value'].abs().sum()  # long & short
    changes_len = (changes['order_shares'] !=0).sum()

    long = changes[changes['direction'] == 1]
    short = changes[changes['direction'] == -1]
    
    
    
    long_buy_size = long[long['order_shares'] > 0]['order_value'].sum()
    long_sell_size = long[long['order_shares'] < 0]['order_value'].sum()
    long_order_net = long['order_value'].sum()
    
    # short_buy_size = short[short['order_shares'] > 0]['order_value'].sum()
    # short_sell_size = short[short['order_shares'] < 0]['order_value'].sum()
    # short_order_net = short['order_value'].sum()
    
    long_hold_value = long['hold_shares'].abs().mul(long['close'], axis=0).sum()
    long_target_value = long['target_shares'].abs().mul(long['close'], axis=0).sum()
    
    long_buy_ratio = long_buy_size / long_hold_value if long_hold_value != 0 else 0
    long_sell_ratio = long_sell_size / long_hold_value if long_hold_value != 0 else 0
    # short_buy_ratio = short_buy_size / get_records_info(account_records, account_name, 'short_hold_value')
    # short_sell_ratio = short_sell_size / get_records_info(account_records, account_name, 'short_hold_value')
    # trade_size_ratio = trade_size / get_records_info(account_records, account_name, 'long_hold_value')
    
    info['long_hold_value'] = long_hold_value
    info['long_target_value'] = long_target_value
    info['long_buy_size'] = long_buy_size
    info['long_sell_size'] = long_sell_size
    info['long_buy_ratio'] = long_buy_ratio
    info['long_sell_ratio'] = long_sell_ratio
    info['long_order_net'] = long_order_net
    info['num_orders'] = changes_len

    info_print = {}
    info_print['持仓市值'] = '{:,.0f}'.format(long_hold_value)
    info_print['目标市值'] = '{:,.0f}'.format(long_target_value)
    info_print['多头买入'] = '{:,.0f}'.format(long_buy_size)
    info_print['多头卖出'] = '{:,.0f}'.format(long_sell_size)
    info_print['目标净买入市值'] = '{:,.0f}'.format(long_order_net)
    info_print['多头买入比例'] = '{:.2%}'.format(long_buy_ratio)
    info_print['多头卖出比例'] = '{:.2%}'.format(long_sell_ratio)
    info_print['总单数'] = '{:.0f}'.format(changes_len)
    print(pd.DataFrame.from_dict(info_print, orient='index', columns=['值']).to_string(header=False))
    # print(tabulate(info_print.items(), headers='keys', tablefmt='psql'))
    
    # print(f'{"持仓市值:":<12} {long_hold_value:,.2f}')
    # print(f'{"目标市值:":<12} {long_target_value:,.2f}')
    # print(f'{"多头买入:":<12} {long_buy_size:,.2f}')
    # print(f'{"多头卖出:":<12} {long_sell_size:,.2f}')
    # print(f'{"目标净买入市值:":<12} {long_order_net:,.2f}')
    # print(f'{"多头买入比例:":<12} {long_buy_ratio:.2%}')
    # print(f'{"多头卖出比例:":<12} {long_sell_ratio:.2%}')
    # print(f'{"总订单数:":<12} {changes_len}')
    
    
    # 如果要出计算过程, 可以打开下面的注释
    # write_file(changes, file_type='xls', dest_type='dav', dest_path='changes.xls', index=False)
    # update_records_info(account_records, account_name, 'long_order_net', long_order_net)
    # update_records_info(account_records, account_name, 'short_order_net', short_order_net)
    # update_records_info(account_records, account_name, 'SH_netValue', SH_netValue)
    # update_records_info(account_records, account_name, 'SZ_netValue', SZ_netValue)
    # update_records_info(account_records, account_name, 'trade_size', trade_size)
    # update_records_info(account_records, account_name, 'long_buy_size', long_buy_size)
    # update_records_info(account_records, account_name, 'long_sell_size', long_sell_size)
    # update_records_info(account_records, account_name, 'long_buy_ratio', long_buy_ratio)
    # update_records_info(account_records, account_name, 'long_sell_ratio', long_sell_ratio)
    
    # update_records_info(account_records, account_name, 'short_buy_size', short_buy_size)
    # update_records_info(account_records, account_name, 'short_sell_size', short_sell_size)
    # update_records_info(account_records, account_name, 'short_buy_ratio', short_buy_ratio)
    # update_records_info(account_records, account_name, 'short_sell_ratio', short_sell_ratio)
    
    # update_records_info(account_records, account_name, 'num', changes_len)
    # # update_records_info(account_records, account_name, 'sell_shares_reduced', order_over_buy_shares)
    # # update_records_info(account_records, account_name, 'buy_shares_reduced', order_over_sell_shares)
    # product_name = trade_config.get('product_name')
    # update_records_info(product_records, product_name, 'product_long_buy_size',
    #     (product_records.get(product_name, {}).get('product_long_buy_size', 0) + long_buy_size))
    # update_records_info(product_records, product_name, 'product_long_sell_size',
    #     (product_records.get(product_name, {}).get('product_long_sell_size', 0) + long_sell_size))
    
    changes = changes[changes['order_shares'] != 0]
    return changes[['symbol', 'order_shares', 'direction']], info

