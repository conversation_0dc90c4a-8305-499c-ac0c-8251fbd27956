import os
import paramiko
import sshtunnel
from loguru import logger
from misc.zzw_tun import tun_kf_trade_dict

private_key_path = '~/.ssh/id_rsa'
private_key = paramiko.RSAKey.from_private_key_file(private_key_path)

ssh_conf_dav = {
    # 'hostname': '*************',
    'hostname': '**********',
    'username': 'trade',
    'port': 2022,
    'pkey': private_key
}

ssh_conf_outer = {
    'hostname': '**************',
    'username': 'root',
    'port': 22,
    'pkey': private_key
}

ssh_conf_inner = {
    'hostname': '************',
    'username': 'root',
    'port': 22,
    'pkey': private_key
}

ssh_conf_trade = {
    'hostname': '127.0.0.1',
    'username': 'admin',
    'port': 16041,
    'pkey': private_key
}

ssh_conf_gtapi = {
    'hostname': '**********',
    'username': 'api',
    'port': 2022,
    'pkey': private_key
}


ssh_client_dav = paramiko.SSHClient()
ssh_client_dav.set_missing_host_key_policy(paramiko.AutoAddPolicy())
ssh_client_dav.connect(**ssh_conf_dav)
sftp_dav = ssh_client_dav.open_sftp()

ssh_client_outer = paramiko.SSHClient()
ssh_client_outer.set_missing_host_key_policy(paramiko.AutoAddPolicy())
ssh_client_outer.connect(**ssh_conf_outer)
sftp_outer = ssh_client_outer.open_sftp()

ssh_client_inner = paramiko.SSHClient()
ssh_client_inner.set_missing_host_key_policy(paramiko.AutoAddPolicy())
ssh_client_inner.connect(**ssh_conf_inner)
sftp_inner = ssh_client_inner.open_sftp()

ssh_client_trade = paramiko.SSHClient()
ssh_client_trade.set_missing_host_key_policy(paramiko.AutoAddPolicy())
ssh_client_trade.connect(**ssh_conf_trade)
sftp_trade = ssh_client_trade.open_sftp()

ssh_client_gtapi = paramiko.SSHClient()
ssh_client_gtapi.set_missing_host_key_policy(paramiko.AutoAddPolicy())
ssh_client_gtapi.connect(**ssh_conf_gtapi)
sftp_gtapi = ssh_client_gtapi.open_sftp()


# 暂时不用的
def warp_tunnel_ssh():
    sshtunnel.SSHTunnelForwarder(**tun_kf_trade_dict).start()
    ssh_client_trade = paramiko.SSHClient()
    ssh_client_trade.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh_client_trade.connect(**ssh_conf_trade)
    sftp_trade = ssh_client_trade.open_sftp()
    return sftp_trade


class SFTPclient:
    def __init__(self, remote):
        if remote == 'dav':
            self.remote = 'dav'
            self.sftp_client = sftp_dav
            # self.remote_prefix = '/sftpgo/sftpgodata/tradefiles/'
            self.remote_prefix = '/'
        elif remote == 'outer':
            self.remote = 'outer'
            self.sftp_client = sftp_outer
            self.remote_prefix = '/data/ftp/'
        elif remote == 'inner':
            self.remote = 'inner'
            self.sftp_client = sftp_inner
            self.remote_prefix = '/home/<USER>/signal/product'
        elif remote == 'trade':
            self.remote = 'trade'
            self.sftp_client = sftp_trade
            self.remote_prefix = 'C:'
        elif remote == 'gtapi':
            self.remote = 'gtapi'
            self.sftp_client = sftp_gtapi
            self.remote_prefix = '/product'
            
        else:
            raise ValueError('remote should be one of dav, outer, inner, gtapi')

    def close(self):
        self.sftp_client.close()
        logger.info('sftp client closed: {}'.format(self.remote))

    def put(self, local_path, remote_path):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        logger.info('try copying to {} \nsource: {} \ndest: {}'.format(self.remote, local_path, remote_path))
        self.sftp_client.put(local_path, remote_path)
        logger.info('copied to remote path finished: {}'.format(remote_path))

    def get(self, remote_path, local_path):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        logger.info('try copying from {} \nsource: {} \ndest: {}'.format(self.remote, remote_path, local_path))
        self.sftp_client.get(remote_path, local_path)
        logger.info('copied from remote path finished: {}'.format(remote_path))
        
    def listdir(self, remote_path=''):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        logger.info('try listing {} \ndirectory: {}'.format(self.remote, remote_path))
        return self.sftp_client.listdir(remote_path)
    
    def mkdir(self, remote_path=''):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        logger.info('try making directory {} \ndirectory: {}'.format(self.remote, remote_path))
        self.sftp_client.mkdir(remote_path)
        logger.info('mkdir finished: {}'.format(remote_path))
        
    def chdir(self, remote_path=''):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        logger.info('try changing directory {} \ndirectory: {}'.format(self.remote, remote_path))
        self.sftp_client.chdir(remote_path)
        logger.info('chdir finished: {}'.format(remote_path))

sftp_clent_dav = SFTPclient('dav')
sftp_clent_outer = SFTPclient('outer')
sftp_clent_inner = SFTPclient('inner')
sftp_clent_trade = SFTPclient('trade')
sftp_clent_gtapi = SFTPclient('gtapi')

#%%

