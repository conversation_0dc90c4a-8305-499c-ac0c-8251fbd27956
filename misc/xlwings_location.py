import xlwings as xws

def range_first_loc(R : xws.main.Range):
    return R.row, R.column

def loc_keyword(sheet, row_or_column, rng: xws.main.Range, keyword):
    try:
        if row_or_column == 'row':
            cell_loc = rng.api.Find(What=keyword, After=sheet.api.Cells(*range_first_loc(rng)), 
                                   LookAt=xws.constants.LookAt.xlWhole,
                                   LookIn=xws.constants.FindLookIn.xlFormulas, 
                                   SearchDirection=xws.constants.SearchDirection.xlNext, 
                                   MatchCase=False).Row

        elif row_or_column == 'col':
            cell_loc = rng.api.Find(What=keyword, After=sheet.api.Cells(*range_first_loc(rng)), 
                                   LookAt=xws.constants.LookAt.xlWhole,
                                   LookIn=xws.constants.FindLookIn.xlFormulas, 
                                   SearchDirection=xws.constants.SearchDirection.xlNext, 
                                   MatchCase=False).Column
    except:
        cell_loc = -9999
    return cell_loc

def loc_two_layers_col(sheet, row_y1, keyword_1, row_y2, keyword_2):
    # 因为第二行的关键字有重复, 所以通过上一行, 也就是第一行的关键字来定位, 第一行的关键字很可能是合并单元格
    # 输入第一行的行数, 关键字, 第二行的行数, 关键字, 返回所在关键字的列数

    # 输入第一行的行数, 关键字, 得到关键字所在的列数
    row_y1 = 1
    col_x1 = loc_keyword(sheet, 'col', sheet[row_y1-1, 0:], keyword_1)
    print('col_x', col_x1)
    
    # 判断这个单元格是不是合并单元格, 是的话,计算本单元格和下个单元格的位置
    # flag_merge = sheet[row_y1-1, col_x1-1].api.MergeCells
    # if flag_merge:
    cell_col_count = sheet[row_y1-1, col_x1-1].api.MergeArea.Columns.Count
    print('cell_col_count', cell_col_count)

    
    # 根据第二个行数, 第一行的列数区间, 和第二行的关键字, 返回第二行关键字所在的列数
    row_y2 = 3
    layer2_rng = sheet[row_y2-1, (col_x1-1):(col_x1-1 + cell_col_count)]
    col_x2 = loc_keyword(sheet, 'col', layer2_rng, keyword_2)
    print('col_x2', col_x2)
    # 最终通过第二个行数索引到的列数, 和另外指定的行数, 定位到目标单元格
    
    return col_x2

def loc_date_row(sheet, col, keyword):
    last_row = sheet[sheet.cells.last_cell.row-1, 0].end('up').row

    my_date_handler = lambda year, month, day, **kwargs: "%04i%02i%02i" % (year, month, day)
    for row_index in range(last_row):
        if keyword == sheet[row_index, col-1].options(dates=my_date_handler).value:
            row = row_index + 1
            break
    else:
        row = -9999
    return row
