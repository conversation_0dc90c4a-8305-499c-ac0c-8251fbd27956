import pandas as pd
# from typing import Literal
from loguru import logger
import re
try:
    from typing import Literal
except ImportError:
    from typing_extensions import Literal

# from misc.standardize.kafangalgo_dbf import *
# from misc.standardize.zhongxin_cats import *
# from misc.standardize.ax_onequant import *
# from misc.standardize.wuxianyi import standardize_wuxianyi_deal
# from misc.standardize.kafangatx_dbf import standardize_order_kafangatx_dbf, standardize_deal_kafangatx_dbf
# from misc.standardize.kafangato_dbf import standardize_order_kafangato_dbf, standardize_deal_kafangato_dbf, standardize_hold_kafangato_dbf
from misc import utils
# from misc.utils import to_datetime_formmats
# from misc.utils import timetic, timetoc
from .standardize_dev.stand_utils import std_filter_ticker_asint
# logger.add('logs/timeformat.log')


from misc.standardize_dev import (
    ax_onequant,
    gf_jinshida,
    guoxin_gtrade,
    kfato_dbf,
    kfatx_dbf,
    yinhe_kfalgo_dbf,
    zhongxin_cats,
    zhongxin_cats_credit,
    zheshang_kfalgo_dbf,
    xuntou,
    zhaoshang_master,
    dfemc,
    matic_otc,
    tf_yulia_dbf,
    hxtrade,
    touyitong,
    
)

# from misc.standardize_dev import gf_jinshida.std_hold
# from misc.standardize_dev.guoxin_gtrade import std_hold
# from misc.standardize_dev.kfato_dbf import std_hold
# from misc.standardize_dev.kfatx_dbf import std_hold
# from misc.standardize_dev.yinhe_kfalgo_dbf import std_hold
# from misc.standardize_dev.zhongxin_cats import std_hold
# from misc.standardize_dev.zheshang_kfalgo_dbf import std_hold
# from misc.standardize_dev.xuntou import std_hold
# from misc.standardize_dev.zhaoshang_master import std_hold


hold_standard_func = {
    'ax_onequant':        ax_onequant.std_hold,
    'guangfa_jsd':        gf_jinshida.std_hold,
    'gtrade':             guoxin_gtrade.std_hold,
    'zhongxin_cats':      zhongxin_cats.std_hold,
    'zhongxin_cats_credit': zhongxin_cats_credit.std_hold_credit,
    'kafangato_dbf':      kfato_dbf.std_hold,
    'kafangatx_dbf':      kfatx_dbf.std_hold,
    'kafangalgo_dbf':     yinhe_kfalgo_dbf.std_hold,
    'zheshang_kfalgo_dbf': zheshang_kfalgo_dbf.std_hold,
    'zhaoshang_master':    zhaoshang_master.std_hold,
    'xt_qmt':              xuntou.std_hold,
    'xt_pb':               xuntou.std_hold,
    'dfemc'         :      dfemc.std_hold,
    'matic_otc'     :      matic_otc.std_hold,
    'tf_yulia_dbf'    :    tf_yulia_dbf.std_hold,
    'hxtrade'        :     hxtrade.std_hold,
    'touyitong'      :     touyitong.std_hold,
}

def standardize_hold(df: pd.DataFrame, **kwargs):
    if df.empty:
        return pd.DataFrame(columns=['ticker', 'volume', 'available_volume'])

    if 'ticker' in df.columns and 'volume' in df.columns:
        df = df[['ticker', 'volume', 'available_volume']]
        df, remainder = std_filter_ticker_asint(df, src_suffix='')
        df = df[df['volume'] != 0]
        return df
    
    client_type = kwargs.get('client_type', 'xt_qmt')
    stand_method = hold_standard_func[client_type]
    
    df = stand_method(df)
    # df = std_filter_ticker_asint(df)
    # if (df['ticker'].dtype == 'int') or (df['ticker'].dtype == 'float') and (not df.empty):
    #     # 过滤非股票
    #     df = df[df['ticker'].astype(str).str.zfill(6).apply(lambda x: re_find('^0|^3|^6', x)).astype(bool) ]
    #     df['ticker'] = df['ticker'].astype(int)
    # elif df['ticker'].dtype == 'object' and (not df.empty):
    #     # 过滤非股票
    #     df = df[ df['ticker'].str.zfill(6).apply(lambda x: re_find('^0|^3|^6', x)).astype(bool) ]
    #     df['ticker'] = df['ticker'].astype(int)
    # elif df.empty:
    #     logger.info('hold df is empty')
    # else:
    #     print(df.head(2))
    #     print(df['ticker'].dtype)
    #     raise TypeError('ticker type error')
    # print(df.head(2))
    # print(df.columns)
    
    df = df[['ticker', 'volume', 'available_volume']]
    df = df[df['volume'] != 0]
    df.reset_index(drop=True, inplace=True)
    return  df
        
        
    
    
    
    # if kwargs.get('client_type', None) == 'kafangalgo_dbf':
    #     df = standardize_hold_kafangalgo_dbf(df)
    # elif kwargs.get('client_type', None) == 'ax_onequant':
    #     df = standardize_hold_ax_onequant(df)
    #     return df
    
    # if '交易对代码' in df.columns:
    #     df['交易对代码'] = df['交易对代码'].apply(lambda x: re.sub('\.XSHE@CNY$|\.XSHG@CNY$|\.CCFX@CNY', '', x))

    
    # # zhongxin cats hold, longshort optional
    # if kwargs.get('client_type', None) == 'zhongxin_cats':
    #     df = standardize_hold_zhongxin_cats(df)
    
    # if kwargs.get('client_type', None) == 'kafangato_dbf':
    #     df = standardize_hold_kafangato_dbf(df)
    
    # for col in df.columns:
    #     if df[col].dtype == 'object':
    #         df[col] = df[col].str.lstrip("=\"")
    #         df[col] = df[col].str.rstrip("\"")
    #         df[col] = df[col].str.lstrip("\\t")
            
    #         if col == 'hold_shares':
    #             df[col] = df[col].str.replace(',', '').astype(float).astype(int)
    #         if col == 'hold_value':
    #             df[col] = df[col].str.replace(',', '').astype(float)

    # if df['ticker'].dtype == 'object' and (not df.empty):
    #     if (df['ticker'].str.endswith('.SZ') | df['ticker'].str.endswith('.SH')).any():
    #         df[['ticker', 'exchange']] = df['ticker'].str.split('.', expand=True)
            
    #     # 过滤期货持仓
    #     # df = df[~df['ticker'].apply(re_find, args=('^IC|^IF|^IH|^IM',)).astype(bool)]
    #     df = df[df['ticker'].apply(lambda x: False if (x.startswith('IC') or x.startswith('IF') or x.startswith('IH')) else True)]

    # # 过滤持仓为0
    # df = df[df['hold_shares'] != 0]
    # df = df.sort_values('ticker').reset_index()

    # if df['ticker'].dtype != 'int' and (not df.empty):
    #     df = df[df['ticker'].apply(lambda x: re_find('\d{6}', x)).astype(bool)]
    #     df['ticker'] = df['ticker'].astype(int)
        
    # if 'hold_value' in df.columns:
    #     df = df[['ticker', 'hold_shares', 'hold_value']]
    #     df.columns = ['代码', '持仓量', '市值']
    # elif 'last_price' in df.columns:
    #     df['hold_value'] = df['hold_shares'] * df['last_price']
    #     df = df[['ticker', 'hold_shares', 'hold_value']]
    #     df.columns = ['代码', '持仓量', '市值']
    # else:
    #     df = df[['ticker', 'hold_shares']]
    #     df.columns = ['代码', '持仓量']
    
    # return df

def standardize_marginhold(df: pd.DataFrame, **kwargs):
    # print(kwargs)
    if kwargs.get('client_type') == 'zhongxin_cats':
        df = standardize_marginhold_zhongxin_cats(df)
    
    if '交易对代码' in df.columns:
        df['交易对代码'] = df['交易对代码'].apply(lambda x: re.sub('\.XSHE@CNY$|\.XSHG@CNY$|\.CCFX@CNY', '', x))
    
    df = df.rename(columns={
            '代码': 'ticker', 
            '标的代码': 'ticker', 
            '证券代码': 'ticker', 
            'StockCode': 'ticker',
            'SYMBOL': 'ticker',
            '交易对代码': 'ticker',
            '合约代码': 'ticker',
            
            '持仓方向' : 'longshort',
            'Direction' : 'longshort',
            '多空方向' : 'longshort',
            '多空' : 'longshort',

            'volume': 'hold_volume',
            '持仓量': 'hold_volume',
            '持仓': 'hold_volume',
            '当前持仓': 'hold_volume',
            '持仓数量': 'hold_volume',
            '当前数量': 'hold_volume',
            '当前拥股': 'hold_volume',
            'Volume': 'hold_volume',
            'CURRENTQ': 'hold_volume',
            '总持仓量': 'hold_volume',

            '可用数量': 'available_volume',
            '可平数量': 'available_volume',
            '可平量': 'available_volume',
            '可用持仓': 'available_volume',
            'CanUseVol': 'available_volume',
            
            '成本价': 'cost_price',
            '开仓均价': 'cost_price',
            
            '最新价': 'last_price',
            'LastPrice': 'last_price',
            
            '当前市值': 'hold_value',
            '市值': 'hold_value',
            '市值（CNY）': 'hold_value',
            '持仓市值': 'hold_value',
            'MarketValue': 'hold_value',
            
            '市场代码': 'mkt_code',
            'EXCHANGE': 'mkt_code',
            'ExchangeID': 'mkt_code',
            
            'ExchangeName': 'mkt_cn',
            })
    
    # 过滤持仓为0
    df = df[df['hold_volume'] != 0]
    df = df.sort_values('ticker').reset_index()

    if df['ticker'].dtype == 'object' and (not df.empty):
        if (df['ticker'].apply(lambda x: re_find('\.SH$|\.SZ$|\.CFFEX$', x))).astype(bool).any():
            df[['ticker', 'exchange']] = df['ticker'].str.split('.', expand=True)

    df['longshort'] = df['longshort'].apply(replace_marginhold_longshort)
    cols = ['ticker', 'longshort', 'hold_volume', 'available_volume']
    if 'available_volume' not in df.columns:
        df['available_volume'] = 0
        
    df['hold_volume'] = df['hold_volume'].astype(int)
    df = df[cols].rename(columns={
        'ticker': '代码',
        'longshort': '多空状态',
        'hold_volume': '持仓量',
        'available_volume': '可用数量'
    })

    return df

order_standard_func = {    
    'ax_onequant':        ax_onequant.std_order,
    'guangfa_jsd':        gf_jinshida.std_order,
    'gtrade':             guoxin_gtrade.std_order,
    'zhongxin_cats':      zhongxin_cats.std_order,
    'zhongxin_cats_credit': zhongxin_cats_credit.std_order,
    'kafangato_dbf':      kfato_dbf.std_order,
    'kafangatx_dbf':      kfatx_dbf.std_order,
    'kafangalgo_dbf':     yinhe_kfalgo_dbf.std_order,
    'zheshang_kfalgo_dbf': zheshang_kfalgo_dbf.std_order,
    'zhaoshang_master':    zhaoshang_master.std_order,
    'xt_qmt':              xuntou.std_order,
    'xt_pb':               xuntou.std_order,
    'dfemc'          :     dfemc.std_order,
    'matic_otc'      :     matic_otc.std_order,
    'tf_yulia_dbf'   :     tf_yulia_dbf.std_order,
    'hxtrade'        :     hxtrade.std_order,
    'touyitong'      :     touyitong.std_order,
}


def standardize_order(df: pd.DataFrame, date, **kwargs):
    if df.empty:
        return pd.DataFrame(columns=['ticker', 
                                     'BS_flag', 
                                     'order_price', 
                                     'order_volume', 
                                     'fill_price', 
                                     'fill_volume', 
                                     'order_status',
                                     'order_ref',
                                     'date', 
                                     'time', 
                                     'algorithm'])
    
    client_type = kwargs.get('client_type', 'xt_qmt')
    if client_type is not None:
        stand_method = order_standard_func[client_type]
        df = stand_method(df, date, **kwargs)
    # check order status 是不是全符合
    
    # df = std_filter_ticker_asint(df)
    if kwargs.get('algorithm') is not None:        
        df['algorithm'] = kwargs.get('algorithm')
    elif 'algorithm' not in df.columns:
        df['algorithm'] = ''
    df = df[[
            "ticker", 
            "BS_flag", 
            "order_price", 
            "order_volume", 
            "fill_price", 
            "fill_volume",
            
            "order_status", 
            "order_ref", 
            "date", 
            "time", 
            "algorithm"
    ]]
    if not (df['BS_flag'].isin(['买入', '卖出', '卖空', '平空']).all()):
        print(df['BS_flag'].unique())
        raise ValueError('BS_flag should be 买入 or 卖出 or 卖空 or 平空')
    if not (df['order_status'].isin(['已成', '已撤', '部成', '废单', '已报', '等待报单', '过期', '待撤', '暂停']).all()):
        raise ValueError('order_status should be 已成, 已撤, 部成, 废单, 已报, 等待报单, 过期, 待撤, 暂停')
    if df['order_ref'].isna().any():
        raise ValueError('order_ref 不能为空')
    df.reset_index(drop=True, inplace=True)
    return df


def standardize_order_old(df: pd.DataFrame, date, **kwargs):
    # tic = timetic()
    if kwargs.get('client_type', None) == 'kafangalgo_dbf':
        df = standardize_order_kafangalgo_dbf(df, date)
    elif kwargs.get('client_type', None) == 'ax_onequant':
        df = standardize_order_ax_onequant(df, date)
        return df
    
    # if kwargs.get('client_type') == 'kafangatx_dbf':
    #     return _standardize_order_kafangatx(df, date)
    
    if 'TransactTime' in df.columns:
        # split time into date and time
        if not df.empty:
            df['TransactTime'] = pd.to_datetime(df['TransactTime'], format='%Y%m%dT%H%M%S').dt.strftime('%Y%m%d %H:%M:%S')
            df[['date', 'time']] = df['TransactTime'].str.split(' ', expand=True)
        else:
            df['date'] = ''
            df['time'] = ''
    
    if '交易对代码' in df.columns:
        df['交易对代码'] = df['交易对代码'].apply(lambda x: re.sub('\.XSHE@CNY$|\.XSHG@CNY$|\.CCFX@CNY', '', x))
    
    df = df.rename(
        columns={"委托时间":"time",
                "时间":"time",
                "OrderTime":"time",
                
                "代码":"ticker",
                "证券代码":"ticker",
                "标的代码":"ticker",
                "Symbol":"ticker",
                "交易对代码":"ticker",
                "StockCode":"ticker",
                
                "买卖标记":"BS_flag",
                "交易":"BS_flag",   # cats
                "委托方向":"BS_flag",
                "交易业务":"BS_flag",
                "方向":"BS_flag",
                "买卖方向":"BS_flag",
                "Side":"BS_flag",
                "BSTag":"BS_flag",
                "开平标志":"BS_flag",
                
                "委托量":"order_volume",  # cats
                "委托数量":"order_volume",
                "OrderQty":"order_volume",
                "OrderVol":"order_volume",
                "意向名义数量":"order_volume",
                
                "委托状态":"order_status",
                "交易状态":"order_status",
                "OrdStatus":"order_status",
                "OrderStat":"order_status",
                "意向状态":"order_status",
                "状态":"order_status",   # cats
                
                "委托价":"order_price",
                "委托价格":"order_price",
                "价格":"order_price",
                "委托价格(港股通单位为港币)":"order_price",
                "Price":"order_price",
                "OrderPrice":"order_price",
                "意向价格":"order_price",
                
                "成交数量":"fill_volume",
                "成交量":"fill_volume",  #cats
                "已成交数量":"fill_volume",
                "CumQty":"fill_volume",
                "BizNum":"fill_volume",
                
                "成交均价(港股通单位为港币)":"fill_price",
                "成交均价":"fill_price",
                "AvgPx":"fill_price",
                "BizPrice":"fill_price",
                
                "成交金额":"fill_amount",
                
                
                "合同编号":"order_ref",
                "委托编号":"order_ref",
                "委托序号":"order_ref",
                "OrderId":"order_ref",
                "意向编号":"order_ref",
                "订单号":"order_ref",   # cats 
                
                "委托日期":"date",
                "OrderDate":"date",
                
                "投资备注":"remark",
                "OrdType":"remark",
                "OrderMark":"remark",

                "算法" : "algorithm",

                # xt qmt
                '委托时间'       : 'time',
                '证券代码'       : 'ticker',
                '买卖标记'       : 'BS_flag',
                '委托状态'       : 'order_status',
                '委托量'         : 'order_volume',
                '委托价格'       : 'order_price',
                '成交数量'        : 'fill_volume',
                '成交均价'        : 'fill_price',
                '合同编号'       : 'order_ref',
                '委托日期'       : 'date',
                
                # kafang atx
                'TransTime'     : 'time',
                'Symbol'        : 'ticker',
                'Side'          : 'BS_flag',
                'OrdStatus'     : 'order_status',
                'OrderQty'      : 'order_volume',
                'Price'         : 'order_price',
                'CumQty'        : 'fill_volume',
                'AvgPx'         : 'fill_price',
                'OrderId'       : 'order_ref',
                'date'          : 'date',
                
                # kafang ato
                # 同 kafang atx
        }
    )
    # tic = timetoc(tic)
    # kafang atx client
    if kwargs.get('client_type', None) == 'kafangatx_dbf':
        df = standardize_order_kafangatx_dbf(df, date)
    
    if kwargs.get('client_type', None) == 'kafangato_dbf':
        df = standardize_order_kafangato_dbf(df, date)
 
    # tic = timetoc(tic)
    for col in df.columns:
        if df[col].dtype == 'object':
            df[col] = df[col].str.lstrip("=\"")
            df[col] = df[col].str.rstrip("\"")
            
            if col in ['order_volume', 'fill_volume']:
                df[col] = df[col].str.replace(',', '').astype(float).astype(int)
            if col in ['order_price', 'fill_price']:
                df[col] = df[col].str.replace(',', '').astype(float)
    # tic = timetoc(tic)
    # print(df.head(2))
    if df['ticker'].dtype == 'object' and (df['ticker'].str.endswith('.SZ') | df['ticker'].str.endswith('.SH')).any():
        df[['ticker', 'exchange']] = df['ticker'].str.split('.', expand=True)
        
    # tic = timetoc(tic)
    # 过滤期货流水
    if df['ticker'].dtype == 'object' and (not df.empty):
        # df = df[~df['ticker'].apply(re_find, args=('^IC|^IF|^IH|^IM',))]
        df = df[df['ticker'].apply(lambda x: False if (x.startswith('IC') or x.startswith('IF') or x.startswith('IH')) else True)] 
    
    # tic = timetoc(tic)
    df['BS_flag'] = df['BS_flag'].apply(replace_bs_flag)
    df = df[(df['BS_flag'] == '买入') | (df['BS_flag'] == '卖出')]
    if df['order_ref'].dtype != 'object':
        raise TypeError('委托编号/order_ref should be str type')
    
    # df['time'] = pd.to_datetime(df['time']).str.strftime("%H:%M:%S")
    if 'date' not in df.columns:
        logger.warning('日期date 不存在, 添加date 为 {}'.format(date))
        df['date'] = date

    # if not df.empty:
    #     logger.info('order date: {}'.format(df['date'][0]))
    # df['date'] = pd.to_datetime(df['date'].astype(str))
    df['date'] = utils.to_datetime_formmats(df['date'].astype(str), source_type='date')
    # print(df['date'].head(2))
    # if not df.empty:
        # logger.info('order time: {}'.format(df['time'].iloc[-1]))
    print(df['time'].head(1))
    df['time'] = utils.to_datetime_formmats(df['time'], source_type='time')
    df['date'] = df['date'].apply(lambda x: x.strftime("%Y%m%d"))
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    
    if "algorithm" not in df.columns:
        df["algorithm"] = ""

    df['order_status'] = df['order_status'].apply(replace_order_status)
    
    if 'fill_price' not in df.columns:
        df['fill_price'] = (df['fill_amount'] / df['fill_volume']).round(4)
        df = df.fillna(0)

    df = df.reset_index()
    df = df[["date", 
                   "time", 
                   "order_ref", 
                   "ticker", 
                   "BS_flag", 
                   "order_price", 
                   "order_volume", 
                   "fill_price", 
                   "fill_volume",
                   "order_status", 
                   "algorithm"]].sort_values("time")
    df.columns = ['委托日期',
                    '委托时间',
                    '合同编号',
                    '代码',
                    '买卖标记',
                    '委托价格',
                    '委托数量',
                    '成交均价',
                    '成交数量',
                    '委托状态',
                    '算法']
    return df
    
deal_standard_func = {    
    'ax_onequant':        ax_onequant.std_deal,
    'guangfa_jsd':        gf_jinshida.std_deal,
    'gtrade':             guoxin_gtrade.std_deal,
    'zhongxin_cats':      zhongxin_cats.std_deal,
    'zhongxin_cats_credit': zhongxin_cats_credit.std_deal,
    'kafangato_dbf':      kfato_dbf.std_deal,
    'kafangatx_dbf':      kfatx_dbf.std_deal,
    'kafangalgo_dbf':     yinhe_kfalgo_dbf.std_deal,
    'zheshang_kfalgo_dbf': zheshang_kfalgo_dbf.std_deal,
    'zhaoshang_master':    zhaoshang_master.std_deal,
    'xt_qmt':              xuntou.std_deal,
    'xt_pb':               xuntou.std_deal,
    'dfemc'           :    dfemc.std_deal,
    'matic_otc'       :    matic_otc.std_deal,
    'tf_yulia_dbf'       : tf_yulia_dbf.std_deal,
    'hxtrade'        :     hxtrade.std_deal,
    'touyitong'      :     touyitong.std_deal,
}
    
    
def standardize_deal(df: pd.DataFrame, date, **kwargs):
    if df.empty:
        return pd.DataFrame(columns=['ticker', 'BS_flag', 'fill_price', 'fill_volume', 'date', 'time', 'order_ref', 'deal_ref', 'algorithm'])
    
    client_type = kwargs.get('client_type', 'xt_qmt')
    try:
        stand_method = deal_standard_func[client_type]
    except KeyError:
        
        print( '\nskip\n' )
        return pd.DataFrame(columns=['ticker', 'BS_flag', 'fill_price', 'fill_volume', 'date', 'time', 'order_ref', 'deal_ref', 'algorithm'])
    
    df = stand_method(df, date, **kwargs)
    # check order status 是不是全符合
    
    # df = std_filter_ticker_asint(df)
    if kwargs.get('algorithm') is not None:        
        df['algorithm'] = kwargs.get('algorithm')
    elif 'algorithm' not in df.columns:
        df['algorithm'] = ''
    df = df[[
            "ticker",
            "BS_flag", 
            "fill_price", 
            "fill_volume",

            "date", 
            "time", 
            "order_ref",
            "deal_ref",
            "algorithm"
    ]]
    if not (df['BS_flag'].isin(['买入', '卖出', '卖空', '平空']).all()):
        print(df['BS_flag'].unique())
        raise ValueError('BS_flag should be 买入 or 卖出 or 卖空 or 平空')
    if df['order_ref'].isna().any():
        raise ValueError('order_ref 不能为空')
    if df['deal_ref'].isna().any():
        raise ValueError('deal_ref 不能为空')
    df.reset_index(drop=True, inplace=True)
    return df

deal_standard_minimal_func = {    
    'ax_onequant':        ax_onequant.std_deal_minimal,
    'guangfa_jsd':        gf_jinshida.std_deal_minimal,
    'gtrade':             guoxin_gtrade.std_deal_minimal,
    'zhongxin_cats':      zhongxin_cats.std_deal_minimal,
    'zhongxin_cats_credit': zhongxin_cats_credit.std_deal_minimal,
    'kafangato_dbf':      kfato_dbf.std_deal_minimal,
    'kafangatx_dbf':      kfatx_dbf.std_deal_minimal,
    'kafangalgo_dbf':     yinhe_kfalgo_dbf.std_deal_minimal,
    'zheshang_kfalgo_dbf': zheshang_kfalgo_dbf.std_deal_minimal,
    'zhaoshang_master':    zhaoshang_master.std_deal_minimal,
    'xt_qmt':              xuntou.std_deal_minimal,
    'xt_pb':               xuntou.std_deal_minimal,
    'dfemc'           :    dfemc.std_deal_minimal,
    'matic_otc'       :    matic_otc.std_deal_minimal,
    'tf_yulia_dbf'       : tf_yulia_dbf.std_deal_minimal,
    'hxtrade'        :     hxtrade.std_deal_minimal,
    'touyitong'      :     touyitong.std_deal_minimal,
}

def standardize_deal_minimal(df: pd.DataFrame, date, **kwargs):
    if df.empty:
        return pd.DataFrame(columns=['ticker', 'BS_flag', 'fill_price', 'fill_volume'])
    
    client_type = kwargs.get('client_type', 'xt_qmt')
    try:
        stand_method = deal_standard_minimal_func[client_type]
    except KeyError:
        
        print( '\nskip\n' )
        return pd.DataFrame(columns=['ticker', 'BS_flag', 'fill_price', 'fill_volume'])
    
    df = stand_method(df, date, **kwargs)
    df = df[[
            "ticker",
            "BS_flag", 
            "fill_price", 
            "fill_volume",
    ]]
    if not (df['BS_flag'].isin(['买入', '卖出', '卖空', '平空']).all()):
        print(df['BS_flag'].unique())
        raise ValueError('BS_flag should be 买入 or 卖出 or 卖空 or 平空')
    df.reset_index(drop=True, inplace=True)
    return df

    

deal_standard_future_func = {
    'xuntou': xuntou.std_deal_future,
    'xt_gt' : xuntou.std_deal_future,
    'gtrade': guoxin_gtrade.std_deal_future,
}

def standardize_deal_future(df: pd.DataFrame, date, **kwargs):
    cols=['date', 'time', 'ticker', 'longshort', 'operation', 'fill_volume', 'fill_price', 'order_ref', 'deal_ref']
    
    
    if df.empty:
        return pd.DataFrame(columns=cols)
    
    client_type = kwargs.get('client_type', None)
    try:
        stand_method = deal_standard_future_func[client_type]
    except KeyError:
        print(f'unknown client type "{client_type}" to standardize file, 用 0 替代')
        return pd.DataFrame(columns=cols)

    df = stand_method(df, date, **kwargs)
    df = df[cols]
    df.reset_index(drop=True, inplace=True)
    return df
    
    
order_standard_future_func = {    
    'xuntou': xuntou.std_order_future,
    'xt_gt' : xuntou.std_order_future,
    'gtrade': guoxin_gtrade.std_order_future,
}

def standardize_order_future(df: pd.DataFrame, date, **kwargs):
    cols=['date', 'time', 'ticker', 'longshort', 'operation', 'order_status', 'order_volume', 'order_price', 'fill_volume', 'fill_price', 'order_ref']

    if df.empty:
        return pd.DataFrame(columns=cols)
    
    client_type = kwargs.get('client_type', None)
    try:
        stand_method = order_standard_future_func[client_type]
    except KeyError:
        print(f'unknown client type "{client_type}" to standardize file, 用 0 替代')
        return pd.DataFrame(columns=cols)

    df = stand_method(df, date, **kwargs)
    df = df[cols]
    df.reset_index(drop=True, inplace=True)
    return df
    
    
    

standard_hold_future_func_dict = {
    'xuntou': xuntou.std_future_hold,
    'xt_gt' : xuntou.std_future_hold,
    # 'xt_gt' : xuntou.std_hold_future,
    # 'gtrade': guoxin_gtrade.std_hold_future,
}
    
def standardize_hold_future(df: pd.DataFrame, **kwargs):
    cols = ['ticker', 'longshort', 'volume', 'today_volume']
    if df.empty:
        return pd.DataFrame(columns=cols)
    client_type = kwargs.get('client_type', None)
    try:
        stand_method = standard_hold_future_func_dict[client_type]
    except KeyError:
        print(f'unknown client type "{client_type}" to standardize file, 用 0 替代')
        return pd.DataFrame(columns=cols)

    df = stand_method(df, **kwargs)
    df = df[cols]
    df.reset_index(drop=True, inplace=True)
    return  df
    
    
account_standard_future_func = {
    'xuntou': xuntou.std_account_future,
    'xt_gt' : xuntou.std_account_future,
    'gtrade': guoxin_gtrade.std_account_future,
}    

    
def standardize_account_future(df: pd.DataFrame, date, **kwargs):
    cols = [
        'begin_rights',
        'dynamic_rights',
        'available_fund',
        'holding_pnl',
        'settle_pnl',
        'transfer_fund',
        'current_margin',
        'date',
    ]
    if df.empty:
        return pd.DataFrame(columns=cols)
    client = kwargs.get('client_type', None)
    try:
        stand_method = account_standard_future_func[client]
    except KeyError:
        print(f'standardize_account_future: client_type {client} is not supported')
        return pd.DataFrame(columns=cols)
    
    df = stand_method(df, date, **kwargs)
    cols = [col for col in cols if col in df.columns]
    df = df[cols]
    df.reset_index(inplace=True, drop=True)
    return df
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
def standardize_deal_old(df: pd.DataFrame, date, **kwargs):
    client_type = kwargs.get('client_type', None)
    if client_type in [ 'kafangalgo_dbf', ]:
        df = standardize_deal_kafangalgo_dbf(df, date, **kwargs)
    elif client_type in ['ax_onequant', ]:
        df = standardize_deal_ax_onequant(df, date, **kwargs)
        return df
    
    if '交易对代码' in df.columns:
        df['交易对代码'] = df['交易对代码'].apply(lambda x: re.sub('\.XSHE@CNY$|\.XSHG@CNY$|\.CCFX@CNY', '', x))

    # kafang atx client
    if kwargs.get('client_type', None) == 'kafangatx_dbf':
        df = standardize_deal_kafangatx_dbf(df, date)
        
    if kwargs.get('client_type', None) == 'kafangato_dbf':
        df = standardize_deal_kafangato_dbf(df, date)
    
    if 'ExchangeID' in df.columns:
        df = df[df['ExchangeID'] != 'CFFEX']
    
    for col in df.columns:
        if df[col].dtype == 'object':
            df[col] = df[col].str.lstrip("=\"")
            df[col] = df[col].str.rstrip("\"")
            
            if col in ['order_volume', 'fill_volume']:
                df[col] = df[col].str.replace(',', '').astype(float).astype(int)
            if col in ['order_price', 'fill_price']:
                df[col] = df[col].str.replace(',', '').astype(float)
    if df['ticker'].dtype == 'object' and (df['ticker'].str.endswith('.SZ') | df['ticker'].str.endswith('.SH')).any():
        df[['ticker', 'exchange']] = df['ticker'].str.split('.', expand=True)
        
    # 过滤期货流水
    if df['ticker'].dtype == 'object' and (not df.empty):
        # df = df[~df['ticker'].apply(re_find, args=('^IC|^IF|^IH|^IM',))]
        df = df[df['ticker'].apply(lambda x: False if (x.startswith('IC') or x.startswith('IF') or x.startswith('IH')) else True)] 

    # 过滤国信gtrade 撤单的负成交数流水
    df = df[~((df['fill_price'] == 0) & (df['fill_volume'] < 0))]
    
    df['BS_flag'] = df['BS_flag'].apply(replace_bs_flag)
    df = df[(df['BS_flag'] == '买入') | (df['BS_flag'] == '卖出')]
    
    if "order_ref" not in df.columns:
        df['order_ref'] = df['deal_ref']

    if "deal_ref" not in df.columns:
        df['deal_ref'] = df['order_ref']
    # if client_type == 'zhongxin_cats':
    #     df['deal_ref'] = ''
    #     df['deal_ref'] = 'deal_' + pd.Series([str(x) for x in range(1, len(df)+1)])

    if df['order_ref'].dtype != 'object':
        raise TypeError('委托编号/order_ref should be str type')
    # if df['deal_ref'].dtype != 'object':
    #     raise TypeError('成交编号/deal_ref should be str type')

    # df['time'] = pd.to_datetime(df['time']).str.strftime("%H:%M:%S")
    if 'date' not in df.columns:
        df['date'] = date

    # logger.info('deal date: {}'.format(df['date'][0]))
    # df['date'] = pd.to_datetime(df['date'].astype(str))
    df['date'] = utils.to_datetime_formmats(df['date'].astype(str), source_type='date')
    
    # logger.info('deal time: {} m'.format(df['time'].iloc[-1]))
    df['time'] = utils.to_datetime_formmats(df['time'], source_type='time')
    df['date'] = df['date'].apply(lambda x: x.strftime("%Y%m%d"))
    print(df['time'].head(2))
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    
    if "algorithm" not in df.columns:
        df["algorithm"] = ""

    
    df = df.reset_index()
    df = df[[
        "date", 
        "time", 
        "order_ref",
        "deal_ref", 
        "ticker",
        "BS_flag", 
        "fill_price", 
        "fill_volume",
        "algorithm"
    ]].sort_values("time")
    df.columns = [
        "成交日期",
        '成交时间',
        '合同编号',
        '成交编号',
        '代码',
        '买卖标记',
        '成交均价',
        '成交数量',
        '算法'
    ]
    return df

def standardize_margindeal(df : pd.DataFrame, date, **kwargs):
    if kwargs.get('client_type') == 'zhongxin_cats':
        df = standardize_margindeal_zhongxin_cats(df, **kwargs)

    if kwargs.get('client_type') == 'wuxianyi':
        df = standardize_wuxianyi_deal(df, **kwargs)
    
    if '交易对代码' in df.columns:
        df['交易对代码'] = df['交易对代码'].apply(lambda x: re.sub('\.XSHE@CNY$|\.XSHG@CNY$|\.CCFX@CNY', '', x))


    if df['ticker'].dtype == 'object' and (not df.empty):
        if (df['ticker'].apply(lambda x: re_find('\.SH$|\.SZ$|\.CFFEX$', x))).astype(bool).any():
            df[['ticker', 'exchange']] = df['ticker'].str.split('.', expand=True)

    # 特殊的, 对国信gtade的流水, 拆分买卖和operation开仓平仓
    if 'BS_flag' in df.columns and df['BS_flag'].apply(lambda x: re_find('GFD$', x)).astype(bool).any():
        df['operation'] = df['BS_flag'].str.slice(2,4)
        df['BS_flag'] = df['BS_flag'].str.slice(0,2)
        
    df['operation'] = df['operation'].apply(replace_marginhold_open_close_flag)
    df = df[(df['operation'] == '开仓') | (df['operation'] == '平仓')]
    
    # df['BS_flag'] = df['BS_flag'].apply(replace_bs_flag)
    # # 过滤出 "买入" 和 "卖出" 的成交
    # df = df[(df['BS_flag'] == '买入') | (df['BS_flag'] == '卖出')]

    # 过滤国信gtrade 撤单的负成交数流水
    df = df[~((df['fill_price'] == 0) & (df['fill_volume'] < 0))]
    
    # 'longshort', 'operation', 'BS_flag', requires 2 of these 3 columns
    if 'longshort' not in df.columns:
        df['longshort'] = (df['BS_flag'] + df['operation']).apply(operation_to_longshort)
    df['longshort'] = df['longshort'].apply(replace_marginhold_longshort)
    
    if "order_ref" not in df.columns:
        df['order_ref'] = ""
    if df['order_ref'].dtype != 'object':
        raise TypeError('委托编号/order_ref should be str type')
    if df['deal_ref'].dtype != 'object':
        raise TypeError('成交编号/deal_ref should be str type')

    # df['time'] = pd.to_datetime(df['time']).str.strftime("%H:%M:%S")
    if 'date' not in df.columns:
        df['date'] = date

    # logger.info('deal date: {}'.format(df['date'][0]))
    # df['date'] = pd.to_datetime(df['date'].astype(str))
    df['date'] = utils.to_datetime_formmats(df['date'].astype(str), source_type='date')
    
    # logger.info('deal time: {}'.format(df['time'][0]))
    df['time'] = utils.to_datetime_formmats(df['time'], source_type='time')
    df['date'] = df['date'].apply(lambda x: x.strftime("%Y%m%d"))
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    
    if "algorithm" not in df.columns:
        df["algorithm"] = ""

    
    df = df.reset_index()
    df = df[["date", 
             "time", 
             "order_ref",
             "deal_ref", 
             "ticker",
             "operation",
             "longshort",
             "fill_price", 
             "fill_volume",
             "algorithm"]].sort_values("time")
    df.columns = [
        "成交日期",
        '成交时间',
        '合同编号',
        '成交编号',
        '代码',
        '开平方向',
        '多空状态',
        '成交均价',
        '成交数量',
        '算法'
    ]
    return df


account_standard_func = {    
    'ax_onequant':        ax_onequant.std_account,
    'guangfa_jsd':        gf_jinshida.std_account,
    'gtrade':             guoxin_gtrade.std_account,
    'zhongxin_cats':      zhongxin_cats.std_account,
    'zhongxin_cats_credit': zhongxin_cats_credit.std_account,
    'kafangato_dbf':      kfato_dbf.std_account,
    'kafangatx_dbf':      kfatx_dbf.std_account,
    'kafangalgo_dbf':     yinhe_kfalgo_dbf.std_account,
    'zheshang_kfalgo_dbf': zheshang_kfalgo_dbf.std_account,
    'zhaoshang_master':    zhaoshang_master.std_account,
    'xt_qmt':              xuntou.std_account,
    'xt_pb':               xuntou.std_account,
    'dfemc'           :    dfemc.std_account,
    'matic_otc'        :   matic_otc.std_account,
    'tf_yulia_dbf'        : tf_yulia_dbf.std_account,
    'hxtrade'        :     hxtrade.std_account,
    'touyitong'      :     touyitong.std_account,
}

def standardize_marginaccount(df: pd.DataFrame, date, **kwargs):
    cols = ['date', 'available_margin', 'available_fund']
    if df.empty:
        df.loc[0, 'date'] = date
        df.loc[0, 'available_margin'] = 0
        df.loc[0, 'available_fund'] = 0
        return df
    
    client_type = kwargs.get('client_type', 'xt_qmt')
    try:
        stand_method = account_standard_func[client_type]
    except KeyError:
        print( '\nskip\n' )
        return pd.DataFrame(columns = cols)
    
    df = stand_method(df, date, **kwargs)
    if 'date' not in df.columns:
        df['date'] = date
    if 'available_margin' not in df.columns:
        df['available_margin'] = 0
    if 'available_fund' not in df.columns:
        df['available_fund'] = 0    
    if 'net_asset' in df.columns:
        cols= cols + ['net_asset']
        
    if len(df) > 1:
        df = df.iloc[[0]]
    # df = df[cols]
    # df.columns = ['日期', '可用保证金', '可用资金']
    return df
    

def standardize_stockaccount(df: pd.DataFrame, date, **kwargs):
    cols = ['date', 'net_asset', 'stock_value', 'available_fund', 'pnl(client)']
    if df.empty:
        return pd.DataFrame(columns = cols)
    client_type = kwargs.get('client_type', 'xt_qmt')
    try:
        stand_method = account_standard_func[client_type]
    except KeyError:
        print( '\nskip\n' )
        return pd.DataFrame(columns = cols)
    
    df = stand_method(df, date, **kwargs)
    
    if 'date' not in df.columns:
        df['date'] = date
    if 'pnl(client)' not in df.columns:
        df['pnl(client)'] = 0
    if 'stock_value' not in df.columns:
        df['stock_value'] = 0
    
    if len(df) > 1:
        df = df.iloc[0:1]
    # df = df[cols]
    return df
                   
def standardize_creditaccount(df: pd.DataFrame, date, **kwargs):
    cols = ['date', 'net_asset', 'available_credit', 'available_fund']
    if df.empty:
        return pd.DataFrame(columns = cols)
    client_type = kwargs.get('client_type', 'xt_qmt')
    try:
        stand_method = account_standard_func[client_type]
    except KeyError:
        print( '\nskip\n' )
        return pd.DataFrame(columns = cols)
    
    df = stand_method(df, date, **kwargs)
    df = df[cols]
    return df

def bak_standardize_marginaccount(df: pd.DataFrame, date, **kwargs):
    client_type = kwargs.get('client_type', 'xt_qmt')
    if client_type == 'matic_otc':
        stand_method = account_standard_func[client_type]
        df = stand_method(df, date, **kwargs)
        df = df[['date', 'available_margin', 'available_fund']]
        return df
        
    df = df.rename(columns={
        '日期' : 'date',
        
        '可用保证金'     : 'available_margin',
        '多头可用保证金'  : 'available_margin',
        '余额'          : 'available_margin',
        'ENBALANCE'    : 'available_margin',
        
        'A股可用资金'     : 'available_fund',
        'Available'     : 'available_fund',
        '多头可用额度'    : 'available_fund',
        '可用金额'        : 'available_fund',
        'ENBALANCE'     : 'available_fund',
        'EnBalance'     : 'available_fund',

        '多头余额'       : 'available_fund',
        '可用金额[人民币]'   :  'available_fund',   # gt 收益互换
    })
    
    if df.empty:
        return pd.DataFrame(columns = [
            '日期',
            '可用保证金',
            '可用资金'
        ])
    if 'date' not in df.columns:
        df['date'] = date
    df['date'] = utils.to_datetime_formmats(df['date'].astype(str), source_type='date').apply(lambda x: x.strftime("%Y%m%d"))
    
    if 'available_margin' not in df.columns:
        df['available_margin'] = 0
        
    if 'available_fund' not in df.columns:
        df['available_fund'] = 0    
    # df = df[['date', 'available_margin', 'available_fund']]
    # df.columns = ['日期', '可用保证金', '可用资金']
    return df

def bak_standardize_stockaccount(df: pd.DataFrame, date, **kwargs):
    # 2号 民生
    df = df.rename(columns={
        '交易日'           :         'date',
        '总资产'           :         'net_asset',
        '总市值'           :         'stock_value',
        '可用金额'         :         'available_fund',
        '可用余额'         :         'available_fund',
        '可用金额[人民币]'    :       'available_fund',   # gt 收益互换
        '可用资金'         :         'available_fund',
        
        '今日盈亏'         :         'pnl(client)',
        '今日账户盈亏'      :         'pnl(client)',
        '今日账号盈亏'      :         'pnl(client)',
        '当日盈亏'         :         'pnl(client)',
    })
    if df.empty:
        return pd.DataFrame(columns = [
            '日期',
            '净资产',
            '股票多头市值',
            '可用资金',
            '今日盈亏'
        ])
    if 'date' not in df.columns:
        df['date'] = date
    df['date'] = utils.to_datetime_formmats(df['date'].astype(str), source_type='date').apply(lambda x: x.strftime("%Y%m%d"))
    if 'pnl(client)' not in df.columns:
        df['pnl(client)'] = 0
    if 'stock_value' not in df.columns:
        df['stock_value'] = df['net_asset'] - df['available_fund']
    df = df[['date', 'net_asset', 'stock_value', 'available_fund', 'pnl(client)']]
    # df.columns = ['日期', '净资产', '股票多头市值', '可用资金', '今日盈亏']
    return df    
                   
def standardize_allaccount(df: pd.DataFrame, date, **kwargs):
    base_columns = ['date', 'net_asset', 'stock_value', 'available_fund', 'withdrawable_fund', 'fund_value', 'repo_value']
    new_df = pd.DataFrame(columns=base_columns)
    df = df.rename(columns={
        # net_asset
        'AssetAmt'     : 'net_asset',
        '净资产'        : 'net_asset',
        '资产总值'      : 'net_asset',
        # '资金资产'      : 'net_asset',
        
        # stock_value
        '证券市值'       : 'stock_value',
        '股票市值'       : 'stock_value',
        '总市值'         : 'stock_value',
        '股票总市值'     : 'stock_value',
        '参考市值'       : 'stock_value',
        '股票多头市值'    : 'stock_value',
        'MarketAmt'     : 'stock_value',

        # available_fund
        '可用金额'           : 'available_fund',
        # '资金余额'           : 'available_fund',
        '余额'              : 'available_fund',
        # '多头余额'           : 'available_fund',
        # '多头可用额度'        : 'available_fund',
        '当前余额'           : 'available_fund',
        '可用余额'           : 'available_fund',
        'CrBalance'         : 'available_fund',
        '现金资产'           : 'available_fund',

        # withdrawable_fund
        '可取金额'     : 'withdrawable_fund',
        '可取资金'     : 'withdrawable_fund',
        'EnBalance'   : 'withdrawable_fund',

        # fund_value
        '基金总市值'    : 'fund_value',

        # repo_value
        '回购总市值'    : 'repo_value',
    })
    df['date'] = date
    df['date'] = utils.to_datetime_formmats(df['date'].astype(str), source_type='date').apply(lambda x: x.strftime("%Y%m%d"))
    if 'stock_value' not in df.columns and 'net_asset' in df.columns and 'available_fund' in df.columns:
        df['stock_value'] = df['net_asset'] - df['available_fund']
    print(df)

    new_df = pd.concat([new_df, df])[base_columns].fillna(0)

    new_df = rename_duplicates(new_df)

    # new_df = new_df[base_columns].fillna(0)
    return new_df

def rename_duplicates(df):
    cols = pd.Series(df.columns)
    for dup in cols[cols.duplicated()].unique():
        cols[cols[cols == dup].index.values.tolist()] = [dup + '_' + str(i) if i != 0 else dup for i in range(sum(cols == dup))]
    df.columns = cols
    print(df.columns)
    return df
    
def standardize_futureaccount(df: pd.DataFrame, date):
    df = df.rename(columns={
        '日期' : 'date',
        
        '可用资金' : 'available',
        'Available' : 'available',
        '保证金可用' : 'available',
        
        'Margin' : 'margin',
        '保证金' : 'margin',
        '保证金占用' : 'margin',
        
        'DynamicRights' : 'dynamic_rights',
        '动态权益' : 'dynamic_rights',
    })
    if 'date' not in df.columns:
        df['date'] = date
    df['date'] = utils.to_datetime_formmats(df['date'].astype(str), source_type='date').apply(lambda x: x.strftime("%Y%m%d"))
    
    for col in ['available', 'margin', 'dynamic_rights']:
        if col not in df.columns:
            df[col] = 0
    df = df[['date', 'available', 'margin', 'dynamic_rights']]
    df.columns = ['日期', '可用资金', '保证金', '动态权益']
    return df
    



 
    
def replace_bs_flag(flag):
    if flag in ['买入', '证券买入', '担保品买入', '限价买入', 
                '证券买入限价委托', '买券还券', '最优五档即时成交剩余撤销买入',
                '对手方最优价格买入', 'B', '最优五档即时成交剩余撤销担保品买入',
                '开仓', '平仓买入'
                ]:
        flag = '买入'
    elif flag in ['卖出', '证券卖出', '担保品卖出', '限价卖出', 
                '证券卖出限价委托', '最优五档即时成交剩余撤销卖出',
                '对手方最优价格卖出', 'S', '最优五档即时成交剩余撤销担保品卖出',
                '平仓', '开仓卖出']:
        flag = '卖出'
    elif flag in ['配股配债']:
        flag = '配股配债'
    elif flag in ['回购卖出']:
        flag = '回购卖出'
    else:
        raise ValueError('买卖标记:  \n{} \n不在范围中'.format(flag))

    return flag


def replace_order_status(status):
    if status in ['已成交', '已成', '全部成交', 'Filled', '全成']:
        status = '已成'
    elif status in ['已撤单', '已撤', 'Canceled', '全撤']:
        status = '已撤'
    elif status in ['部撤', '部成', '部撤(部成)', '部分成交', 'PartialCanceled', '部成[终]', '部成[中]']:
        status = '部成'
    elif status in ['废单', '内部废单', 'Stop', '被拒']:
        status = '废单'
    elif status in ['已报', '待成交', '等待报单']:
        status = '已报'
    elif status.startswith('拒绝'):
        status = '废单'
    else:
        raise ValueError('委托状态:  \n{} \n不在范围中'.format(status))
    return status

def replace_marginhold_longshort(longshort):
    if longshort in ['看多', '多', '多头']:
        longshort = '多头'
    elif longshort in ['看空', '空', '空头']:
        longshort = '空头'
    else:
        raise ValueError('多空状态:  \n{} \n不在范围中'.format(longshort))
    return longshort
    

def replace_marginhold_open_close_flag(open_close_flag):
    if open_close_flag in ['开仓', '开']:
        open_close_flag = '开仓'
    elif open_close_flag in ['平仓', '平']:
        open_close_flag = '平仓'
    else:
        raise ValueError('开平标志:  \n{} \n不在范围中'.format(open_close_flag))
    return open_close_flag

def operation_to_longshort(operation):
    if operation == '买入开仓':
        return '多头'
    elif operation == '卖出开仓':
        return '空头'
    elif operation == '买入平仓':
        return '空头'
    elif operation == '卖出平仓':
        return '多头'
    else:
        raise ValueError('开平标志:  \n{} \n不在范围中'.format(operation))


def re_find(pattern, string):
    if re.search(pattern, string):
        return True
    else:
        return False