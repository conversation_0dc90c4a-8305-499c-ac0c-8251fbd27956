import sshtunnel
import socket
from loguru import logger
from accounts_config import sys_config

tun_datayes_dict = {
    # 'ssh_address_or_host' : ('shallpass.asuscomm.com', 16021),
    'ssh_address_or_host' : ('************', 16021),
    'ssh_username' : 'admin',
    'remote_bind_address' : ('**************', 3306),
    'local_bind_address' : ('127.0.0.1', 3306),
    'ssh_pkey' : f'/home/<USER>/.ssh/id_rsa'
}

tun_kf_trade_dict = {
    # 'ssh_address_or_host' : ('shallpass.asuscomm.com', 16021),
    'ssh_address_or_host' : ('************', 16021),
    'ssh_username' : 'admin',
    'remote_bind_address' : ('*************', 22),
    'local_bind_address' : ('127.0.0.1', 10022),
    'ssh_pkey' : f'/home/<USER>/.ssh/id_rsa'
}

tun_kfdatabase_dict = {
    'ssh_address_or_host' : ('************', 16021),
    'ssh_username' : 'admin',
    'remote_bind_address' : ('**************', 3306),
    'local_bind_address' : ('127.0.0.1', 13306),
    'ssh_pkey' : f'/home/<USER>/.ssh/id_rsa'
}

tun_zhongxinapi_dict = {
    'ssh_address_or_host' : ('*************', 22),    # kf local 工作主机 windows
    'ssh_username' : 'admin',
    'remote_bind_address' : ('*************', 20080),
    'local_bind_address' : ('127.0.0.1', 30085),
    'ssh_pkey' : f'/home/<USER>/.ssh/id_rsa'
}

tun_zhongxinliangrong_dict = {
    'ssh_address_or_host' : ('*************', 22),  # kf local 工作主机 windows
    'ssh_username' : 'admin',
    'remote_bind_address' : ('************', 80),
    'local_bind_address' : ('127.0.0.1', 30086),
    'ssh_pkey' : f'/home/<USER>/.ssh/id_rsa'
}

class cusTunnel(sshtunnel.SSHTunnelForwarder):
    def __init__(self, 
                 ssh_address_or_host=None, 
                 ssh_config_file=..., 
                 ssh_host_key=None, 
                 ssh_password=None, 
                 ssh_pkey=None, 
                 ssh_private_key_password=None, 
                 ssh_proxy=None, 
                 ssh_proxy_enabled=True, 
                 ssh_username=None, 
                 local_bind_address=None, 
                 local_bind_addresses=None, 
                 logger=None, 
                 mute_exceptions=False, 
                 remote_bind_address=None, 
                 remote_bind_addresses=None, 
                 set_keepalive=5, 
                 threaded=True, 
                 compression=None, 
                 allow_agent=True, 
                 host_pkey_directories=None, 
                 *args, 
                 **kwargs):
        super().__init__(ssh_address_or_host, 
                         ssh_config_file, 
                         ssh_host_key, 
                         ssh_password, 
                         ssh_pkey, 
                         ssh_private_key_password, 
                         ssh_proxy, 
                         ssh_proxy_enabled, 
                         ssh_username, 
                         local_bind_address, 
                         local_bind_addresses, 
                         logger, 
                         mute_exceptions, 
                         remote_bind_address, 
                         remote_bind_addresses, 
                         set_keepalive, 
                         threaded, 
                         compression, 
                         allow_agent, 
                         host_pkey_directories, 
                         *args, 
                         **kwargs)
        if local_bind_address != None:
            self.local_binding = local_bind_address
        
    def start(self):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            is_port_used = s.connect_ex((self.local_binding[0], self.local_binding[1])) == 0
        if not is_port_used :
            return super().start()
        else:
            logger.warning('The port {} is already in use, please check if the tunnel is already active.'.format(self.local_binding[1]))
        