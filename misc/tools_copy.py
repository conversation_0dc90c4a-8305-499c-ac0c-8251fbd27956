import numpy as np
import pandas as pd
import os
import re
from loguru import logger
from sqlalchemy import create_engine
from urllib.parse import quote_plus
import datetime

from data_utils.trading_calendar import Calendar
from misc.Readstockfile import read_remote_file, update_sheet_in_remote_excel
from accounts_config.accounts_fj import fj_pnl_account, fj_pnl_file, accounts_fj, fj_dir_prefix
from accounts_config.kf_accounts_basic import dma_accounts, kf_pnl_file, kf_pnl_account, kf_accounts_dict
from accounts_config.kf_accounts_basic import get_account_sftp
from misc.ssh_conn import sftp_clent_dav, sftp_clent_ninner, sftp_clent_outer, ftp_clent_jeff
from misc.redis_func import df_read_redis, conn_redis
# from misc.standard_table_dev import standardize_hold


# from test4 import trade_config, update_sub_config_in_main_config
pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.width', 180)


rs = conn_redis()
DATAYESDB_CONNECTION_STRING = 'mysql+pymysql://datayesRO:datayesRO@**************:3306/datayes'
TRADEDB_CONNECTION_STRING = f'mysql+pymysql://tradeRO:{quote_plus("tradeRO@KF123")}@************:63306/trade'

ZYYX_CONNECTION_STRING = f'mysql+pymysql://vip_user:{quote_plus("c93U%%7zp")}@**************:53306/gds_data'

# trade_dict={"host":"************","user":"tradeRO","password":"tradeRO@KF123","port":63306}

def get_trade_config():
    trade_config_file = 'others/trade_config.xlsx'
    trade_config = read_remote_file(trade_config_file, src_type='dav',
                                    dtype={'start_time':str, 'end_time':str}
                                    )
    if 'No.' in trade_config.columns:
        trade_config.drop(columns=['No.'], inplace=True)
    trade_config.set_index('account_name', inplace=True)
    # trade_config = trade_config.replace(np.nan, None)
    trade_config = trade_config[trade_config['active'] == 1]
    # print(trade_config)
    trade_config = trade_config.to_dict(orient='index') 
    # return dict
    return trade_config

# def find_latest_remote_file_on_date(sftp_method, dir, file_prefix, date, sortReverse:bool=True):
#     """
#     在给定文件夹目录下，找出指定日期的时间最新的文件名称

#     :return: 时间最新的文件名称，如果没有找到符合条件的文件则返回 FileNotFound
#     """
#     # 编译正则表达式，匹配指定日期的文件名
#     pattern = re.compile(f"^{file_prefix}{date}(?:[0-9]{6})?")
#     pattern = re.compile(f"^{file_prefix}{date}(?:[0-9]{6})?")

#     # 列出目录下的所有文件，并过滤出符合正则表达式的文件
#     folder_lst = sftp_method.listdir(dir)
#     files = [f for f in folder_lst if pattern.match(f)]

#     # 按照文件名排序，这里假设时间标记越大，文件越新
#     files.sort(reverse=sortReverse)
#     # print(files)

#     # 返回时间标记最新的文件名，如果没有找到则返回None
#     if files:
#         return files[0]
#     else :
#         raise FileNotFoundError('{} 文件不存在'.format(os.path.join(dir, file_prefix+date+'...')))


# new find remote file
def find_latest_remote_file_on_date(sftp_method, dir, file_prefix, date, sortReverse:bool=True, timeby='235959', timefrom='000000'):
    """
    根据文件名查找指定日期的文件
    :param sftp_method: sftp方法
    :param dir: 目录
    :param file_prefix: 文件前缀
    :param date: 日期
    :param sortReverse: 是否倒序
    :param timeby: 小于截止时间前
    :param timefrom: 大于开始时间
    :return: 文件名
    """
    folder_lst = sftp_method.listdir(dir)
    res_files = []
    time_pattern = r"(\d{6})?"

    regex = f"^{file_prefix}{date}{time_pattern}\.(?:xls|xlsx|csv)$"
    # print(regex)
    by_time = datetime.datetime.strptime(f"{date}{timeby}", "%Y%m%d%H%M%S")
    from_time = datetime.datetime.strptime(f"{date}{timefrom}", "%Y%m%d%H%M%S")
    
    for file in folder_lst:
        # print(file)
        match = re.search(regex, file)
        # cond 1:
        if match:
            time_part = match.group(1) if match.group(1) else "000000"  # 如果时间不存在，假设为 "000000"（即午夜）

            # 将日期时间字符串转换为datetime对象
            date_time = datetime.datetime.strptime(date + time_part, "%Y%m%d%H%M%S")
            
            # cond 2:            
            if from_time <= date_time <= by_time:
                res_files.append(file)                
                
    # 按照文件名排序，这里假设时间标记越大，文件越新
    res_files.sort(reverse=sortReverse)
    # print(files)

    if res_files:
        return res_files[0] 
    else :
        raise FileNotFoundError('{} 文件不存在'.format(os.path.join(dir, file_prefix+date+'...')))    


def get_target_by_date(account_name, date, config=None):
    """
    cancelled....
        config: {
                    account_name: 
                        {
                            'new_system': True/False,
                        }
                }
    """
    sftp_ftp = get_account_sftp(account_name)['sftp']
    ftp_type = get_account_sftp(account_name)['ftp_type']

    # if config is not None:
    if config is not None and config.get('timeby') is not None:
        timeby = config['timeby']
    else:
        timeby = '235959'
    
    try:
        # if isinstance(config, dict) and config.get(account_name).get('new_system'):
        position_file = find_latest_remote_file_on_date(sftp_ftp, os.path.join(account_name, 'position'), file_prefix='position.', date=date, timeby=timeby)
        target_pos = read_remote_file(path=os.path.join(account_name, 'position', position_file),
                                src_type=ftp_type, header=None, names=['ticker', 'volume'])
        target_pos['ticker'] = target_pos['ticker'].astype('int')
            # print(account_name, 'new_account')
        # else: 
        #     target_pos = read_remote_file(path=os.path.join(account_name, 'position', 'position.{}091000.csv'.format(date)),
        #                             src_type='inner', header=None, names=['ticker', 'volume'])
    except FileNotFoundError:
        target_pos = pd.DataFrame([[1,0]], columns=['ticker', 'volume'])
        logger.warning('账户- {} 的目标持仓position文件不存在, 用 0 替代'.format(account_name))
    return target_pos

def read_latest_pnl_file(product_name=0):
    pnl_path = '远航安心中性1号/pnl/宽辅系列.xlsx'
    df = read_remote_file(pnl_path, src_type='dav', sheet_name=product_name, header=[1,2], index_col=0)
    return df


# 保证正确, 没有数据会异常
def get_available_fund(account_name, date, kf_accounts_dict=kf_accounts_dict):
    # pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    # 手动配置, 优先最高    
    # TODO
    
    # from dav stockaccount, marginaccount,
    if account_name in kf_accounts_dict:
        acc_fig = kf_accounts_dict[account_name]

        # if acc_fig.get('account_file') and acc_fig.get('account_type') in ['stockaccount', 'marginaccount']:
        #     account_df = read_remote_file(os.path.join(account_name, 'account', acc_fig['account_file']['file_name'].format(date)),
        #                                 src_type=acc_fig['account_file']['file_src'])
        #     available_margin = account_df.loc[account_df['date'].astype(str)==date, 'available_margin'].values[0] if 'available_margin' in account_df else 0
        #     available_fund = account_df.loc[account_df['date'].astype(str)==date, 'available_fund'].values[0] if 'available_fund' in account_df else 0
        #     available_fund = available_margin * 4 if available_fund == 0 else available_fund
        # else:
        #     raise ValueError(f'{account_name} account_file or account_type 没有配置')
        
        accountinfo_file = f'accountinfo_{date}.xls'
        # accountinfo_file = f'marginaccount_{date}.xls' if acc_fig.get('account_type') == 'marginaccount' else f'stockaccount_{date}.xls'
        file_path = os.path.join(account_name, 'account', accountinfo_file)
        account_df = read_remote_file(file_path, src_type='dav')
        available_margin = account_df.loc[account_df['date'].astype(str)==date, 'available_margin'].values[0] if 'available_margin' in account_df else 0
        available_fund = account_df.loc[account_df['date'].astype(str)==date, 'available_fund'].values[0] if 'available_fund' in account_df else 0
        available_fund = available_margin * 4 if available_fund == 0 else available_fund
        
    # 不是kf账户, 从pnl文件中获取
    else:
        account_config = get_trade_config()[account_name]
        if account_config.get('pnl_file') == fj_pnl_file:
            available_fund = get_available_fund_from_pnl_file_fj(account_name, date, account_config)
    return available_fund

def get_account_stock_value(account_name, date, kf_accounts_dict=kf_accounts_dict) -> float:
    # from dav stockaccount, marginaccount,    
    if account_name in kf_accounts_dict:
        acc_fig = kf_accounts_dict[account_name]
        # accountinfo_file = f'accountinfo_{date}.xls'
        accountinfo_file = f'marginaccount_{date}.xls' if acc_fig.get('account_type') == 'marginaccount' else f'stockaccount_{date}.xls'
        file_path = os.path.join(account_name, 'account', accountinfo_file)
        account_df = read_remote_file(file_path, src_type='dav')
        acc_net_asset = account_df.loc[account_df['date'].astype(str)==date, 'long_value'].values[0]

    # 不是kf账户, 从pnl文件中获取
    else:
        account_config = get_trade_config()[account_name]
        if account_config.get('pnl_file') == fj_pnl_file:
            acc_net_asset = get_stock_value_from_pnl_file_fj(account_name, date, account_config)
    return acc_net_asset

def get_account_net_asset(account_name, date, kf_accounts_dict=kf_accounts_dict) -> float:
    # from dav stockaccount, marginaccount,
    # print(account_name)
    if account_name in dma_accounts :
        return 0
    
    if account_name in kf_accounts_dict:
        accountinfo_file = f'accountinfo_{date}.xls'
        file_path = os.path.join(account_name, 'account', accountinfo_file)
        try:
            account_df = read_remote_file(file_path, src_type='dav')
        # 找不到文件, 可能是初始账户, 返回0
        except FileNotFoundError:
            logger.warning(f'没有找到 {account_name} 的 account_info 文件, 用 0 替代')
            return 0
        acc_net_asset = account_df.loc[account_df['date'].astype(str)==date, 'net_asset'].values[0]

    # 不是kf账户, 从pnl文件中获取
    else:
        account_config = get_trade_config()[account_name]
        if account_config.get('pnl_file') == fj_pnl_file:
            acc_net_asset = get_net_asset_from_pnl_file_fj(account_name, date, account_config)
        else:
            acc_net_asset = 0
    return acc_net_asset

def get_account_estimate_pnl(account_name, date, kf_accounts_dict=kf_accounts_dict) -> float:
    # from dav stockaccount, marginaccount,    
    if account_name in kf_accounts_dict:
        acc_fig = kf_accounts_dict[account_name]
        # accountinfo_file = f'accountinfo_{date}.xls'
        accountinfo_file = f'marginaccount_{date}.xls' if acc_fig.get('account_type') == 'marginaccount' else f'stockaccount_{date}.xls'
        file_path = os.path.join(account_name, 'account', accountinfo_file)
        account_df = read_remote_file(file_path, src_type='dav')
        acc_net_asset = account_df.loc[account_df['date'].astype(str)==date, 'account_pnl'].values[0]

    # 不是kf账户, 从pnl文件中获取
    else:
        account_config = get_trade_config()[account_name]
        if account_config.get('pnl_file') == fj_pnl_file:
            acc_net_asset = get_stock_value_from_pnl_file_fj(account_name, date, account_config)
    return acc_net_asset

# 从pnl文件中获取可用资金, 没有数据会异常
# def get_dma_account_logic_config() -> pd.DataFrame:
#     logic_config_file = 'others/account_config.xlsx'

#     logic_config = read_remote_file(logic_config_file, src_type='dav', sheet_name='account_index_num'
#                                     )
#     if 'No.' in logic_config.columns:
#         logic_config.drop(columns=['No.'], inplace=True)
#     logic_config = logic_config[logic_config['active']==True]
#     # logic_config.set_index('account_name', inplace=True)
#     logic_config = logic_config.replace(np.nan, None)
#     # print(trade_config)
#     # logic_config = logic_config.to_dict(orient='index')
#     # return dict
#     return logic_config

def load_account_info_adjust_config(sheet_name='logic_coef') -> pd.DataFrame:
    adjust_file_path = 'others/account_config.xlsx'
    adjust_config = read_remote_file(adjust_file_path, src_type='dav', sheet_name=sheet_name)
    adjust_config = adjust_config[adjust_config['active']==True]
    adjust_config = adjust_config.fillna(0)
    adjust_config = adjust_config.set_index('account_name')
    adjust_config['future_intraday_change'] = adjust_config['future_intraday_change'].replace(np.nan, '')
    # trade_config.set_index('account_name', inplace=True)
    if not adjust_config.index.is_unique :
        raise ValueError('account_name not unique')
    return adjust_config

def parse_account_adjust_config_future_info(futureinfo:str) -> pd.Series:
    result = []
    if not isinstance(futureinfo, str):
        return pd.Series(result)
    deals = futureinfo.split(';')
    for item in deals:
        i = item.split(':')
        result.append({'bench' : i[0], 'index_num': float(i[1])})
        # result.update({'bench' : i[0], 'index_num': float(i[1])})
    return pd.DataFrame(result).set_index('bench')['index_num']

def cal_future_value(index_num: pd.Series, index_close_df):
    value = (index_num * index_close_df.loc[index_num.index, 'close'] * index_close_df.loc[index_num.index, 'multiplier']).sum()
    return value

def get_dma_account_logic_size(account_name, logic_config_df, index_close_df):
    logic_size = 0
    logic_config_df = logic_config_df[logic_config_df['account_name']==account_name] # single account
    for idx in logic_config_df.index:
        bench = logic_config_df.loc[idx, 'bench']
        index_num = logic_config_df.loc[idx, 'index_num']        
        logic_size += index_num * index_close_df.loc[bench, 'close'] * index_close_df.loc[bench, 'multiplier']
    return logic_size

def get_index_close_df(date):
    index_close_df = pd.DataFrame(index=['ZZ500', 'ZZ1000', 'HS300', ], columns=['close', 'multiplier'],
                 data=[[get_index_close('000905', date), 200],
                       [get_index_close('000852', date), 200],
                       [get_index_close('000300', date), 300],
                    #    [get_index_close('932000', date), 0],
                    #    [get_index_close('000832', date), 1],
                       ]
                 )
    return index_close_df


def get_latest_hold(account_name, date, config):
    # 如果指定了hold_file, 用指定设置, 先dav, 再inner
    # 如果redis 没有, 先去dav上找date 这天的
    # 如果dav 没有, 去inner ftp找 date这天的
    # 如果inner ftp 没有date 的, 再找前一天 pre_date 的
    # 如果都没有, 用 0 代替
    
    # 多次尝试功能
    def try_get_hold_methods(account_name, date):
        methods = [get_hold_dav, get_hold_ftp, make_zero_hold]  # 将方法作为函数对象列表
        for method in methods:
            try:
                result = method(account_name=account_name, date=date)
                # print(f"{method.__name__} succeeded")
                return result
            except Exception as e:
                print(f"{method.__name__} failed:", e)
        raise Exception("All methods failed")

    # dav 上当前date 的最新文件
    def get_hold_dav(account_name, date):
        file_name = find_latest_remote_file_on_date(
            sftp_clent_dav,
            os.path.join(account_name, "hold"),
            file_prefix="hold_",
            date=date,
        )
        logger.info(f'\n{account_name} dav hold: {file_name}')
        df = read_remote_file(os.path.join(account_name, "hold", file_name), src_type="dav")
        return df

    # ninner上当前 date 的最新文件
    def get_hold_ftp(account_name, date):
        sftp_ftp = get_account_sftp(account_name)['sftp']
        ftp_type = get_account_sftp(account_name)['ftp_type']
        file_name = find_latest_remote_file_on_date(
            sftp_ftp,
            os.path.join(account_name, "hold"),
            file_prefix="hold_",
            date=date,
        )
        logger.info(f'\n{account_name} {ftp_type} hold: {file_name}')
        df = read_remote_file(os.path.join(account_name, "hold", file_name), src_type=ftp_type)
        return df
    
    def make_zero_hold(account_name, date):
        df = pd.DataFrame({'ticker': [1], 'volume': [0], 'available_volume': [0]})
        logger.warning(f'\n{account_name} hold on {date} not found anywhere, 用 0 替代')
        return df
    ############################################################
    if config.get('hold_file') is not None and pd.notna(config['hold_file']):
        if config['hold_file'] in sftp_clent_dav.listdir(os.path.join(account_name, 'hold')):
            df = read_remote_file(path= os.path.join(account_name, 'hold', config['hold_file']), src_type='dav')
            logger.info(f'读取了 {account_name} hold_file {config["hold_file"]} found in dav')
        else:
            ftp_type = get_account_sftp(account_name)['ftp_type']
            df = read_remote_file(path= os.path.join(account_name, 'hold', config['hold_file']), src_type=ftp_type)
            logger.info(f'读取了 {account_name} hold_file {config["hold_file"]} found in {ftp_type}')
    else:
        # pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
        df = df_read_redis(rs, key=f'{date}.stock.{account_name}.hold')
        if df is None or df.empty:
            logger.warning(f'没有找到 {account_name} 的缓存 hold 为空, 再通过文件查找')
            df = try_get_hold_methods(account_name=account_name, date=date)
    # df = df.rename(columns={'ticker': 'symbol', 'volume': 'hold_shares',
    #                         '代码' : 'symbol', '持仓量' : 'hold_shares',
    #                         '证券代码' : 'symbol', '持有数量' : 'hold_shares', '当前数量' : 'hold_shares'})
    return df


# stock deal
def get_stock_deal(account_name, date):
    # 如果指定了stock_deal_file, 用指定设置, 先dav, 再inner
    # 如果redis 没有, 先去dav上找date 这天的
    # 如果dav 没有, 去inner ftp找 date这天的
    # 如果inner ftp 没有date 的, 再找前一天 pre_date 的
    # 如果都没有, 用 0 代替
    # 多次尝试功能
    deal_file = f'deal_{date}.xls'
    deal = read_remote_file(os.path.join(account_name, 'account', deal_file), src_type='dav')
    return deal

def get_stock_minimal_deal(account_name, date):
    rs_read_key = f'{date}.stock.{account_name}.deal.minimal'
    df = df_read_redis(rs, key=rs_read_key)
    return df

def get_stock_morning_hold(account_name, date):
    try:
        file_name = find_latest_remote_file_on_date(
            sftp_clent_dav,
            os.path.join(account_name, "hold"),
            file_prefix="hold_",
            date=date,
            timeby='092500'
        )
        print(f'{account_name} dav morning hold: {file_name}')
        df = read_remote_file(os.path.join(account_name, "hold", file_name), src_type="dav")
    except FileNotFoundError:
        file_name = find_latest_remote_file_on_date(
            sftp_method=get_account_sftp(account_name)['sftp'],
            dir=os.path.join(account_name, "hold"),
            file_prefix="hold_",
            date=date,
            timeby='092500'
        )
        print(f'{account_name} ftp morning hold: {file_name}')
        df = read_remote_file(os.path.join(account_name, "hold", file_name), src_type=get_account_sftp(account_name)['ftp_type'])
    return df


# ===================================================
# def get_morning_hold(account_name, date, config={}):
#     pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')

#     # find previous day hold
#     try:
#         file_name = find_latest_remote_file_on_date(
#             sftp_method=sftp_clent_dav,
#             dir=os.path.join(account_name, 'hold'),
#             file_prefix='hold_',
#             date=pre_date,
#         )
#         print(file_name)
#         pre_hold_path = os.path.join(account_name, 'hold', file_name)
#         df = read_remote_file(pre_hold_path, src_type='dav')
#         df = df.rename(columns={'代码': 'ticker', '持仓量': 'volume', '可用数量':'available_volume'})
#         df['available_volume'] = df['volume']
#     # fb from pre hold
#     except FileNotFoundError:
#         df = pd.DataFrame({'ticker': [1], 'volume': [0], 'available_volume': [0]})
#         logger.warning(f'{account_name} hold_{pre_date}.xls not found in dav, 用 0 替代')
#     return df

def get_account_inout_money(account_name, account_type, date):
    inout_file = f'inout_{date}.xlsx'
    inout_path = os.path.join('others', 'inout_money', inout_file)
    
    try:
        inout = read_remote_file(inout_path, src_type='dav')
    except FileNotFoundError:
        inout = pd.DataFrame(columns=['date', 'product_name', 'account_name', 'account_type', 'available_adjust'])
    inout_money = inout.loc[(inout['account_type']==account_type) & (inout['account_name']==account_name), 'available_adjust'].sum()
    return inout_money

def get_product_data_from_nav(product_name, date, col_name):
    nav_path = 'others/宽辅产品净值序列更新.xlsx'
    df = read_remote_file(nav_path, src_type='dav', sheet_name=product_name, index_col='日期')
    # print(df.tail(2).index)
    data = df.loc[date, col_name]
    return data

def get_product_nav_series(product_name):
    nav_path = 'others/宽辅产品净值序列更新.xlsx'
    df = read_remote_file(nav_path, src_type='dav', sheet_name=product_name, index_col='日期')
    # print(df.tail(2).index)
    return df




def get_product_data_from_pnl(product_name, date, col_name):
    pnl_path = os.path.join(kf_pnl_account, 'pnl', kf_pnl_file)
    df = read_remote_file(pnl_path, src_type='dav', sheet_name=product_name, header=[1,2], index_col=0)
    # print(df.tail(2).index)
    data = df.loc[date, ('总账户', col_name)]
    return data


# def get_stock_hold_df(account_name, date):
#     # 如果指定了hold_file, 用指定设置, 先dav, 再inner
#     # 如果redis 没有, 先去dav上找date 这天的
#     # 如果dav 没有, 去inner ftp找 date这天的
#     # 如果inner ftp 没有date 的, 再找前一天 pre_date 的
#     # 如果都没有, 用 0 代替
#     pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
#     df = df_read_redis(rs, key=f'{date}.stock.{account_name}.hold')
#     if df is None or df.empty:
#         if account_name in sftp_clent_dav.listdir('') and \
#             f'hold_{date}.xls' in sftp_clent_dav.listdir(os.path.join(account_name, 'hold')):
#             df = read_remote_file(os.path.join(account_name, 'hold', f'hold_{date}.xls'), src_type='dav')
#             logger.info(f'{account_name} hold_{date}.xls not found in memory, 读取了 hold_{date}.xls in dav')
#         elif f'hold_{date}.xls' in sftp_clent_ninner.listdir(os.path.join(account_name, 'hold')):
#             df = read_remote_file(os.path.join(account_name, 'hold', f'hold_{date}.xls'), src_type='inner')
#             logger.warning(f'{account_name} hold_{date}.xls not found in memory, 读取了 hold_{date}.xls in inner')
#         elif f'hold_{pre_date}.xls' in sftp_clent_ninner.listdir(os.path.join(account_name, 'hold')):
#             df = read_remote_file(os.path.join(account_name, 'hold', f'hold_{pre_date}.xls'), src_type='inner')
#             logger.warning(f'{account_name} hold_{date}.xls not found in memory, 读取了 hold_{pre_date}.xls in inner')
#         else:
#             df = pd.DataFrame({'ticker': [1], 'volume': [0]})
#             logger.warning(f'{account_name} hold_{date}.xls not found anywhere, 用 0 替代')

#     df = df.rename(columns={'ticker': 'symbol', 'volume': 'hold_shares',
#                             '代码' : 'symbol', '持仓量' : 'hold_shares',
#                             '证券代码' : 'symbol', '持有数量' : 'hold_shares', '当前数量' : 'hold_shares'})
#     return df

def get_futures_hold_df(account_name, date):
    future_hold_file = f'hold_{date}.xls'
    future_hold_path = os.path.join('期货汇总', 'hold', future_hold_file)
    df = read_remote_file(future_hold_path, src_type='dav')
    df = df[df['account_name']==account_name]
    return df

def get_product_future_hold_to_info(date):
    future_hold_file = f'hold_{date}.xls'
    future_hold_path = os.path.join('期货汇总', 'hold', future_hold_file)
    df = read_remote_file(future_hold_path, src_type='dav')
    
    def map_values(value):
        if 'IC' in value or '中证500' in value:
            return 'ZZ500'
        elif 'IM' in value or '中证1000' in value:
            return 'ZZ1000'
        elif 'IF' in value or '沪深300' in value:
            return 'HS300'
        elif 'IH' in value or '上证50' in value:
            return 'SZ50'
        else:
            return value  # 保留其他值不变
        
    df = df[['product_name', 'longshort', 'ticker', 'volume']]
    df['ticker'] = df['ticker'].map(map_values)
    df['volume'] = df['longshort'].map({'多头': 1, '空头': -1}) * df['volume']
    df = df[['product_name', 'ticker', 'volume']].groupby(['product_name', 'ticker']).sum().reset_index()
    df = df.pivot(index='product_name', columns='ticker', values='volume').fillna(0)
    
    return df

# def validate_futures_hold(product_name, date, size):
#     product_weight = getProductConstitute(date).set_index('product_name').loc[product_name]
#     index_df = get_index_close_df(date)
#     futures_target = product_weight * size / index_df['close'] / index_df['multiplier']
#     account_df = get_product_future_hold_to_info(date)

#     # print(account_df)
#     account_hold = account_df.set_index('product_name').loc[product_name]
#     diff = futures_target + account_hold
#     return diff


def get_futures_net_asset(account_name, date):
    future_path = os.path.join('期货账户', account_name, f'futureaccount_{date}.xls')
    df = read_remote_file(future_path, src_type='dav')
    # print(df)
    data = df[pd.to_datetime(df['date'].astype(str)) ==date]['dynamic_rights'].values[0]
    # net_asset = df.set_index('date').loc[date, 'dynamic_rights']
    return data

def get_futures_available_fund(account_name, date):
    future_path = os.path.join('期货账户', account_name, f'futureaccount_{date}.xls')
    df = read_remote_file(future_path, src_type='dav')
    data = df[pd.to_datetime(df['date'].astype(str)) ==date]['available'].values[0]
    # available_fund = df.set_index('date').loc[date, 'available_fund']
    return data

def get_futures_today_deal(account_name, date):
    deal_path = os.path.join('期货账户', account_name, f'futuredeal_{date}.xls')
    try:
        df = read_remote_file(deal_path, src_type='dav')
    except FileNotFoundError:
        rs_key = f'{date}.future.{account_name}.deal'
        df = df_read_redis(rs, rs_key)
    if df is None:
        df = pd.DataFrame(columns=['ticker', 'longshort', 'operation', 'volume', 'price'])
    return df


def get_futuStra_futures_deal(account_name, date):
    deal_path = os.path.join('期货策略', account_name, 'account', f'deal_{date}.xls')
    try:
        df = read_remote_file(deal_path, src_type='dav')
    except FileNotFoundError:
        rs_key = f'{date}.future.{account_name}.deal'
        df = df_read_redis(rs, rs_key)
    if df is None:
        df = pd.DataFrame(columns=['ticker', 'longshort', 'operation', 'fill_volume', 'fill_price'])
    return df


# fj
####################################################
def get_product_asset_from_pnl_fj(product_name, date):
    pnl_path = os.path.join(fj_dir_prefix, fj_pnl_account, 'pnl', fj_pnl_file)

    df = read_remote_file(pnl_path, src_type='outer', sheet_name=product_name, header=[1,2], index_col=0)
    data = df.loc[date, ('总账户', '总资产')]
    return data

def get_product_short_value_from_pnl_fj(product_name, date):
    pnl_path = os.path.join(fj_dir_prefix, fj_pnl_account, 'pnl', fj_pnl_file)

    df = read_remote_file(pnl_path, src_type='outer', sheet_name=product_name, header=[1,2], index_col=0)
    data = df.loc[date, ('总账户', '总空头市值')]
    return data

# def get_morning_hold_fj(account_name, date, config=accounts_fj['accounts']):
#     pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
#     try:
#         file_name = find_latest_remote_file_on_date(
#             sftp_method=sftp_clent_outer,
#             dir=os.path.join(fj_dir_prefix, account_name, 'hold'),
#             file_prefix='hold_',
#             date=pre_date,
#         ) # latest time file
#         # pre_hold_path = os.path.join(account_name, 'hold', file_name)
#         print(file_name)
#         source_hold_path = os.path.join(fj_dir_prefix, account_name, 'hold', file_name)
#         df = read_remote_file(source_hold_path, src_type='outer')
#     except FileNotFoundError:
#         df = pd.DataFrame({'ticker': [1], 'volume': [0], 'available_volume': [0]})
#         logger.warning(f'{account_name} hold_{pre_date}.xls not found in outer, 用 0 替代')

#     client_type = config.get(account_name).get('client_type')
#     if client_type is not None:
#         df = standardize_hold(df, client_type=client_type)
#     else:
#         df = standardize_hold(df)
#     df['available_volume'] = df['volume']
#     return df

def get_net_asset_from_pnl_file_fj(account_name, date, account_config):
    splited_res = account_name.rsplit('_', 1)
    if len(splited_res) == 2:
        product_name = splited_res[0]
        broker = splited_res[1]
    else:
        product_name = splited_res[0]
        broker = ''
    
    pnl_path = os.path.join(fj_pnl_account, 'account', fj_pnl_file)
    ftp_type = get_account_sftp(account_name)['ftp_type']
    df = read_remote_file(pnl_path, src_type=ftp_type, sheet_name=product_name, header=[1,2], index_col=0)
    for cols in df.columns:
        if '股票端' in cols[0] and broker in cols[0]:
            col_1 = cols[0]
            break
    # 可能是 '净资产', '实际资产'
    for cols in df.columns:
        if col_1 == cols[0] and ('净资产' == cols[1] or '实际净资产' == cols[1]):
            col_2 = cols[1]
            break
    print(col_2)
    fund = df.loc[date, (col_1, col_2)]
    return fund


def get_stock_value_from_pnl_file_fj(account_name, date, account_config):
    splited_res = account_name.rsplit('_', 1)
    if len(splited_res) == 2:
        product_name = splited_res[0]
        broker = splited_res[1]
    else:
        product_name = splited_res[0]
        broker = ''
    
    pnl_path = os.path.join(fj_pnl_account, 'account', fj_pnl_file)
    ftp_type = get_account_sftp(account_name)['ftp_type']

    df = read_remote_file(pnl_path, src_type=ftp_type, sheet_name=product_name, header=[1,2], index_col=0)
    for cols in df.columns:
        if '股票端' in cols[0] and broker in cols[0]:
            col_1 = cols[0]
            break
    data = df.loc[date, (col_1, '股票多头市值')]
    return data

def get_available_fund_from_pnl_file_fj(account_name, date, account_config):
    splited_res = account_name.rsplit('_', 1)
    if len(splited_res) == 2:
        product_name = splited_res[0]
        broker = splited_res[1]
    else:
        product_name = splited_res[0]
        broker = ''
    
    pnl_path = os.path.join(fj_pnl_account, 'account', fj_pnl_file)
    ftp_type = get_account_sftp(account_name)['ftp_type']

    df = read_remote_file(pnl_path, src_type=ftp_type, sheet_name=product_name, header=[1,2], index_col=0)
    for cols in df.columns:
        if '股票端' in cols[0] and broker in cols[0]:
            col_1 = cols[0]
            break
    fund = df.loc[date, (col_1, '可用资金')]
    return fund


# ==============================================================================

def get_product_sub_redeem_influence(product_name, date):
    # rules:
    # T+1, T+2
    
    
    account_config_file = 'others/account_config.xlsx'
    account_config_src_type = 'dav'
    
    config = read_remote_file(path=account_config_file, src_type=account_config_src_type,
                              sheet_name='redeem')
    # filter inactive account
    config = config[config['active'] == 1]
    config.drop(columns=['active'], inplace=True)
    # filter by dates rules  -> True/False
    # config['influence'] = 
    value = config[config['product_name']==product_name]['sub_redeem'].sum()
    return value


def get_product_net_asset_from_nav_or_estimate(product_name, date):

    # pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    nav_file = 'others/宽辅产品净值序列更新.xlsx'
    nav_df = read_remote_file(nav_file, src_type='dav', sheet_name=product_name, index_col=0)
    
    
    last_nav_date = date
    while last_nav_date not in nav_df.index:
        last_nav_date = Calendar.last_trading_day(last_nav_date).strftime('%Y%m%d')
    if last_nav_date != date:
        logger.warning(f'{product_name} 没有找到 {date} 的净资产, 尝试从 {last_nav_date} 估计')
    last_nav_net_asset = nav_df.loc[last_nav_date, '资产净值']
    
    # require_net_asset_date = last_nav_date
    while last_nav_date != date:
        tmp_pre_date = last_nav_date
        
        last_nav_date = Calendar.next_trading_day(tmp_pre_date).strftime('%Y%m%d')
        tmp_next_day_pnl = get_product_data_from_pnl(product_name, last_nav_date, '日盈亏')
        last_nav_net_asset += tmp_next_day_pnl
        tmp_next_day_long_value = get_product_data_from_pnl(product_name, last_nav_date, '总多头市值')
        days = (datetime.datetime.strptime(last_nav_date, '%Y%m%d') - datetime.datetime.strptime(tmp_pre_date, '%Y%m%d')).days
        tmp_next_day_pnl_interest = max(tmp_next_day_long_value - last_nav_net_asset, 0) *  0.038 * days / 365
        last_nav_net_asset -= tmp_next_day_pnl_interest
        
    return last_nav_net_asset


###################################### query
def get_futures_price(date):
    query_close = """
    SELECT  
        TICKER_SYMBOL as ticker,
        TRADE_DATE as date,
        PRE_SETTL_PRICE as pre_settle,
        PRE_CLOSE_PRICE as pre_close,
        SETTL_PRICE as settle,
        CLOSE_PRICE as close
    FROM mkt_futd
    WHERE TRADE_DATE="{}"
    """
    engine = create_engine(DATAYESDB_CONNECTION_STRING)
    with engine.connect() as conn:
        close = pd.read_sql(query_close.format(date), conn)
    # close = close[['TICKER_SYMBOL', 'PRE_SETTL_PRICE', 'PRE_CLOSE_PRICE', 'SETTL_PRICE', 'CLOSE_PRICE', 'TRADE_DATE']]
    # close.columns = ['ticker', 'pre_settle', 'pre_close', 'settle', 'close', 'date']
    return close


def get_main_index_close(date):
    query_close = """
    SELECT 
        TICKER_SYMBOL as ticker,
        TRADE_DATE as date,
        CLOSE_INDEX as close,
        PRE_CLOSE_INDEX as pre_close
    FROM mkt_idxd
    WHERE 
    TICKER_SYMBOL IN ('000300', '000016', '000852', '000905')
    AND
    TRADE_DATE="{}"
    """.format(date)
    engine = create_engine(DATAYESDB_CONNECTION_STRING)
    with engine.connect() as conn:
        close = pd.read_sql(query_close, conn)
    c_1 = close.copy()
    c_2 = close.copy()
    c_1['ticker'] = c_1['ticker'].replace({'000300': '沪深300', '000016': '上证50', '000852': '中证1000', '000905': '中证500'})
    c_2['ticker'] = c_2['ticker'].replace({'000300': 'HS300', '000016': 'SH50', '000852': 'ZZ1000', '000905': 'ZZ500'})
    close = pd.concat([c_1, c_2], axis=0, ignore_index=True)
    return close

def get_futures_price_include_index(date):
    futures_price = get_futures_price(date)
    index_price = get_main_index_close(date)
    index_price['pre_settle'] = index_price['pre_close']
    index_price['settle'] = index_price['close']
    return pd.concat([index_price, futures_price])

def get_index_close(index, date) -> float:
    query_close = """
    SELECT 
        TICKER_SYMBOL as ticker,
        CLOSE_INDEX as close
    FROM mkt_idxd
    WHERE 
    TICKER_SYMBOL IN ('{}')
    AND
    TRADE_DATE="{}"
    """.format(index, date)
    engine = create_engine(DATAYESDB_CONNECTION_STRING)
    with engine.connect() as conn:
        close = pd.read_sql(query_close, conn).set_index('ticker').loc[index, 'close']
    return close


def get_stock_close(date):
    engine = create_engine(DATAYESDB_CONNECTION_STRING)
    query="""
    SELECT 
        TICKER_SYMBOL as ticker,
        CLOSE_PRICE as close
    FROM mkt_equd

    WHERE TRADE_DATE="{}"
    """.format(date)
    with engine.connect() as conn:
        close=pd.read_sql(query,conn)
    close['ticker'] = close['ticker'].astype(int)
    return close 


def get_stock_adj_close(date):
    engine = create_engine(DATAYESDB_CONNECTION_STRING)
    query="""
    SELECT 
        CONVERT( TICKER_SYMBOL, UNSIGNED INTEGER) as ticker,
        CLOSE_PRICE_1 as close
    FROM mkt_equd_adj

    WHERE TRADE_DATE="{}"
    """.format(date)
    with engine.connect() as conn:
        close=pd.read_sql(query,conn)
    # close['ticker'] = close['ticker'].astype(int)
    return close 

def get_st_stocks():
    engine = create_engine(DATAYESDB_CONNECTION_STRING)
    query="""
        SELECT CONVERT( t1.TICKER_SYMBOL, UNSIGNED INTEGER) as ticker, t1.PARTY_STATE, t1.EFF_DATE
        FROM equ_inst_sstate t1
        JOIN (
            SELECT TICKER_SYMBOL, MAX(EFF_DATE) AS max_eff_time
            FROM equ_inst_sstate
        #     WHERE PARTY_STATE = 2
            WHERE EFF_DATE <= NOW()
            GROUP BY TICKER_SYMBOL
        ) t2
        ON t1.TICKER_SYMBOL = t2.TICKER_SYMBOL AND t1.EFF_DATE = t2.max_eff_time
        WHERE 
        t1.PARTY_STATE in (2, 6)
    """
    with engine.connect() as conn:
        df=pd.read_sql(query,conn)
    return df


def getProductDict(date):
    query="""
    SELECT
        product_id,product_name
    FROM trade.register_product
    WHERE into_date<="{}" and (out_date is null or out_date>"{}")
    """.format(date,date)
    engine = create_engine(TRADEDB_CONNECTION_STRING)
    with engine.connect() as conn:
    # conn = pymysql.connect(host=trade_dict["host"], user=trade_dict["user"], password=trade_dict["password"],port=trade_dict["port"])
        product_dict = pd.read_sql(query, conn).set_index("product_id")["product_name"].to_dict()
    # conn.close()
    
    # print(f'product_dict:{product_dict}')
    return product_dict

def getStrategyBenchDict(date):
    query = """
        SELECT
            strategy_id,benchmark
        FROM trade.register_strategy
        WHERE into_date<="{}" and (out_date is null or out_date>"{}")
        """.format(date, date)
    engine = create_engine(TRADEDB_CONNECTION_STRING)
    # conn = pymysql.connect(host=trade_dict["host"], user=trade_dict["user"], password=trade_dict["password"],
    #                        port=trade_dict["port"])
    with engine.connect() as conn:
        strategy_dict = pd.read_sql(query, conn).set_index("strategy_id")["benchmark"].to_dict()
    # conn.close()
    
    # print('-----------------------------')
    # print(f'strategy_dict:{strategy_dict}')
    # print('-----------------------------')
    return strategy_dict

def getAccountStrategyWeight(date):
    query="""
            SELECT
                product_id,
                strategy_id,
                weight,
                effective_date                                
            FROM trade.info_strategy_info AS isi
            WHERE effective_date<="{}"
        """.format(date+"091000")
    engine = create_engine(TRADEDB_CONNECTION_STRING)
    with engine.connect() as conn:
    # conn = pymysql.connect(host=trade_dict["host"], user=trade_dict["user"], password=trade_dict["password"], port=trade_dict["port"])
        account_df = pd.read_sql(query, conn).sort_values("effective_date")
        # print(f'account_df:{account_df}')
        account_df=account_df.groupby("product_id")["effective_date"].max().reset_index().merge(account_df,on=["product_id", "effective_date"],how='inner').pivot(index="product_id",columns="strategy_id",values="weight").fillna(0)
    # conn.close()
    
    # print(f'account_df:{account_df}')
    return account_df

def getProductConstitute(date):
    product_dict=getProductDict(date)
    account_df=getAccountStrategyWeight(date).rename(columns=getStrategyBenchDict(date))
    account_df=account_df[account_df.index.isin(product_dict.keys())].rename(product_dict).stack()
    account_df=account_df[account_df>0].reset_index().groupby(["product_id","strategy_id"])[0].sum().unstack().fillna(0)
    account_df=account_df.reset_index().rename(columns={'product_id': 'product_name'})
    account_df.columns.name = None
    return account_df


# ------------------------------

def get_product_target_futures_unit(date):
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    
    products_weight = getProductConstitute(date)
    # print(f'products_weight: {products_weight}')
    index_df = get_index_close_df(pre_date)
    
    # append 2000 as 1000 index
    s_ZZ2000 = index_df.loc['ZZ1000', :].copy()
    s_ZZ2000.name = 'ZZ2000'
    
    # print(f's_ZZ2000: {s_ZZ2000}')
    index_df = pd.concat([index_df, s_ZZ2000.to_frame().T], axis=0, ignore_index=False)
    
    # print(f'index_df: {index_df}')
    futures_target_unit = products_weight.set_index('product_name') / index_df['close'] / index_df['multiplier']
    # print(futures_target_unit)
    
    return futures_target_unit


# def get_product_future_df(date):
#     FUTURE_HOLD_DIR = '期货汇总'
#     future_hold_file = os.path.join(FUTURE_HOLD_DIR, 'hold', f'hold_{date}.xls')
#     df = read_remote_file(future_hold_file, src_type='dav')
#     def map_values(value):
#         if 'IC' in value or '中证500' in value:
#             return 'ZZ500'
#         elif 'IM' in value or '中证1000' in value:
#             return 'ZZ1000'
#         elif 'IF' in value or '沪深300' in value:
#             return 'HS300'
#         elif 'IH' in value or '上证50' in value:
#             return 'SZ50'
#         else:
#             return value  # 保留其他值不变
#     df['ticker'] = df['ticker'].map(map_values)

#     df = df[['product_name', 'ticker', 'volume']].groupby(['product_name', 'ticker']).sum().reset_index()
#     df = df.pivot(index='product_name', columns='ticker', values='volume').fillna(0).reset_index()
#     df.set_index('product_name', inplace=True)
#     return df


# old func
# =====================================================
# def old_get_latest_hold(account_name, date, config={}):
#     # 如果指定了hold_file, 用指定设置, 先dav, 再inner
#     # 如果redis 没有, 先去dav上找date 这天的
#     # 如果dav 没有, 去inner ftp找 date这天的
#     # 如果inner ftp 没有date 的, 再找前一天 pre_date 的
#     # 如果都没有, 用 0 代替
#     if config.get('hold_file') is not None:
#         if config['hold_file'] in sftp_clent_dav.listdir(os.path.join(account_name, 'hold')):
#             df = read_remote_file(path= os.path.join(account_name, 'hold', config['hold_file']), src_type='dav')
#             logger.info(f'读取了 {account_name} hold_file {config["hold_file"]} found in dav')
#         else:
#             df = read_remote_file(path= os.path.join(account_name, 'hold', config['hold_file']), src_type='ninner')
#             logger.info(f'读取了 {account_name} hold_file {config["hold_file"]} found in ninner')
#     else:
#         pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
#         df = df_read_redis(rs, key=f'{date}.stock.{account_name}.hold')
#         if df is None or df.empty:
#             if account_name in sftp_clent_dav.listdir('') and \
#                 f'hold_{date}.xls' in sftp_clent_dav.listdir(os.path.join(account_name, 'hold')):
#                 df = read_remote_file(os.path.join(account_name, 'hold', f'hold_{date}.xls'), src_type='dav')
#                 logger.info(f'{account_name} hold_{date}.xls not found in memory, 读取了 hold_{date}.xls in dav')
#             #  get latest file
#             elif f'hold_{date}.xls' in sftp_clent_ninner.listdir(os.path.join(account_name, 'hold')):
#                 df = read_remote_file(os.path.join(account_name, 'hold', f'hold_{date}.xls'), src_type='ninner')
#                 logger.warning(f'{account_name} hold_{date}.xls not found in memory, 读取了 hold_{date}.xls in ninner')
#             elif f'hold_{pre_date}.xls' in sftp_clent_ninner.listdir(os.path.join(account_name, 'hold')):
#                 df = read_remote_file(os.path.join(account_name, 'hold', f'hold_{pre_date}.xls'), src_type='ninner')
#                 logger.warning(f'{account_name} hold_{date}.xls not found in memory, 读取了 hold_{pre_date}.xls in ninner')

#             else:
#                 df = pd.DataFrame({'ticker': [1], 'volume': [0]})
#                 logger.warning(f'{account_name} hold_{date}.xls not found anywhere, 用 0 替代')

#     # df = df.rename(columns={'ticker': 'ticker', 'volume': 'volume',
#     #                         '代码' : 'ticker', '持仓量' : 'volume',
#     #                         '证券代码' : 'ticker', '持有数量' : 'volume', '当前数量' : 'volume',
#     #                         ''
#     #                         })
#     df = standardize_hold(df)
#     return df


def read_stand_gtrade_hold(tmp_account_name):
    from .standardize_dev import guoxin_gtrade

    hold_rs_key = f'{datetime.datetime.now().strftime("%Y%m%d")}.stock.{tmp_account_name}.hold.origin'
    hold = df_read_redis(rs, hold_rs_key)
    hold = guoxin_gtrade.std_hold(hold)
    return hold


def calc_t0_volume(hold, target):
    t0 = pd.merge(hold, target, on='ticker', how='outer', suffixes=('', '_target'))
    t0 = t0.fillna(0)
    t0['t0_volume'] = t0[['available_volume', 'volume_target']].min(axis=1)
    t0['t0_volume'] = t0['t0_volume'].div(100).astype('int').mul(100)
    # 筛选 688 股票股数小于 200 的不要
    t0['t0_volume'] = np.where((t0['ticker'] >= 688000) & (t0['t0_volume'] < 200), 0, t0['t0_volume'])
    
    # 保留了 volume 为0 的股票
    t0 = t0.fillna(0)
    t0 = t0[['ticker', 't0_volume']]
    
    # t0 = t0[t0['t0_volume'] != 0][['ticker', 't0_volume']]
    # t0 = t0[t0['t0_volume'] > 0][['ticker', 't0_volume']]
    
    return t0


# -------------------------------------------------------
# -------------future related ----------------------------
def get_futures_contract_info(date):
    engine = create_engine(DATAYESDB_CONNECTION_STRING)
    query=f"""
    SELECT TICKER_SYMBOL, EXCHANGE_CD, CONT_MULT_NUM, TRADE_MARGIN_RATIO
    FROM datayes.futu 
    WHERE 
        LIST_DATE<="{date}" 
    AND 
        LAST_TRADE_DATE>="{date}"

    """
    with engine.connect() as conn:
        df=pd.read_sql(query,conn)
        df.rename(
            columns={
                "TICKER_SYMBOL": "ticker",
                "EXCHANGE_CD": "exchange",
                "CONT_MULT_NUM": "multiplier",
                "TRADE_MARGIN_RATIO": "margin_ratio",
            },
            inplace=True,
        )

    # ['XSIE' 'XSGE' 'XDCE' 'XZCE' 'XGFE' 'CCFX']

    # close['ticker'] = close['ticker'].astype(int)
    return df


def get_latest_futuStra_future_hold(account_name, date, timeby='203000'):
    """
    date 为交易日期 trade_date
    针对取用历史数据
    """
    
    # 多次尝试功能
    def try_get_hold_methods(account_name, date):
        methods = [get_hold_dav, get_hold_ftp, make_zero_hold]  # 将方法作为函数对象列表
        for method in methods:
            try:
                result = method(account_name=account_name, date=date)
                # print(f"{method.__name__} succeeded")
                return result
            except Exception as e:
                print(f"{method.__name__} failed:", e)
        raise Exception("All methods failed")

    def make_zero_hold(account_name, date):
        df = pd.DataFrame(columns=['ticker', 'longshort', 'volume', 'today_volume'])
        logger.warning(f'\n{account_name} hold on {date} not found anywhere, 用 0 替代')
        return df

    def get_hold_dav(account_name, date):
        file_name = find_latest_remote_file_on_date(
            sftp_clent_dav,
            os.path.join('期货策略', account_name, "hold"),
            file_prefix="hold_",
            date=date,
            timeby=timeby
        )
        logger.info(f'\n{account_name} dav hold: {file_name}')
        df = read_remote_file(
            os.path.join("期货策略", account_name, "hold", file_name), src_type="dav"
        )
        return df

    # ninner上当前 date 的最新文件
    def get_hold_ftp(account_name, date):

        file_name = find_latest_remote_file_on_date(
            ftp_clent_jeff,
            os.path.join(account_name, "hold"),
            file_prefix="hold_",
            date=date,
            timeby=timeby
        )
        logger.info(f'\n{account_name} ftp hold: {file_name}')
        df = read_remote_file(os.path.join(account_name, "hold", file_name), src_type='jeff')
        return df

    df = try_get_hold_methods(account_name=account_name, date=date)
    return df


# def get_index_constitutions():
#     # 朝阳永续
#     query="""
#     SELECT
#         index_code
#     ,CONVERT(stock_code,UNSIGNED INTEGER) AS ticker
#     ,DATE_FORMAT(into_date,"%%Y%%m%%d") AS Into_date
#     ,DATE_FORMAT(out_date,"%%Y%%m%%d") AS Out_date
#     FROM gds_data.qt_idx_constituents
#     WHERE index_code in ('000300','000905','000852','399006','000985') and is_valid=1
#     """

#     engine = create_engine(ZYYX_CONNECTION_STRING)
#     with engine.connect() as conn:
#         df=pd.read_sql(query,conn)

#     return df
# # index_component_data[(index_component_data["Into_date"] <= each_day) & ((index_component_data["Out_date"].isnull()) | (index_component_data["Out_date"] > each_day))]


def get_index_constitutions_last_month(index_code, date):
    last_month_last_trading_day = get_last_month_last_trading_day(date)
    
    query=f"""
    SELECT 
        c.TICKER_SYMBOL AS ticker,
        c.SEC_SHORT_NAME AS STOCK_NAME,
        a.EFF_DATE,
        a.WEIGHT
    FROM csi_idxm_wt_ashare a 
        LEFT JOIN md_security b ON a.SECURITY_ID=b.SECURITY_ID
        LEFT JOIN md_security c ON a.CONS_ID=c.SECURITY_ID
    WHERE b.TICKER_SYMBOL='{index_code}' /*输入需查询的指数代码*/
        AND a.EFF_DATE='{last_month_last_trading_day}'/*输入需查询的生效日期*/
    ORDER BY c.TICKER_SYMBOL
    """
    engine = create_engine(DATAYESDB_CONNECTION_STRING)
    with engine.connect() as conn:
        df=pd.read_sql(query,conn)

    return df

def get_last_month_last_trading_day(date):
    this_month = datetime.datetime.strptime(date, '%Y%m%d').strftime('%Y%m')
    last_month_last_trading_day = Calendar.last_trading_day(this_month + '01')
    return last_month_last_trading_day.strftime('%Y%m%d')


def get_cons_tag_series(date):
    hs300 = get_index_constitutions_last_month('000300', date)
    hs300['cons'] = 'HS300'
    zz500 = get_index_constitutions_last_month('000905', date)
    zz500['cons'] = 'ZZ500'
    zz1000 = get_index_constitutions_last_month('000852', date)
    zz1000['cons'] = 'ZZ1000'
    zz2000 = get_index_constitutions_last_month('932000', date)
    zz2000['cons'] = 'ZZ2000'
    # print(hs300.tail(2))
    # print(zz500.tail(2))
    # print(zz1000.tail(2))
    # print(zz2000.tail(2))
    
    cons_tag = pd.concat([hs300, zz500, zz1000, zz2000], axis=0, ignore_index=True)
    if cons_tag['ticker'].duplicated().any():
        raise ValueError('指数成分分类存在重复')
    cons = cons_tag.set_index('ticker')['cons']
    
    return cons

def calc_cons_ratio(stocks:pd.DataFrame, cons:pd.Series, price:pd.DataFrame) -> pd.DataFrame:
    stocks = stocks.copy().rename(columns={'symbol':'ticker', 'hold_shares':'volume', 'target_shares': 'volume'})
    price = price.copy().rename(columns={'symbol':'ticker'})
    cons = cons.copy()
    
    cons = cons.to_frame('cons').reset_index()
    cons['ticker'] = cons['ticker'].astype(int)

    stocks = stocks.merge(cons, on='ticker', how='left')
    stocks['cons'] = stocks['cons'].fillna('OTHER')

    stocks = stocks.merge(price, on='ticker', how='left')
    # print(stocks)

    stocks['value'] = stocks['close'] * stocks['volume']
    df = stocks[['cons', 'value']].groupby(['cons'])['value'].sum() / stocks['value'].sum()
    return df



# 申购, 赎回表读取


def update_redeem_accounting_items():
    # 读取第一个表格（业务规则）
    redeems_file = 'others/申购赎回表.xlsx'
    rules = read_remote_file(redeems_file, src_type='dav', sheet_name='规则')
    # 读取第二个表格
    redeems = read_remote_file(redeems_file, src_type='dav', sheet_name='申赎记录', dtype={'申赎日期':'str', '记账日期':'str'})

    # updating
    na_values_redeems = redeems[(redeems['申赎金额'].isna()) | (redeems['申赎份额'].isna()) | (redeems['记账日期'].isna())]
    if na_values_redeems.shape[0] == 0:
        print('无需更新')
        return redeems

    products = na_values_redeems['产品名'].unique()
    for product_name in products:

        product_items = na_values_redeems[na_values_redeems['产品名'] == product_name]
        for index, row in product_items.iterrows():
            # product_name = row['产品名']
            # 在第一个表格中查找对应的产品行
            

            # 记账日
            rule_to_apply = None
            rule_row = rules[rules['产品名'] == product_name]
            if (pd.isna(redeems.at[index, '记账类型'])) and not rule_row.empty:
                default_rule = rule_row.iloc[0]['默认记账']
                special_rule = rule_row.iloc[0]['申购特殊']
                if pd.notna(special_rule) and (row['申赎金额'] > 0 or row['申赎份额'] > 0):
                    rule_to_apply = special_rule
                else:
                    rule_to_apply = default_rule
                    
                # 连续交易特殊
                continuous_rule = rule_row.iloc[0]['连续交易特殊']
                # 如果 rule_to_apply 日继续有申赎交易, 则应用连续交易特殊规则
                if pd.notna(continuous_rule):
                    if continuous_rule == 'T+1':
                        tmp_date = Calendar.next_trading_day(row['申赎日期']).strftime('%Y%m%d')
                    elif continuous_rule == 'T+2':
                        tmp_date = Calendar.next_n_trading_days(row['申赎日期'], 2)[-1].strftime('%Y%m%d')
                    else:
                        raise ValueError(f'未知的记账规则: {continuous_rule}')
                    # print(product_items)
                    # print(f'tmp_date: {tmp_date}')
                    if (product_items['申赎日期'] == tmp_date).any():
                        print(f"{product_name} {row['申赎日期']} 应用连续交易特殊规则 {continuous_rule}")
                        rule_to_apply = continuous_rule
                    
                redeems.at[index, '记账类型'] = rule_to_apply

            # 根据规则计算记账日期
            if redeems.at[index, '记账类型'] == 'T+1':
                redeems.at[index, '记账日期'] = Calendar.next_trading_day(row['申赎日期']).strftime('%Y%m%d')
            elif redeems.at[index, '记账类型'] == 'T+2':
                redeems.at[index, '记账日期'] = Calendar.next_n_trading_days(row['申赎日期'], 2)[-1].strftime('%Y%m%d')
            else:
                raise ValueError(f'未知的记账规则: {rule_to_apply}')
        
        product_nav_series = get_product_nav_series(product_name)
        product_nav_series.index = product_nav_series.index.strftime('%Y%m%d')
        for index, row in product_items.iterrows():
            if pd.isna(redeems.at[index, '申赎净值']) and pd.isna(redeems.at[index, '申赎金额']) and pd.notna(redeems.at[index, '申赎份额']):
                try:
                    nav = product_nav_series.loc[row['申赎日期'], '单位净值']
                except KeyError:
                    print(f"{product_name} {row['申赎日期']} 单位净值不存在")
                    continue
                if isinstance(nav, float):
                    redeems.at[index, '申赎净值'] = nav
                    redeems.at[index, '申赎金额'] = redeems.at[index, '申赎份额'] * nav
                else:
                    raise ValueError(f"{product_name} {row['申赎日期']} 单位净值可能重复")
                
            elif pd.isna(redeems.at[index, '申赎净值']) and pd.notna(redeems.at[index, '申赎金额']) and pd.isna(redeems.at[index, '申赎份额']):
                try:
                    nav = product_nav_series.loc[row['申赎日期'], '单位净值']
                except KeyError:
                    print(f"{product_name} {row['申赎日期']} 单位净值不存在")
                    continue
                if isinstance(nav, float):
                    redeems.at[index, '申赎净值'] = nav
                    redeems.at[index, '申赎份额'] = redeems.at[index, '申赎金额'] / nav
                else:
                    raise ValueError(f"{product_name} {row['申赎日期']} 单位净值可能重复")
    
    update_sheet_in_remote_excel(df=redeems, file_type='xlsx', dest_type='dav', 
                                 dest_path=redeems_file, 
                                #  dest_path='others/测试excel.xlsx', 
                                 sheet_name='申赎记录', 
                                 index=False)
    
    return redeems

# def read_redeems_accounting_items():
#     redeems_file = 'others/申购赎回表.xlsx'
#     rules = read_remote_file(redeems_file, src_type='dav', sheet_name='规则')
#     # 读取第二个表格
#     redeems = read_remote_file(redeems_file, src_type='dav', sheet_name='申赎记录', dtype={'申赎日期':'str'})


