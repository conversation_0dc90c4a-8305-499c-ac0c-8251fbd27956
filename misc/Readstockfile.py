#%%
import pandas as pd
from loguru import logger
# from typing import Literal
import tempfile
import os
import dbf
import dbfread
import csv
from openpyxl import load_workbook



try:
    from typing import Literal
except ImportError:
    from typing_extensions import Literal

from misc.ssh_conn import sftp_methods, ftps, sftps

# import (
#     sftp_clent_dav, 
#     sftp_clent_inner, 
#     sftp_clent_trade, 
#     sftp_clent_gtapi, 
#     sftp_clent_outer,
#     sftp_clent_aws,
#     sftp_clent_work
# )


read_file_configs = {
    "config_1": {
        "type": "csv",
        "args": {
            "encoding": "gbk",
            "sep": ",",
        },
    },
    "config_2": {
        "type": "csv",
        "args": {
            "encoding": "gbk",
            "sep": "\t",
        },
    },
    "config_3": {
        "type": "excel",
        "args": {
            "engine": "openpyxl"
        },
    },
    "config_4": {
        "type": "csv",
        "args": {
            "encoding": "cp1252"
        },
    },
    "config_5": {
        "type": "txt",
        "args": {
            "encoding": "cp1252"
        },
    },
    "config_6": {
        "type": "xlsbutreallyxlsx",
        "args": {
        },
    },
}

def read_file(path:str, 
              src_type: str='local', # Literal['local', 'inner', 'outer', 'dav', 'trade', 'gtapi', 'aws', 'work', 'trade2', 'trade3', 'ninner', 'jeff'] = 'local',
              **kwargs):
    try:
        if path.endswith(".xlsx") or path.endswith(".xls"):
            if "encoding" in kwargs:
                kwargs.pop('encoding')
            df = pd.read_excel(path, **kwargs)
        
        elif path.endswith(".dbf"):
            # if 'codepage' in kwargs:
            #     codepage = kwargs.pop('codepage')
            # else:
            #     codepage = 0xf0  # utf8
            # with dbf.Table(path, codepage=codepage) as table:
            #     df = pd.DataFrame(iter(table), columns=table.field_names)
            if 'encoding' in kwargs:
                encoding = kwargs.pop('encoding')
            else: 
                encoding = 'utf-8'
            
            # 有的dbf 有解析错误
            if 'parserclass' in kwargs:
                parserclass = kwargs.pop('parserclass')
            else:
                parserclass = dbfread.FieldParser
                
            table = dbfread.DBF(path, encoding=encoding, parserclass=parserclass)
            df = pd.DataFrame(iter(table), columns=table.field_names)
        
        else:  # include  ".csv"
            df = pd.read_csv(path, **kwargs)
    # except UnicodeDecodeError:
    except Exception as e:
        # print('catched unicode error')
        # logger.warning('正常读取文件失败: {} \nERROR: {} \n尝试其他读取配置.'.format(path, e))
        # n = 0
        for config in read_file_configs.values():
            # n += 1
            # logger.info('尝试 配置: {}'.format(config))
            try:
                if config["type"] in ["csv", "txt"]:
                    df = pd.read_csv(path, **config["args"], **kwargs)
                    # logger.warning('使用配置 {} \n成功读取文件: {}'.format(config, path))
                    break
                elif config["type"] == "excel":
                    df = pd.read_excel(path, **config["args"], **kwargs)
                    # logger.warning('使用配置 {} \n成功读取文件: {}'.format(config, path))
                    break
                elif config["type"] == "xlsbutreallyxlsx":
                    # 打开文件并读取数据，将其传递给 pandas
                    with open(path, 'rb') as f:
                        df = pd.read_excel(f, engine='openpyxl', **config["args"], **kwargs)
                    # logger.warning('使用配置 {} \n成功读取文件: {}'.format(config, path))
                    break
            except Exception as sub_e:
                continue
        else:
            logger.error('所有配置均无法读取文件: {}'.format(path))
            raise e        
            
    return df

def read_remote_file(path:str,
                     src_type: str, # Literal['inner', 'outer', 'dav', 'trade', 'gtapi', 'aws', 'work', 'trade2', 'trade3', 'ninner', 'jeff'],
                     **kwargs
    ):
    # sftp_methods = sftp_methods
    # {
    #     'inner': sftp_clent_inner,
    #     'outer': sftp_clent_outer,
    #     'dav': sftp_clent_dav,
    #     'trade': sftp_clent_trade,
    #     # 'gtapi': sftp_clent_gtapi
    # }
    sftp_method = sftp_methods[src_type]
    extension = os.path.splitext(path)[-1].lower()
    if extension in ['.xls', '.xlsx', '.csv', '.dbf', '.txt']:
        with tempfile.NamedTemporaryFile(delete=True, suffix=extension) as tmp:
            # print(f'path: {path}')
            sftp_method.get(path, tmp.name)
            df = read_file(tmp.name, **kwargs)
    else:
        raise ValueError('file type must be "csv" , "xls", "xlsx", "dbf", "txt"')
    # logger.info('成功读取文件 src_type:{}, path: {}'.format(src_type, path))
    return df

def mask_chaos_text(text):
    return True if isinstance(text, str) and text.startswith("=\"") and text.endswith("\"") else False

def clean_excel_str(text:str):
    if isinstance(text, str) and text.startswith("=\"") and text.endswith("\""):
        return text.lstrip("=\"").rstrip("\"")
    else:
        return text
    
    
# def read_remote_file_test(path:str,
#                      src_type: Literal['inner', 'outer', 'dav', 'trade', 'gtapi', 'aws', 'work', 'trade2', 'trade3', 'ninner', 'jeff'],
#                      **kwargs
#     ):
#     # sftp_methods = sftp_methods
#     # {
#     #     'inner': sftp_clent_inner,
#     #     'outer': sftp_clent_outer,
#     #     'dav': sftp_clent_dav,
#     #     'trade': sftp_clent_trade,
#     #     # 'gtapi': sftp_clent_gtapi
#     # }
#     from misc.ssh_conn_2 import sftp_methods
#     sftp_method = sftp_methods[src_type]
#     extension = os.path.splitext(path)[-1].lower()
#     if extension in ['.xls', '.xlsx', '.csv', '.dbf', '.txt']:
#         with tempfile.NamedTemporaryFile(delete=True, suffix=extension) as tmp:
#             sftp_method.get(path, tmp.name)
#             df = read_file(tmp.name, **kwargs)
#     else:
#         raise ValueError('file type must be "csv" , "xls", "xlsx", "dbf", "txt"')
#     # logger.info('成功读取文件 src_type:{}, path: {}'.format(src_type, path))
#     return df

def mask_chaos_text(text):
    return True if isinstance(text, str) and text.startswith("=\"") and text.endswith("\"") else False

def clean_excel_str(text:str):
    if isinstance(text, str) and text.startswith("=\"") and text.endswith("\""):
        return text.lstrip("=\"").rstrip("\"")
    else:
        return text
    
    
def write_file(
    df : pd.DataFrame,
    file_type : Literal['csv', 'xls', 'xlsx'],
    # dest_type : Literal['dav', 'inner', 'trade'],
    dest_type : Literal['dav', 'inner', 'trade', 'outer', 'gtapi', 'aws', 'work', 'trade2', 'trade3', 'ninner', 'jeff', 'local'],
    dest_path : str,
    put_confirm : bool = True,
    **kwargs
):
    # sftp_methods = sftp_methods
                    # {'dav': sftp_clent_dav, 
                    # 'inner': sftp_clent_inner,
                    # 'trade': sftp_clent_trade,
                    # 'outer': sftp_clent_outer,
                    # 'gtapi': sftp_clent_gtapi,
                    # }
    sftp_method = sftp_methods[dest_type]

    if dest_type == 'local':    
        if file_type == 'csv':
            df.to_csv(dest_path, encoding= 'gbk', **kwargs)

        elif file_type == 'xls':
            # if pandas.__version__ <= '1.3.0', use xlwt
            if kwargs.get('engine') is None:
                if pd.__version__ <= '1.3.0':
                    kwargs['engine'] = 'xlwt'
                else:
                    kwargs['engine'] = 'openpyxl'
            df.to_excel(dest_path, **kwargs)

        elif file_type == 'xlsx':
            if kwargs.get('engine') is None:
                kwargs['engine'] = 'openpyxl'

            df.to_excel(dest_path, **kwargs)
        else:
            raise ValueError('file_type must be "csv" or "excel"')

    else:           
        if file_type == 'csv':
            with tempfile.NamedTemporaryFile(delete=True, suffix='.csv') as tmp:
                df.to_csv(tmp.name, encoding= 'gbk', **kwargs)
                if dest_type in sftps:
                    sftp_method.put(tmp.name, dest_path, confirm=put_confirm)
                else:
                    sftp_method.put(tmp.name, dest_path)
        elif file_type == 'xls':
            # if pandas.__version__ <= '1.3.0', use xlwt
        
            if kwargs.get('engine') is None:
                if pd.__version__ <= '1.3.0':
                    kwargs['engine'] = 'xlwt'
                else:
                    kwargs['engine'] = 'openpyxl'
            with tempfile.NamedTemporaryFile(delete=True, suffix='.xls') as tmp:
                df.to_excel(tmp.name, **kwargs)
                if dest_type in sftps:
                    sftp_method.put(tmp.name, dest_path, confirm=put_confirm)
                else:
                    sftp_method.put(tmp.name, dest_path)
        elif file_type == 'xlsx':
            if kwargs.get('engine') is None:
                kwargs['engine'] = 'openpyxl'
            with tempfile.NamedTemporaryFile(delete=True, suffix='.xlsx') as tmp:
                df.to_excel(tmp.name, **kwargs)
                if dest_type in sftps:
                    sftp_method.put(tmp.name, dest_path, confirm=put_confirm)
                else:
                    sftp_method.put(tmp.name, dest_path)
        else:
            raise ValueError('file_type must be "csv" or "excel"')

    
    
def append_dbf(df, dest_type, dest_path, dbf_fields_list=None):
    """
    将 df 数据添加到 dbf 文件中
    :param df: 数据
    :param dest_type: 远程连接通道 inner, dav, trade, outer, gtapi, aws, work, trade2, trade3, jeff
    :param dest_path: 远程文件路径
    :return: 是否添加成功
    """
    # load dbf from remote
    sftp_method = sftp_methods[dest_type]
    # tmp_file
    try:
        with tempfile.NamedTemporaryFile(delete=True, suffix='.dbf') as tmp:
            # get dbf file from remote
            sftp_method.get(dest_path, tmp.name)

            # load dbf file
            table = dbf.Table(tmp.name, codepage='utf8')
            
            # check if df.columns == dbf columns
            # if ([f.lower() for f in table.field_names] != [c.lower() for c in df.columns]):
            #     raise ValueError('dbf columns not match df.columns')
            
            # append df to dbf
            table.open(dbf.READ_WRITE)
            for _, row in df.iterrows():
                # print(tuple(row))
                table.append(tuple(row))
            table.close()
            
            # 回传远程文件
            sftp_method.put(tmp.name, dest_path)
            
            return True
        
    except FileNotFoundError:
        logger.warning('目标dbf文件不存在, 创建dbf文件: {}'.format(dest_path))
        if dbf_fields_list is None:
            raise ValueError('目标dbf文件不存在, 需要创建dbf文件, dbf_fields_list 不能为空')
        
        dbf_fields = '; '.join([' '.join(f) for f in dbf_fields_list])
        with tempfile.NamedTemporaryFile(delete=True, suffix='.dbf') as tmp:
            table = dbf.Table(tmp.name, field_specs=dbf_fields, codepage='utf8')
        
            # append df to dbf
            table.open(dbf.READ_WRITE)
            for _, row in df.iterrows():
                # print(tuple(row))
                table.append(tuple(row))
            table.close()
            
            # 回传远程文件
            sftp_method.put(tmp.name, dest_path)
            
            return True
        
    except Exception as e:
        logger.error(e)
        return False
    
    
def append_csv(df, dest_type, dest_path):
    """
    将 df 数据添加到 csv 文件中
    :param df: 数据
    :param dest_type: 远程连接通道 inner, dav, trade, outer, gtapi, aws, work, trade2, trade3, jeff
    :param dest_path: 远程文件路径
    :return: 是否添加成功
    """
    # load dbf from remote
    sftp_method = sftp_methods[dest_type]
    # tmp_file
    try:
        with tempfile.NamedTemporaryFile(delete=True, suffix='.csv') as tmp:
            # get csv file from remote
            sftp_method.get(dest_path, tmp.name)

            # open csv file
            with open(tmp.name, 'a', newline='') as f:
                writer = csv.writer(f)
                for _, row in df.iterrows():
                    writer.writerow(row)
            
            # 回传远程文件
            sftp_method.put(tmp.name, dest_path)
            
            return True

        
    except FileNotFoundError:
        logger.warning('目标csv文件不存在, 创建csv文件: {}'.format(dest_path))
        # if dbf_fields_list is None:
        #     raise ValueError('目标dbf文件不存在, 需要创建dbf文件, dbf_fields_list 不能为空')
        
        col_fields = df.columns.to_list()
        # col_fields = [f'"{c}"' for c in col_fields]
        
        with tempfile.NamedTemporaryFile(delete=True, suffix='.csv') as tmp:
            
            # open csv file
            with open(tmp.name, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(col_fields)
                for _, row in df.iterrows():
                    writer.writerow(row)
            
            # 回传远程文件
            sftp_method.put(tmp.name, dest_path)
            
            return True
        
    except Exception as e:
        logger.error(e)
        return False
    
    
    

def update_sheet_in_remote_excel(df, file_type, dest_type, dest_path, sheet_name='Sheet1', **kwargs):
    
    sftp_method = sftp_methods[dest_type]
    if file_type == 'xlsx' or file_type == 'xls':
        with tempfile.NamedTemporaryFile(delete=True, suffix='.'+file_type) as tmp:

            sftp_method.get(dest_path, tmp.name)

            # update excel file
            _sn = kwargs.pop('sheet_name', None)
            sheet_name = sheet_name if _sn is None else _sn
            
            
            # workbook = load_workbook(tmp.name)
            writer = pd.ExcelWriter(tmp.name, engine='openpyxl', mode='a', if_sheet_exists='overlay')
            # writer.book = workbook
            # writer.sheets = dict((ws.title, ws) for ws in workbook.worksheets)
            
            df.to_excel(writer, sheet_name=sheet_name, **kwargs)
            writer.close()

            # 回传远程文件
            sftp_method.put(tmp.name, dest_path)

            

import openpyxl

def update_xlsx_getdf(file_path, **kwargs):
    reader = openpyxl.load_workbook(file_path, data_only=False)
    if 'engine' not in kwargs:
        kwargs['engine'] = 'openpyxl'
    source_df = pd.read_excel(reader, **kwargs)
    return source_df
    

def update_xlsx_putdf(df:pd.DataFrame, file_path, **kwargs):
    writer = pd.ExcelWriter(file_path,
                        mode='a',
                        if_sheet_exists='overlay',
                        engine='openpyxl')
    df.to_excel(writer, **kwargs)
    writer.close()

def update_remote_xlsx_getdf(file_path, src_type, **kwargs):
    sftp_method = sftp_methods[src_type]
    extension = os.path.splitext(file_path)[-1].lower()
    if extension in ['.xls', '.xlsx',]:
        with tempfile.NamedTemporaryFile(delete=True, suffix=extension) as tmp:
            # print(f'path: {path}')
            sftp_method.get(file_path, tmp.name)
            
            reader = openpyxl.load_workbook(tmp.name, data_only=False)
            if 'engine' not in kwargs:
                kwargs['engine'] = 'openpyxl'
            source_df = pd.read_excel(reader, **kwargs)
            return source_df
    else:
        raise ValueError('file type must be "xls" or "xlsx"')
        
def update_remote_xlsx_putdf(df:pd.DataFrame, file_type, dest_type, dest_path, **kwargs):
    sftp_method = sftp_methods[dest_type]
    if file_type == 'xlsx' or file_type == 'xls':
        with tempfile.NamedTemporaryFile(delete=True, suffix='.'+file_type) as tmp:
            sftp_method.get(dest_path, tmp.name)

            writer = pd.ExcelWriter(tmp.name,
                        mode='a',
                        if_sheet_exists='overlay',
                        engine='openpyxl')
            df.to_excel(writer, **kwargs)
            writer.close()

            # 回传远程文件
            sftp_method.put(tmp.name, dest_path)
    else:
        raise ValueError('file type must be "xls" or "xlsx"')