from imapclient import imap_utf7
from datetime import datetime
import os
from loguru import logger
from datetime import datetime, timedelta
import pandas as pd

from misc.Readstockfile import read_file, write_file
from misc.email import download_excel_attachments
from data_utils.trading_calendar import Calendar

import warnings
warnings.filterwarnings("ignore")



email_host = 'imap.feishu.cn'

# email_addr = '<EMAIL>'
# email_pass = 'V4VwSVVGCK8xanGT'

guzhi_email_addr = '<EMAIL>'
guzhi_email_pass = 'MJMa2VkdTw3RUfSD'



def read_guzhi_file(account_name, date, search_days=7, **more_args):
    # print(date)
    email_folder = guzhi_files_dict[account_name]['email_folder']
    title_date_fmt = guzhi_files_dict[account_name]['title_date_fmt']
    file_date_fmt = guzhi_files_dict[account_name].get('file_date_fmt')
    email_addr = guzhi_files_dict[account_name].get('email_addr')
    email_pass = guzhi_files_dict[account_name].get('email_pass')
    
    
    title_reg = guzhi_files_dict[account_name]['title_reg']
    if title_reg is not None:
        title_reg = title_reg.format(date=datetime.strptime(date, '%Y%m%d').strftime(title_date_fmt))
    
    file_reg = guzhi_files_dict[account_name].get('file_reg')
    if file_reg is not None:
        file_reg = file_reg.format(date=datetime.strptime(date, '%Y%m%d').strftime(file_date_fmt))
    read_file_args = guzhi_files_dict[account_name]['args']
    read_file_args.update(more_args)
    # search_start_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    search_start_date = date
    search_end_date = (datetime.strptime(date, '%Y%m%d') + timedelta(days=search_days)).strftime('%Y%m%d')
    # print('file_reg:', file_reg)

    # email & password
    if email_addr is None or email_pass is None:
        email_addr = guzhi_email_addr
        email_pass = guzhi_email_pass
    
    files_path = download_excel_attachments(
        server=email_host,
        email_user=email_addr,
        email_pass=email_pass,
        folder=email_folder,
        start_date=search_start_date,
        end_date=search_end_date,
        title_reg=title_reg,
        attachment_reg=file_reg
    )
    # print(files_path)
    if len(files_path) == 0:
        raise FileNotFoundError(f'{account_name} {date} 没有找到附件')
    elif len(files_path) > 1:
        files_path.sort(reverse=True)
        for file in files_path.copy():
            if '以此为准' in file:
                files_path = [file]
                print(f'找"以此为准"的附件 {file}')
        if len(files_path) > 1:
            logger.error(f'{account_name} {date} 找到多个邮件附件')
            print(files_path)
    file_path = files_path[0]
    df = read_file(file_path, src_type='local', **read_file_args)
    os.remove(file_path)
    return df

# df = read_guzhi_file('远航安心中性1号', guzhi_files_dict, date='********')
# print(df)


def calcu_account_guzhi_pnl_guoxin(account_name, date):
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    
    guzhi = read_guzhi_file(account_name, date)
    pre_guzhi = read_guzhi_file(account_name, pre_date)
    # print(guzhi.loc['多头', '浮动盈亏'])
    # print(guzhi.loc['多头', '开仓表存续分红'])
    
    long_pnl = (guzhi.loc['多头', '浮动盈亏'] + guzhi.loc['多头', '开仓表存续分红'] \
        + guzhi.loc['多头', '实现盈亏（已扣平仓费用，加上终止的红利）'] \
        - guzhi.loc['多头', '开仓交易费用']) \
        - (pre_guzhi.loc['多头', '浮动盈亏'] + pre_guzhi.loc['多头', '开仓表存续分红'] \
        + pre_guzhi.loc['多头', '实现盈亏（已扣平仓费用，加上终止的红利）'] \
        - pre_guzhi.loc['多头', '开仓交易费用']) 
    
    shrot_pnl = (guzhi.loc['空头', '浮动盈亏'] + guzhi.loc['空头', '开仓表存续分红'] \
        + guzhi.loc['空头', '实现盈亏（已扣平仓费用，加上终止的红利）'] \
        - guzhi.loc['空头', '开仓交易费用']) \
        - (pre_guzhi.loc['空头', '浮动盈亏'] + pre_guzhi.loc['空头', '开仓表存续分红'] \
        + pre_guzhi.loc['空头', '实现盈亏（已扣平仓费用，加上终止的红利）'] \
        - pre_guzhi.loc['空头', '开仓交易费用']) 
    # guzhi = 
    # print(guzhi)
    # print(long_pnl, shrot_pnl)
    return long_pnl, shrot_pnl

# long_pnl, shrot_pnl = calcu_account_guzhi_pnl_guoxin('远航安心中性1号', guzhi_files_dict, date='********')
# print(long_pnl, shrot_pnl)


def calcu_account_guzhi_pnl_dongfang(account_name, date):
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')

    guzhi = read_guzhi_file(account_name, date)
    guzhi.index = pd.to_datetime(guzhi.index.astype(str))
    long_pnl = (
        guzhi.loc[date, ("浮动收益合计", "多头")]
        + guzhi.loc[date, ("现金分红", "合计")]
        + guzhi.loc[date, ("红利税", "合计")]
    ) - (
        guzhi.loc[pre_date, ("浮动收益合计", "多头")]
        + guzhi.loc[pre_date, ("现金分红", "合计")]
        + guzhi.loc[pre_date, ("红利税", "合计")]
    )
    short_pnl = (
        guzhi.loc[date, ("浮动收益合计", "空头")]
        - guzhi.loc[pre_date, ("浮动收益合计", "空头")]
    )
    # print(long_pnl, short_pnl)
    return long_pnl, short_pnl


def calcu_account_guzhi_pnl_yinhe(account_name, date):
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    def get_pnl(guzhi:pd.DataFrame, longshort, date):
        ls_mask = guzhi['持仓方向'] == longshort
        res = guzhi[ls_mask].loc[date, '总浮动盈亏(结算货币)'].sum() + guzhi[ls_mask].loc[date, '实现损益'].sum()
        return res

    guzhi = read_guzhi_file(account_name, date)
    pre_guzhi = read_guzhi_file(account_name, pre_date)
    guzhi.index = pd.to_datetime(guzhi.index.astype(str))
    pre_guzhi.index = pd.to_datetime(pre_guzhi.index.astype(str))
    # print(guzhi.loc['多头', '浮动盈亏'])
    # print(guzhi.loc['多头', '开仓表存续分红'])
    # print(guzhi)
    long_pnl = get_pnl(guzhi, '看多', date) - get_pnl(pre_guzhi, '看多', pre_date)
    short_pnl = get_pnl(guzhi, '看空', date) - get_pnl(pre_guzhi, '看空', pre_date)
    # print(long_pnl, short_pnl)
    return long_pnl, short_pnl

def calcu_account_guzhi_pnl_guangfa(account_name, date):
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    more_args = {
            'sheet_name' : '每日损益情况',
            'index_col' : 0,
            'header' : [2,3],
    }
    
    guzhi = read_guzhi_file(account_name, date, **more_args)
    # guzhi.reset_index(inplace=True)
    contract_id = guzhi_files_dict[account_name]['contract_id']
    guzhi = guzhi[guzhi[('组合编号', 'Unnamed: 1_level_1')] == contract_id]
    guzhi.index = [pd.to_datetime(str(x)).strftime('%Y%m%d') for x in guzhi.index]
    # guzhi.set_index('日期', inplace=True)
    # guzhi.index = pd.to_datetime(guzhi.index.astype(str))
    # # 除去重复的日期, 保留第一行
    # guzhi = guzhi[~guzhi.index.duplicated(keep='first')]

    long_pnl = (
        guzhi.loc[date, ("浮动收益合计", "多头")]
        # + guzhi.loc[date, ("现金分红", "合计")]
        # + guzhi.loc[date, ("红利税", "合计")]
    ) - (
        guzhi.loc[pre_date, ("浮动收益合计", "多头")]
        # + guzhi.loc[pre_date, ("现金分红", "合计")]
        # + guzhi.loc[pre_date, ("红利税", "合计")]
    )
    short_pnl = (
        guzhi.loc[date, ("浮动收益合计", "空头")]
        - guzhi.loc[pre_date, ("浮动收益合计", "空头")]
    )
    # print(long_pnl, short_pnl)
    return long_pnl, short_pnl

    # def get_pnl(guzhi:pd.DataFrame, longshort, date):
    #     ls_mask = guzhi['持仓方向'] == longshort
    #     res = guzhi[ls_mask].loc[date, '总浮动盈亏(结算货币)'].sum() + guzhi[ls_mask].loc[date, '实现损益'].sum()
    #     return res

    # guzhi = read_guzhi_file(account_name, date)
    # pre_guzhi = read_guzhi_file(account_name, pre_date)
    # guzhi.index = pd.to_datetime(guzhi.index.astype(str))
    # pre_guzhi.index = pd.to_datetime(pre_guzhi

def calcu_account_guzhi_pnl_guolian(account_name, date):
    # pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    guzhi = read_guzhi_file(account_name, date)
    contract_id = guzhi_files_dict[account_name]['contract_id']
    long_pnl = guzhi.loc[contract_id, 'T日多头收益（单位：元）']
    short_pnl = guzhi.loc[contract_id, 'T日空头收益（单位：元）']
    # print(long_pnl, short_pnl)
    return long_pnl, short_pnl


def calcu_account_guzhi_pnl_guojun(account_name, date):
    guzhi = read_guzhi_file(account_name, date)
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    pre_guzhi = read_guzhi_file(account_name, pre_date)
    
    guzhi = guzhi[['金额']]
    pre_guzhi = pre_guzhi[['金额']]
    long_pnl = (guzhi.loc['多头浮动权益收益金额', '金额'] - pre_guzhi.loc['多头浮动权益收益金额', '金额']) \
        + (guzhi.loc['多头权益收益金额', '金额'] - pre_guzhi.loc['多头权益收益金额', '金额']) \
        + (guzhi.loc['多头盈亏累计金额', '金额'] - pre_guzhi.loc['多头盈亏累计金额', '金额']) 
    short_pnl = (guzhi.loc['空头浮动权益收益金额', '金额'] - pre_guzhi.loc['空头浮动权益收益金额', '金额']) \
        + (guzhi.loc['空头权益收益金额', '金额'] - pre_guzhi.loc['空头权益收益金额', '金额']) \
        + (guzhi.loc['空头盈亏累计金额', '金额'] - pre_guzhi.loc['空头盈亏累计金额', '金额']) 
    # print(long_pnl, short_pnl)
    return long_pnl, short_pnl

def calcu_account_guzhi_pnl_zheshang(account_name, date):
    guzhi = read_guzhi_file(account_name, date, sheet_name='持仓了结', header=1)
    realized_long = guzhi[guzhi['持仓方向']=='多头']['权益端平仓损益'].sum()
    realized_short = guzhi[guzhi['持仓方向']=='空头']['权益端平仓损益'].sum()
    # print(realized_long, realized_short)
    
    guzhi = read_guzhi_file(account_name, date, sheet_name='持仓明细', header=3)
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    pre_guzhi = read_guzhi_file(account_name, pre_date, sheet_name='持仓明细', header=3)
    # print(guzhi.head(4))
    # print(guzhi.index)
    # print(guzhi.columns)
    long_mask = guzhi['标的多空方向']=='多头'
    pre_long_mask = pre_guzhi['标的多空方向']=='多头'
    short_mask = guzhi['标的多空方向']=='空头'
    pre_short_mask = pre_guzhi['标的多空方向']=='空头'

    unrealized_long = (guzhi[long_mask]['待实现股息金额'].sum() \
                + guzhi[long_mask]['权益端待实现收益'].sum() \
                + guzhi[long_mask]['固定端待实现券息'].sum()) \
                    - (pre_guzhi[pre_long_mask]['待实现股息金额'].sum() \
                + pre_guzhi[pre_long_mask]['权益端待实现收益'].sum() \
                + pre_guzhi[pre_long_mask]['固定端待实现券息'].sum())
    unrealized_short = (guzhi[short_mask]['待实现股息金额'].sum() \
                + guzhi[short_mask]['权益端待实现收益'].sum() \
                + guzhi[short_mask]['固定端待实现券息'].sum()) \
                    - (pre_guzhi[pre_short_mask]['待实现股息金额'].sum() \
                + pre_guzhi[pre_short_mask]['权益端待实现收益'].sum() \
                + pre_guzhi[pre_short_mask]['固定端待实现券息'].sum())
                
    # print(unrealized_long, unrealized_short)
    long_pnl = realized_long + unrealized_long
    short_pnl = realized_short + unrealized_short
    # print(long_pnl, short_pnl)
    return long_pnl, short_pnl

def calcu_account_guzhi_pnl_huatai_otc(account_name, date):
    guzhi = read_guzhi_file(account_name, date, sheet_name='估值表', index_col='日期')
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    pre_guzhi = read_guzhi_file(account_name, pre_date, sheet_name='估值表', index_col='日期')

    guzhi.index = pd.to_datetime(guzhi.index).strftime('%Y%m%d')
    pre_guzhi.index = pd.to_datetime(pre_guzhi.index).strftime('%Y%m%d')
    long_pnl = guzhi.loc[date, '客户估值'] - pre_guzhi.loc[pre_date, '客户估值']
    short_pnl = 0
    return long_pnl, short_pnl
    
def calcu_account_guzhi_pnl_zhongxin_otc(account_name, date):
    guzhi = read_guzhi_file(account_name, date, sheet_name='标的持仓')
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    pre_guzhi = read_guzhi_file(account_name, pre_date, sheet_name='标的持仓')
    
    contract_id = guzhi_files_dict[account_name]['contract_id']
    guzhi = guzhi[guzhi['清单编号'] == contract_id]
    pre_guzhi = pre_guzhi[pre_guzhi['清单编号'] == contract_id]
    
    guzhi.rename(columns={
        '证券代码': 'ticker',
        '持仓方向': 'direction',
        '总浮动盈亏_含股息(交易货币)': 'unrealized_pnl',
        '总实现盈亏_不含股息(交易货币)': 'realized_pnl',
    }, inplace=True)
    
    pre_guzhi.rename(columns={
        '证券代码': 'ticker',
        '持仓方向': 'direction',
        '总浮动盈亏_含股息(交易货币)': 'unrealized_pnl',
        '总实现盈亏_不含股息(交易货币)': 'realized_pnl',
    }, inplace=True)
    long_pnl = guzhi[guzhi['direction']=='多头']['unrealized_pnl'].sum() + guzhi[guzhi['direction']=='多头']['realized_pnl'].sum()
    short_pnl = guzhi[guzhi['direction']=='空头']['unrealized_pnl'].sum() + guzhi[guzhi['direction']=='空头']['realized_pnl'].sum()
    
    pre_long_pnl = pre_guzhi[pre_guzhi['direction']=='多头']['unrealized_pnl'].sum() + pre_guzhi[pre_guzhi['direction']=='多头']['realized_pnl'].sum()
    pre_short_pnl = pre_guzhi[pre_guzhi['direction']=='空头']['unrealized_pnl'].sum() + pre_guzhi[pre_guzhi['direction']=='空头']['realized_pnl'].sum()
    
    long_pnl = long_pnl - pre_long_pnl
    short_pnl = short_pnl - pre_short_pnl

    return long_pnl, short_pnl
    
def calcu_account_statement_pnl_zhongxin(account_name, date):
    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
    guzhi = read_guzhi_file(account_name, date)
    pre_guzhi = read_guzhi_file(account_name, pre_date)

    guzhi = guzhi.iloc[0:1][['总资产','净资产','资金余额','冻结资金','证券市值','保证金可用','可取金额']]
    pre_guzhi = pre_guzhi.iloc[0:1][['总资产','净资产','资金余额','冻结资金','证券市值','保证金可用','可取金额']]

    long_pnl = guzhi.loc[guzhi.index[0], '净资产'] - pre_guzhi.loc[pre_guzhi.index[0], '净资产']
    short_pnl = 0
    return long_pnl, short_pnl
    
guzhi_files_dict = {
    '远航安心中性1号' : {
        'email_folder' : '中性1号-国信dma',
        'title_reg' : '国信证券收益互换估值表（多空1）_宽辅远航安心市场中性1号私募证券投资基金_{date}',
        'title_date_fmt' : '%Y-%m-%d',
        'file_reg' : '国信证券收益互换估值表（多空1）_宽辅远航安心市场中性1号私募证券投资基金_{date}',
        'file_date_fmt' : '%Y-%m-%d',
        'args' : {
            'sheet_name' : '盈亏数据',
            'index_col' : 0
        },
        'calcu_func' : calcu_account_guzhi_pnl_guoxin
    },
    '宽辅泓涛专享1号' : {
        'email_folder' : '泓涛专享1号-国信dma',
        'title_reg' : '国信证券收益互换估值表（多空1）_宽辅泓涛专享1号私募证券投资基金_{date}',
        'title_date_fmt' : '%Y-%m-%d',
        'file_reg' : '国信证券收益互换估值表（多空1）_宽辅泓涛专享1号私募证券投资基金_{date}',
        'file_date_fmt' : '%Y-%m-%d',
        'args' : {
            'sheet_name' : '盈亏数据',
            'index_col' : 0
        },
        'calcu_func' : calcu_account_guzhi_pnl_guoxin
    },
    '宽辅专享1号' : {
        'email_folder' : '宽辅专享1号-国信dma',
        'title_reg' : '国信证券收益互换估值表（多空1）_宽辅专享1号私募证券投资基金_{date}',
        'title_date_fmt' : '%Y-%m-%d',
        'file_reg' : '国信证券收益互换估值表（多空1）_宽辅专享1号私募证券投资基金_{date}',
        'file_date_fmt' : '%Y-%m-%d',
        'args' : {
            'sheet_name' : '盈亏数据',
            'index_col' : 0
        },
        'calcu_func' : calcu_account_guzhi_pnl_guoxin
    },
    '远航安心中性3号' : {
        'email_folder' : '中性1号-3号国元',
        'title_reg' : '宽辅远航安心市场中性1号私募证券投资基金盈亏持仓表A产品{date}',
        'title_date_fmt' : '%Y%m%d',
        'file_reg' : '宽辅远航安心市场中性1号私募证券投资基金盈亏持仓表A产品{date}',
        'file_date_fmt' : '%Y%m%d',
        'args' : {
            'sheet_name' : '盈亏数据',
            'index_col' : 0
        },
        'calcu_func' : calcu_account_guzhi_pnl_guoxin
    },
    '宽辅泓涛专享1号_东方' : {
        'email_folder' : '泓涛专享1号-东方dma',
        'title_reg' : '【衍生品估值】{date}估值报告-宽辅泓涛专享1号私募证券投资基金',
        'title_date_fmt' : '%Y年%m月%d日',
        'file_reg' : 'DEA0000000035_宽辅泓涛专享1号私募证券投资基金_{date}',
        'file_date_fmt' : '%Y%m%d',
        'args' : {
            'sheet_name' : '每日损益情况',
            'index_col' : 0,
            # 'skiprows' : 3,
            'header' : [2,3],
        },
        'calcu_func' : calcu_account_guzhi_pnl_dongfang
    },
    '远航安心中性1号_银河' : {
        'email_folder' : '中性1号-银河dma',
        'title_reg' : '【银河证券】场外日报表与交易回执-宽辅远航安心市场中性1号私募证券投资基金-{date}',
        'title_date_fmt' : '%Y%m%d',
        'file_reg' : '日报表&交易回执-宽辅远航安心市场中性1号私募证券投资基金-{date}',
        'file_date_fmt' : '%Y%m%d',
        'args' : {
            'sheet_name' : '标的持仓',
            'index_col' : 0,
        },
        'calcu_func' : calcu_account_guzhi_pnl_yinhe
    },
    '远航安心中性6号_银河' : {
        'email_folder' : '中性6号-银河dma',
        'title_reg' : '【银河证券】场外日报表与交易回执-宽辅专享7号私募证券投资基金-{date}',
        'title_date_fmt' : '%Y%m%d',
        'file_reg' : '日报表&交易回执-宽辅专享7号私募证券投资基金-{date}',
        'file_date_fmt' : '%Y%m%d',
        'args' : {
            'sheet_name' : '标的持仓',
            'index_col' : 0,
        },
        'calcu_func' : calcu_account_guzhi_pnl_yinhe
    },
    '宽辅专享5号' : {
        'email_folder' : '专享5号-银河dma',
        'title_reg' : '【银河证券】场外日报表与交易回执-宽辅专享5号私募证券投资基金-{date}',
        'title_date_fmt' : '%Y%m%d',
        'file_reg' : '日报表&交易回执-宽辅专享5号私募证券投资基金-{date}',
        'file_date_fmt' : '%Y%m%d',
        'args' : {
            'sheet_name' : '标的持仓',
            'index_col' : 0,
        },
        'calcu_func' : calcu_account_guzhi_pnl_yinhe
    },
    '宽辅泓涛专享1号_广发' : {
        'email_folder' : '泓涛专享1号-广发dma',
        'title_reg' : '{date}估值报告宽辅泓涛专享1号私募证券投资基金',
        'title_date_fmt' : '%Y年%m月%d日',
        'file_reg' : '广发证券场外衍生品多空互换估值报告（宽辅泓涛专享1号私募证券投资基金）{date}',
        'file_date_fmt' : '%Y-%m-%d',
        'args' : {},
        'calcu_func' : calcu_account_guzhi_pnl_guangfa,
        'contract_id' : 'TRS-KFHTZX12024-01',
    },
    
    '远航安心中性6号_国联' : {
        'email_folder' : '中性6号-国联dma',
        'title_reg' : '【新版日报】国联证券-{date}-估值日报（宽辅远航安心市场中性6号）',
        'title_date_fmt' : '%Y年%m月%d日',
        'file_reg' : '宽辅远航安心市场中性6号私募证券投资基金-CP23121303-{date}',
        'file_date_fmt' : '%Y%m%d',
        'args' : {
            'sheet_name' : '存续互换合约',
            'index_col' : '交易编号'
        },
        'contract_id' : 'TS-Swap-2024-DMA052',
        'calcu_func' : calcu_account_guzhi_pnl_guolian
    },
    
    '远航安心中性6号_国君' : {
        'email_folder' : '中性6号-国君dma',
        # 'title_reg' : '国泰君安多空收益互换盯市日报（海南宽辅私募基金管理合伙企业（有限合伙）-宽辅远航安心市场中性6号私募证券投资基金\) {date}',
        'title_reg' : '国泰君安多空收益互换盯市日报（海南宽辅私募基金管理合伙企业（有限合伙）-宽辅专享7号私募证券投资基金\) {date}',
        'title_date_fmt' : '%Y-%m-%d',
        'file_reg' : '多空组合盯市表\(海南宽辅私募基金管理合伙企业（有限合伙）-宽辅专享7号私募证券投资基金\){date}',
        'file_date_fmt' : '%Y%m%d',
        'args' : {
            'sheet_name' : '多空组合盯市总表（LSTRS)',
            'index_col' : '项目',
            'header' : 6,
        },
        'calcu_func' : calcu_account_guzhi_pnl_guojun
    },
    '宽辅专享1号_浙商' : {
        'email_folder' : '宽辅专享1号-浙商dma',
        'title_reg' : '【浙商-宽辅专享1号】DMA估值报告及补充交易确认书_{date}',
        'title_date_fmt' : '%Y%m%d',
        'file_reg' : '宽辅专享1号私募证券投资基金_估值报告_{date}',
        'file_date_fmt' : '%Y%m%d',
        'args' : {},
        'calcu_func' : calcu_account_guzhi_pnl_zheshang
    },

    '宽辅臻好专享_华泰' : {
        'email_folder' : '臻好专享-华泰证券场外券',
        'title_reg' : '【收益互换】-【估值文件】-【海南宽辅私募基金管理合伙企业（有限合伙）作为受托资产的管理人并代表宽辅臻好专享私募证券投资基金】{date}',
        'title_date_fmt' : '%Y%m%d',
        'file_reg' : 'HTSC_{date}_海南宽辅私募基金管理合伙企业（有限合伙）作为受托资产的管理人',
        'file_date_fmt' : '%Y%m%d',
        'args' : {},
        'calcu_func' : calcu_account_guzhi_pnl_huatai_otc
    },

    '宽辅泓涛专享1号_中信多空' : {
        'email_folder' : '泓涛专享1号-多空',
        'title_reg' : '【中信证券】关于【EQSWAP118966客户\[宽辅泓涛专享1号\]】的履约保障报告{date}',
        'title_date_fmt' : '%Y%m%d',
        'file_reg' : 'EQSWAP118966_客户估值报表_{date}.xlsx',
        'file_date_fmt' : '%Y%m%d',
        'args' : {},
        'calcu_func' : calcu_account_guzhi_pnl_zhongxin_otc,
        'contract_id' : '118966--CNY--RM'
    },
    
    '宽辅泓涛专享1号_中信两融' : {
        'email_addr' : '<EMAIL>',
        'email_pass' : 'MLhGsK6cTd3BXOTR',
        'email_folder' : '宽辅泓涛专享1号/泓涛1号-中信证券（信用户）',
        'title_reg' : '宽辅泓涛专享1号-客户对账单-8009314415_{date}',
        'title_date_fmt' : '%Y%m%d',
        'file_reg' : '宽辅泓涛专享1号-融资融券账户对账单（含约定融资＆约定融券）-8009314415_{date}',
        'file_date_fmt' : '%Y%m%d',
        'args' : {'skiprows' : 15},
        'calcu_func' : calcu_account_statement_pnl_zhongxin
    },

    '远航安心中性6号_中信多空' : {
        'email_folder' : '中性6号-中信dma',
        # 'title_reg' : '【中信证券】关于【EQSWAP118965客户\[宽辅远航安心市场中性6号\]】的履约保障报告{date}',
        'title_reg' : '【中信证券】关于【EQSWAP118965客户\[宽辅专享7号\]】的履约保障报告{date}',
        'title_date_fmt' : '%Y%m%d',
        'file_reg' : 'EQSWAP118965_客户估值报表_{date}.xlsx',
        'file_date_fmt' : '%Y%m%d',
        'args' : {},
        'calcu_func' : calcu_account_guzhi_pnl_zhongxin_otc,
        'contract_id' : '118965--CNY--RM--B'
    },

    '宽辅专享6号_中信多空' : {
        'email_folder' : '专享6号-中信多空',
        # 'title_reg' : '【中信证券】关于【EQSWAP118965客户\[宽辅远航安心市场中性6号\]】的履约保障报告{date}',
        'title_reg' : '【中信证券】关于【EQSWAP121531客户\[宽辅专享6号\]】的履约保障报告{date}',
        'title_date_fmt' : '%Y%m%d',
        'file_reg' : 'EQSWAP121531_客户估值报表_{date}.xlsx',
        'file_date_fmt' : '%Y%m%d',
        'args' : {},
        'calcu_func' : calcu_account_guzhi_pnl_zhongxin_otc,
        'contract_id' : '121531--CNY--RM'
    },

    '宽辅思贤专享中性1号_中金多空' : {
        'email_folder' : '思贤专享1号-中金场外多空',
        'title_reg' : '宽辅思贤专享中性1号私募证券投资基金估值报告{date}',
        'title_date_fmt' : '%Y-%m-%d',
        'file_reg' : '{date}宽辅思贤专享中性1号私募证券投资基金-DMA.xlsx',
        'file_date_fmt' : '%Y-%m-%d',
        'args' : {},
        'calcu_func' : None,
    },
    
    '宽辅专享6号_中金场外' : {
        'email_folder' : '专享6号-中金场外多空',
        'title_reg' : '宽辅专享6号私募证券投资基金估值报告{date}',
        'title_date_fmt' : '%Y-%m-%d',
        'file_reg' : '{date}宽辅专享6号私募证券投资基金-DMA.xlsx',
        'file_date_fmt' : '%Y-%m-%d',
        'args' : {},
        'calcu_func' : None,
    },
    
}


def collect_account_pnl(date):
    # results_dict = {}
    df = []
    for account_name in guzhi_files_dict.keys():
    # [
    #     '远航安心中性1号',
    #     '宽辅泓涛专享1号',
    #     '宽辅专享1号',
    # ]:
        # results_dict[account_name] = {}
        for d in Calendar.last_n_trading_days(date, n=1):
            # print(d)
            try:
                trade_date = d.strftime('%Y%m%d')
                print(f'计算{account_name} {trade_date}')
                # results_dict[account_name]['date'] = trade_date
                calcu_func = guzhi_files_dict[account_name]['calcu_func']
                long_pnl, short_pnl = calcu_func(account_name, trade_date)
                # print(long_pnl, short_pnl)
                tmp_df = pd.DataFrame({
                    'account_name': account_name, 'date': trade_date,
                    'long_pnl': long_pnl, 'short_pnl': short_pnl
                    }, index=[0])
                
                # results_dict[account_name]['long_pnl'] = long_pnl
                # results_dict[account_name]['short_pnl'] = short_pnl
                df.append(tmp_df)
            except Exception as e:
                print(e)
                continue
    df = pd.concat(df, axis=0, ignore_index=True)
    print(df)
    write_file(df, file_type='xls', dest_type='dav', dest_path='others/account_guzhi_pnl.xls', index=False)
    return df


def get_email_account_pnl(account_name, trade_date):
    calcu_func = guzhi_files_dict[account_name]['calcu_func']
    long_pnl, short_pnl = calcu_func(account_name, trade_date)
    df = pd.DataFrame({
        'account_name': account_name, 
        'date': trade_date,
        'long_pnl': long_pnl, 
        'short_pnl': short_pnl
    }, index=[0])
    return df








# dates = Calendar.next_n_trading_days('********', n=82)

# df = []
# account_name = '宽辅泓涛专享1号_中信多空'
# for d in dates:
#     print(d)
#     if d.strftime('%Y%m%d') >= '********':
#         break
#     try:
#         trade_date = d.strftime('%Y%m%d')
#         print(f'计算{account_name} {trade_date}')
#         calcu_func = guzhi_files_dict[account_name]['calcu_func']
#         long_pnl, short_pnl = calcu_func(account_name, trade_date)
#         # print(long_pnl, short_pnl)
#         tmp_df = pd.DataFrame({
#             'account_name': account_name, 'date': trade_date,
#             'long_pnl': long_pnl, 'short_pnl': short_pnl
#             }, index=[0])
#         print(tmp_df)
#         df.append(tmp_df)
#     except Exception as e:
#         print(e)
#         continue

# df = pd.concat(df, axis=0, ignore_index=True)
# print(df)
# write_file(df, file_type='xlsx', dest_type='dav', dest_path='ht1_ls_pnl.xlsx', index=False)

# collect_account_pnl('********')
# calcu_account_guzhi_pnl_guojun('远航安心中性6号_国君', '********')
# calcu_account_guzhi_pnl_zheshang('宽辅专享1号_浙商', '********')
# calcu_account_guzhi_pnl_zhongxin_otc('宽辅泓涛专享1号_中信多空', '********')
