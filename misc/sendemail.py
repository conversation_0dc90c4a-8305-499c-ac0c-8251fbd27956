import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from loguru import logger
import os
import traceback


def send_mail(receiver:str, receiver_cc:str, subject, content, attachments:list,
              content_type = "plain",
              sender_email = "<EMAIL>",
              password = "V4VwSVVGCK8xanGT"
              ):
    smtp_server = "smtp.feishu.cn"
    # port = 587  # For starttls
    # sender_email = "<EMAIL>"
    # password = "V4VwSVVGCK8xanGT"
    # receiver_email = "<EMAIL>"

    # multipart as main mail object
    message = MIMEMultipart("alternative")
    message["From"] = sender_email
    message["To"] = receiver
    message["CC"] = receiver_cc
    
    # subject
    # subject = subj + " : " + InfoWarning
    # body = "Date: {:%Y-%m-%d}".format(datetime.now())
    # body = body + "\nstock_daily macd filter update"
    message["Subject"] = subject
    
    # body 
    # body = "Result: \n" + content
    # body = body + "\nLogfile:\n"j
    # with open(logfile, "r") as attachment:
    #     body = body + attachment.read()
    message.attach(MIMEText(content, content_type))
    
    # attachements
    for attachment in attachments:
        part_attach = MIMEApplication(open(attachment, 'rb').read())  # 打开附件
        part_attach.add_header('Content-Disposition', 'attachment', filename=os.path.basename(attachment))  # 为附件命名
        message.attach(part_attach)
    # attch files
    
    # context = ssl.create_default_context()
    # Try to log in to server and send email
    try:
        server = smtplib.SMTP_SSL(smtp_server)
        # server.ehlo()  # Can be omitted
        # server.starttls(context=context)  # Secure the connection
        # server.ehlo()  # Can be omitted
        server.login(sender_email, password)
        server.sendmail(
            sender_email, receiver.split(';') + receiver_cc.split(';'), message.as_string()
        )
        logger.info('Email sent success. \nsub: {} \nreceiver: {}'.format(subject, receiver.split(';') + receiver_cc.split(';')))
    except Exception as e:
        # Print any error messages to stdout
        # print(e)
        logger.error(e)
        logger.error(traceback.format_exc())
    finally:
        server.quit()