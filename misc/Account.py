import pandas as pd
import numpy as np
import os, sys
# import re
from sqlalchemy import create_engine
from urllib.parse import quote_plus as urlquote
from typing import List
from loguru import logger
import traceback
import datetime

from misc.dma_account_pnl import get_email_account_pnl
from misc.Readstockfile import read_remote_file, write_file
from misc.ssh_conn import sftp_clent_dav, sftp_clent_ninner
from data_utils.trading_calendar import Calendar
from accounts_config import kf_accounts_basic

# from accounts_config.kf_accounts_basic import kf_accounts_dict, dma_accounts, duotou_accounts
from accounts_config.accounts_fj import products_fj
from misc.feishu import msgBot
# import load_src_into_redis
# load_account_info_data
from misc import tools
from misc.utils import cffex_multiplier
# from misc.redis_func import df_read_redis, conn_redis


# class ProductAccount:
#     # 假设之前的定义已经存在...

#     def adjust_bookvalue(self, adjustment):
#         """根据提供的调整值增减bookvalue"""
#         for account in self.stock_accounts:
#             if 'adjustment' in adjustment:
#                 account.bookvalue_allocation += adjustment['adjustment']
#             elif 'new_value' in adjustment:
#                 account.bookvalue_allocation = adjustment['new_value']

# def read_account_data_from_excel(file_path):
#     """从Excel文件读取账户数据"""
#     df = pd.read_excel(file_path, sheet_name='Accounts')
#     # 根据Excel的具体列名创建账户实例...
#     return accounts

# def read_adjustments_from_excel(file_path):
#     """从Excel文件读取bookvalue调整数据"""
#     df = pd.read_excel(file_path, sheet_name='Adjustments')
#     # 解析调整值...
#     return adjustments

# 示例使用方式（注释掉以避免执行）
# accounts = read_account_data_from_excel('path_to_accounts.xlsx')
# adjustments = read_adjustments_from_excel('path_to_adjustments.xlsx')
# product_acc.adjust_bookvalue(adjustments)


# rs = conn_redis()

# Redefining classes and methods after code execution state reset
class StockAccount:
    def __init__(self, product_name, account_name, trade_date, 
                 pre_close=None,
                 close=None,
                 short_num=None,
                 futures_price=None,
                 bookvalue=None,
                 stock_commission_rate=0.00012,
                 stock_tax_rate=0.0005,
                 account_type='stockaccount',
                 owner='kf',
                #  long_value, available_fund, net_asset, profit_loss):
        ):
        self.product_name = product_name
        self.account_name = account_name
        self.trade_date = trade_date
        self.pre_date = Calendar.last_trading_day(trade_date).strftime('%Y%m%d')
        # self.long_value = long_value
        # self.stock_value_long = 0
        # self.available_fund = 0
        # self.initial_available_fund = 0
        # self.profit_loss = 0
        # self.futures_positions = []
        # self.futures_pre_close = None
        # self.total_transfer_in = 0  # 记录转入金额总和
        # self.bookvalue = 0
        self.account_type = account_type
        self.account_owner = owner

        self._Product = None
        self.short_num = short_num
        self._futures_price = futures_price
        self._bookvalue = bookvalue

        self._hold = None
        self._morning_hold = None
        self._deal = None
        self._close = close
        self._net_asset = None
        self._stock_value = None
        self._available_fund = None
        self._estimate_pnl = None

        self._pre_hold = None
        self._pre_deal = None
        self._pre_close = pre_close
        self._pre_net_asset = None
        self._pre_stock_value = None
        self._pre_available_fund = None
        self._stock_value_pre_close = None

        self._inout_money = None
        self._account_pnl = None
        self._email_account_pnl_df = None
        self.account_pnl_df = None

        self.stock_commission_rate = stock_commission_rate
        self.stock_tax_rate = stock_tax_rate

    # def get_pre_stock_hold(self):
    #     df = tools.get_stock_hold_df(self.account_name, self.pre_date)
    #     self.hold = df

    # def get_pre_stock_value_long(self):
    #     self.pre_stock_value_long =  tools.get_account_stock_value(self.account_name, self.pre_date, kf_accounts_dict=kf_accounts_dict)

    # def get_pre_account_available_fund(self):
    #     self.pre_available_fund = tools.get_available_fund(self.account_name, self.pre_date, kf_accounts_dict=kf_accounts_dict)

    # def load_account_config(self, account_config={}):
    #     self.account_type = account_config.get('account_type', 'neutral')
    #     self.stock_props = account_config.get('stock_props', 0.86) if self.account_type == 'neutral' else 0

    # def load_data(self):
    #     self.get_pre_stock_hold()
    #     self.get_pre_stock_value_long()
    #     self.get_pre_account_net_asset()
    #     self.get_pre_account_available_fund()

    # def output_result(self):
    #     print(self.account_name, self.trade_date, self.stock_value_long, self.available_fund, self.net_asset, self.profit_loss)

    # =====================================
    @property
    def Product(self):
        return self._Product
    @Product.setter
    def Product(self, Product):
        self._Product = Product

    def _get_hold(self, date):
        df = tools.get_latest_hold(self.account_name, date=date, config={})
        return df

    def get_latest_hold_by_morning_hold_deal(self, hold, deal):
        hold = hold.copy()
        deal = deal.copy()

        hold['direction'] = np.where(hold['volume']>0, 1, -1)
        hold['volume'] = hold['volume'].abs()
        hold['available_volume'] = hold['available_volume'].abs()
        hold = hold[['ticker', 'direction', 'volume', 'available_volume']].set_index(['ticker', 'direction'])

        deal['direction'] = np.where(deal['BS_flag'].isin(["买入", "卖出"]), 1, -1)
        deal['OC'] = np.where(deal['BS_flag'].isin(['买入', '卖空']), 1, -1)
        deal['fill_volume'] = deal['fill_volume'].abs()
        deal = deal[deal['fill_volume'] != 0]
        deal['fill_volume'] = deal['fill_volume'] * deal['OC']
        deal['only_close_volume'] = np.where(deal['fill_volume']>0, 0, deal['fill_volume'])
        deal = deal[['ticker', 'direction', 'fill_volume', 'only_close_volume']].groupby(['ticker', 'direction']).sum()

        # print(f'hold: {hold[:50]}')
        # print(f'deal: {deal[:50]}')

        hold = pd.concat([hold, deal], axis=1)
        hold.fillna(0, inplace=True)
        # print(hold)

        hold['volume'] = hold['volume'].add(hold['fill_volume'], fill_value=0)    
        hold['available_volume'] = hold['available_volume'].add(hold['only_close_volume'], fill_value=0)
        
        hold.reset_index(inplace=True)
        hold['volume'] = hold['volume'] * hold['direction']
        hold['available_volume'] = hold['available_volume'] * hold['direction']
        hold = hold[['ticker', 'volume', 'available_volume']].groupby(['ticker']).sum().reset_index()
        hold['volume'] = hold['volume'].astype(int)
        hold['available_volume'] = hold['available_volume'].astype(int)
        hold = hold[hold['volume'] != 0]
        
        self._hold = hold
        
        return hold


    def _load_close(self, date):
        # print(date)
        close = tools.get_stock_adj_close(date=date)
        # print(pre_close)
        return close

    def _load_account_net_asset(self, date):
        if self.account_name in kf_accounts_basic.dma_accounts:
            net_asset = 0
        else:
            net_asset = tools.get_account_net_asset(self.account_name, date=date)
        return net_asset

    def _get_account_available_fund(self, date):
        available_fund = tools.get_available_fund(self.account_name, date, kf_accounts_dict=kf_accounts_basic.kf_accounts_dict)
        return available_fund

    def _load_inout_money(self, date):
        inout = tools.get_account_inout_money(self.account_name, account_type='stock', date=date)
        return inout

    def _calcu_volume_value(self, hold, price):
        # print(f'pre_close: {self.pre_close.head()}')
        value_df = pd.merge(hold.set_index('ticker'), price.set_index('ticker'),
                                            left_index=True, right_index=True,
                                            how='left')
        value = value_df['volume'].mul(value_df['close']).sum()
        return value

    def _get_stock_deal(self, date):
        df = tools.get_stock_deal(self.account_name, date=date)
        df = df.rename(columns={
            "成交日期"    :  "date"        ,
            "成交时间"    :  "time"        ,
            "合同编号"    :  "order_ref"   ,
            "成交编号"    :  "deal_ref"    ,
            "代码"       :  "ticker"      ,
            "买卖标记"    :  "BS_flag"     ,
            "成交均价"    :  "fill_price"  ,
            "成交数量"    :  "fill_volume" ,
            "算法"       :  "algorithm"   ,
        })
        return df

    # def _cal_bookvalue_based_short(self):
    #     # bookvalue = product.bv_coef * product.short_value + product.bv_const
    #     pass

    def _get_short_num_value(self):
        config_file = 'others/account_config.xlsx'
        config = read_remote_file(config_file, src_type='dav', sheet_name='account_index_num')
        config['bv_const'] = config['bv_const'].replace(np.nan, 0)
        # config[['account_name', 'bench', 'index_num', 'bv_coef']]
        config = config.merge(self.futures_price, how='left', left_on='bench', right_on='ticker')
        config['multiplier'] = config['ticker'].map(cffex_multiplier)
        config['short_value'] = config['index_num'].mul(config['close']).mul(config['multiplier']).mul(config['bv_coef'])
        value = config[config['account_name']==self.account_name]['short_value'].sum()
        return value

    @property
    def pre_hold(self):
        if self._pre_hold is None:
            # print(f"Loading hold at {self.pre_date}...")
            self._pre_hold = self._get_hold(date=self.pre_date)
        return self._pre_hold
    @property
    def hold(self):
        if self._hold is None:
            # print(f"Loading hold at {self.trade_date}...")
            self._hold = self._get_hold(date=self.trade_date)
        return self._hold

    @property
    def morning_hold(self):
        if self._morning_hold is None:
            # print(f"Loading morning hold at {self.trade_date}...")
            self._morning_hold = tools.get_stock_morning_hold(self.account_name, self.trade_date)
        return self._morning_hold

    @property
    def target(self):
        return tools.get_target_by_date(self.account_name, self.trade_date)

    @property
    def pre_close(self):
        if self._pre_close is None:
            # print(self.pre_date)
            self._pre_close = self._load_close(date=self.pre_date)
        return self._pre_close
    @property
    def close(self):
        if self._close is None:
            self._close = self._load_close(date=self.trade_date)
        return self._close

    @property
    def deal(self):
        if self._deal is None:
            self._deal = self._get_stock_deal(date=self.trade_date)
        return self._deal

    @property
    def pre_deal(self):
        if self._pre_deal is None:
            self._pre_deal = self._get_stock_deal(date=self.pre_date)
        return self._pre_deal

    @property
    def stock_value_pre_close(self):
        # print(f'pre_close: {self.pre_close.head()}')
        if self._stock_value_pre_close is None:
            self._stock_value_pre_close = self._calcu_volume_value(self.hold, self.pre_close)
        return self._stock_value_pre_close

    @property
    def stock_value(self):
        if self._stock_value is None:
            self._stock_value = self._calcu_volume_value(self.hold, self.close)
        return self._stock_value
    @property
    def pre_stock_value(self):
        if self._pre_stock_value is None:
            self._pre_stock_value = self._calcu_volume_value(self.pre_hold, self.pre_close)
            # print(self.pre_date)
            # print(self.pre_close.head())
            # print(self.pre_hold.head())
        return self._pre_stock_value

    @property
    def pre_net_asset(self):
        if self._pre_net_asset is None:
            self._pre_net_asset = self._load_account_net_asset(self.pre_date)
        return self._pre_net_asset
    @property
    def net_asset(self):
        if self._net_asset is None:
            self._net_asset = self._load_account_net_asset(self.trade_date)
        return self._net_asset

    @property
    def available_fund(self):
        if self._available_fund is None:
            self._available_fund = self._get_account_available_fund(self.trade_date)
        return self._available_fund
    @property
    def pre_available_fund(self):
        if self._pre_available_fund is None:
            self._pre_available_fund = self._get_account_available_fund(self.pre_date)
        return self._pre_available_fund

    @property
    def futures_price(self):
        if self._futures_price is None:
            self._futures_price = tools.get_futures_price_include_index(self.trade_date)
        return self._futures_price

    @property
    def estimate_pnl(self):
        if self._estimate_pnl is None:
            self._estimate_pnl = tools.get_account_estimate_pnl(self.account_name, self.trade_date)
        return self._estimate_pnl

    # @property
    # def avilable_fund(self):
    #     if self._available_fund is None:
    #         self._available_fund = self._load_account_available_fund(self.trade_date)
    #     return self._available_fund

    @property
    def minimal_deal(self):
        return tools.get_stock_minimal_deal(self.account_name, self.trade_date)

    @property
    def inout_money(self):
        if self._inout_money is None:
            self._inout_money = self._load_inout_money(self.trade_date)
        return self._inout_money

    @property
    def account_pnl(self):
        if self._account_pnl is None:
            if self.account_name in kf_accounts_basic.read_email_pnl_accounts:
                try:
                    self._email_account_pnl_df = get_email_account_pnl(self.account_name, self.trade_date)
                    self._account_pnl = self._email_account_pnl_df.iloc[0].get('long_pnl')
                except FileNotFoundError:
                    self._email_account_pnl_df = None
            else:
                self._account_pnl = self.calcu_account_pnl()
        return self._account_pnl

    def calcu_stock_weight_ratio(self):
        stock_value_pre_close_df = pd.merge(self.hold.set_index('ticker'), self.pre_close.set_index('ticker'),
                                            left_index=True, right_index=True,
                                            how='left')
        stock_value_pre_close_df['value_pre_close'] = stock_value_pre_close_df['volume'].mul(stock_value_pre_close_df['close'])
        stock_value_pre_close_df['weight'] = stock_value_pre_close_df['value_pre_close'] / stock_value_pre_close_df['value_pre_close'].sum()
        return stock_value_pre_close_df[['weight']].reset_index()

    def calcu_account_ret(self):
        return (self.net_asset - self.pre_net_asset - self.inout_money) / (self.pre_net_asset)

    def calcu_account_pnl(self):
        # try:
        #     account_file = f'accountinfo_{self.trade_date}.xls'
        #     account_file_path = os.path.join(self.account_name, 'account', account_file)
        #     account_df = read_remote_file(account_file_path, src_type='dav')
        #     pnlclient = account_df['pnl(client)'].iloc[0]

        #     # 如果pnl(client)数据为0, 则从帐户资产相减计算收益
        #     if pnlclient == 0:
        #         pnlclient = (self.net_asset - self.pre_net_asset - self.inout_money)

        # except KeyError:
        #     pnlclient = (self.net_asset - self.pre_net_asset - self.inout_money)

        pnlclient = (self.net_asset - self.pre_net_asset - self.inout_money)
        return pnlclient

    def calcu_estimate_pnl(self):
        hold = self.hold
        # pre_hold = self.pre_hold
        morning_hold = self.morning_hold
        close = self.close
        pre_close = self.pre_close
        deal = self.deal        
        deal['fill_volume'] = deal['BS_flag'].apply(lambda x: 1 if x in ['买入', '平空'] else -1) * deal['fill_volume']
        deal = deal[deal['fill_volume'] != 0]

        deal_buy = deal[deal['fill_volume'] > 0].copy()[['ticker', 'fill_volume', 'fill_price']].rename(columns={'fill_volume': 'buy_volume', 'fill_price': 'buy_price'})
        deal_sell = deal[deal['fill_volume'] < 0].copy()[['ticker', 'fill_volume', 'fill_price']].rename(columns={'fill_volume': 'sell_volume', 'fill_price': 'sell_price'})

        # 对deal 进行汇总, price为加权平均
        deal_buy = deal_buy.groupby(['ticker']).agg(
            {'buy_volume': 'sum', 'buy_price': lambda x: np.average(x, weights=deal_buy.loc[x.index, "buy_volume"])}
        ).reset_index()
        deal_sell = deal_sell.groupby(['ticker']).agg(
            {'sell_volume': 'sum', 'sell_price': lambda x: np.average(x, weights=deal_sell.loc[x.index, "sell_volume"])}
        ).reset_index()

        data = pd.merge(hold, morning_hold, on='ticker', how='outer', suffixes=('', '_pre'))
        data = pd.merge(data, close, on='ticker', how='left' )
        data = pd.merge(data, pre_close, on='ticker', how='left', suffixes=('', '_pre'))
        data = pd.merge(data, deal_buy, on='ticker', how='outer')
        data = pd.merge(data, deal_sell, on='ticker', how='outer')

        # write_file(data, file_type='xls', dest_type='dav', dest_path=os.path.join(self.account_name, 'account', 'estimate_pnl_{}.xls'.format(self.trade_date)), index=False)
        data.fillna(0, inplace=True)
        estimate_pnl = (
            data["volume"].mul(data["close"]).sum()
            - data["volume_pre"].mul(data["close_pre"]).sum()
            - data["buy_volume"].mul(data["buy_price"]).sum()
            - data["sell_volume"].mul(data["sell_price"]).sum()
        )
        return estimate_pnl

    def get_account_pnl_df(self):
        # 计算账户收益
        _ = self.account_pnl

        if self.account_name in kf_accounts_basic.read_email_pnl_accounts:
            if self._email_account_pnl_df is not None:
                self.account_pnl_df = self._email_account_pnl_df
        else:
            self.account_pnl_df = pd.DataFrame({
                'account_name': [self.account_name],
                'date': [self.trade_date],
                'long_pnl': [self.account_pnl],
            })
        return self.account_pnl_df

    def cal_trading_monitor(self):

        pre_hold = self.morning_hold.copy().rename(columns={'volume': 'hold_volume'})

        target = tools.get_target_by_date(self.account_name, self.trade_date)
        # target = df_read_redis(rs, key = f'{self.trade_date}.stock.{self.account_name}.position')
        
        if target is None:
            target = pd.DataFrame(columns=['ticker', 'target_volume' ])
        else:
            target = target.rename(columns={'volume': 'target_volume'})

        
        # target = self.target.copy().rename(columns={'volume': 'target_volume'})
        deal = self.minimal_deal.copy()
        # test = deal.copy()
        # if self.account_name == '宽辅思贤专享中性2号_广发':
        #     test['amt'] = test['fill_price'] * test['fill_volume']
        #     print(test[['amt', 'BS_flag']].groupby('BS_flag').sum())

        combined_data = pd.concat([pre_hold.set_index('ticker'),
                                    target.set_index('ticker')], 
                                    axis=1, join='outer')
        combined_data.fillna(0, inplace=True)
        combined_data['order_volume'] = combined_data['target_volume'] - combined_data['hold_volume']

        # combined_data = combined_data[combined_data['target_volume'] != 0].reset_index()[['ticker', 'target_volume']]
        # combined_data['buy_volume'] = combined_data['volume'][combined_data['volume'] > 0]
        # combined_data['sell_volume'] = combined_data['volume'][combined_data['volume'] <= 0]
        # combined_data = combined_data.fillna(0)
        # for col in ['volume', 'buy_volume', 'sell_volume']:
        #     combined_data[col] = combined_data[col].astype(int)

        # combined_data = combined_data[['ticker', 'target_volume']]
        # combined_data.sort_values('ticker', inplace=True, ignore_index=True)

        # deal['direction'] = deal['BS_flag'].apply(lambda x: 1 if x=='买入' else -1)
        deal['direction'] = np.where(deal['BS_flag'].isin(["买入", "平空"]), 1, -1)
        # print(deal)
        deal['buy_volume'] = np.where(deal['direction']==1, deal['fill_volume'], 0)
        deal['sell_volume'] = np.where(deal['direction']==-1, -deal['fill_volume'], 0)

        # deal['buy_volume'] = deal['direction'][deal['direction']==1] * deal['fill_volume']
        # deal['sell_volume'] = deal['direction'][deal['direction']==-1] * deal['fill_volume']

        deal = deal[['ticker', 'buy_volume', 'sell_volume']].groupby(['ticker']).sum().reset_index()

        deal['trade_volume'] = deal['buy_volume'] + deal['sell_volume']

        deal = deal.merge(combined_data, how='outer', on='ticker')
        deal.fillna(0, inplace=True)

        # deal['is_target'] = deal['order_volume'] != 0
        is_target = deal['order_volume'] != 0

        deal['target_buy_volume'] = np.where(deal['order_volume'] > 0, deal['order_volume'], 0)
        deal['target_sell_volume'] = np.where(deal['order_volume'] < 0, deal['order_volume'], 0)

        deal = deal.merge(self.pre_close, how='left', on='ticker')

        deal['target_buy_value'] = deal['target_buy_volume'] * deal['close']
        deal['target_sell_value'] = deal['target_sell_volume'] * deal['close']
        deal['order_net_value'] = deal['order_volume'] * deal['close']
        deal['BS_net_value'] = deal['trade_volume'] * deal['close']
        deal['filled_buy_value'] = deal['buy_volume'] * deal['close']
        deal['filled_sell_value'] = deal['sell_volume'] * deal['close']
        deal['target_value'] = deal['target_volume'] * deal['close']
        deal['hold_value'] = deal['hold_volume'] * deal['close']

        # deal['sell_volume'] = deal['volume'][deal['volume'] <= 0]
        # print(deal)

        # print(deal)
        deal['net_buy_volume'] = np.where(deal['trade_volume'] > 0, deal['trade_volume'], 0)
        deal['net_sell_volume'] = np.where(deal['trade_volume'] < 0, deal['trade_volume'], 0)

        deal['aviation_buy_volume'] = np.where(deal['trade_volume'] > deal['target_buy_volume'], deal['trade_volume'] - deal['target_buy_volume'], 0)
        deal['aviation_sell_volume'] = np.where(deal['trade_volume'] < deal['target_sell_volume'], deal['trade_volume'] - deal['target_sell_volume'], 0)

        rat_target_filled_buy = deal[is_target]['net_buy_volume'].sum() / deal['target_buy_volume'].sum() if deal['target_buy_volume'].sum() !=0 else 0
        rat_target_filled_sell = deal[is_target]['net_sell_volume'].sum() / deal['target_sell_volume'].sum() if deal['target_sell_volume'].sum() !=0 else 0

        rat_outtarget_buy = deal['aviation_buy_volume'].sum() / max(deal['target_buy_volume'].abs().sum(), deal['target_sell_volume'].abs().sum()) if (deal['target_buy_volume'].sum() !=0 or deal['target_sell_volume'].sum() !=0) else 0
        rat_outtarget_sell = deal['aviation_sell_volume'].sum() / max(deal['target_buy_volume'].abs().sum(), deal['target_sell_volume'].abs().sum()) if (deal['target_buy_volume'].sum() !=0 or deal['target_sell_volume'].sum() !=0) else 0

        # rat_outtarget_buy = deal[~is_target]['buy_volume'].sum() / max(deal['target_buy_volume'].abs().sum(), deal['target_sell_volume'].abs().sum())
        # rat_outtarget_sell = deal[~is_target]['sell_volume'].sum() / max(deal['target_buy_volume'].abs().sum(), deal['target_sell_volume'].abs().sum())

        # print(rat_target_filled_buy, rat_target_filled_sell, rat_outtarget_buy, rat_outtarget_sell)
        # print(rat_sell)

        trade_targets = {}
        trade_summary = {}

        trade_targets.update({
            self.account_name : {
                'product_name' : self.product_name,
                'inplan_buyvolume' : round(  rat_target_filled_buy, 4),
                'inplan_sellvolume' : round(  rat_target_filled_sell, 4),
                'outplan_buyvolume' : round(  rat_outtarget_buy, 4),
                'outplan_sellvolume' : round(  rat_outtarget_sell, 4),
            }
        })

        trade_summary.update({
            self.account_name : {
                'product_name' : self.product_name,
                'pre_hold_value' : deal['hold_value'].sum(),
                'target_value' : deal['target_value'].sum(),
                'target_buy_value' : deal['target_buy_value'].sum(),
                'target_sell_value' : deal['target_sell_value'].sum(),
                'order_net_value' : deal['order_net_value'].sum(),
                'BS_net_value' : deal['BS_net_value'].sum(),
                'realized_dff' : deal['BS_net_value'].sum() - deal['order_net_value'].sum(),
                'buy_value' : deal['filled_buy_value'].sum(),
                'sell_value' : deal['filled_sell_value'].sum(),
                'target_buy_volume' : deal['target_buy_volume'].sum(),
                'target_sell_volume' : deal['target_sell_volume'].sum(),
                'buy_volume_filled' : round(  rat_target_filled_buy, 2),
                'sell_volume_filled' : round(  rat_target_filled_sell, 2),
                # 'trader' : trade_config[account_name]['交易员'],
                # 'acc_type' : trade_config[account_name]['类型'],
            }
        })
        # if self.account_name == '宽辅思贤专享中性2号_广发':
        #     print(trade_summary[self.account_name])
        #     print('net buy', deal['net_buy_volume'].sum())
        #     print('net sell', deal['net_sell_volume'].sum())

        trade_targets = pd.DataFrame.from_dict(trade_targets, orient='index')
        trade_summary = pd.DataFrame.from_dict(trade_summary, orient='index')
        # print(trade_targets)
        # print(trade_summary)

        return trade_targets, trade_summary

    def compute_comprehensive_account(self):
        hold = self.hold
        morning_hold = self.morning_hold
        close = self.close
        pre_close = self.pre_close
        deal = self.deal

        hold['direction'] = hold['volume'].map(lambda x: 1 if x>0 else -1)
        hold['volume'] = hold['volume'].abs()
        morning_hold['direction'] = morning_hold['volume'].map(lambda x: 1 if x>0 else -1)
        morning_hold['volume'] = morning_hold['volume'].abs()

        deal['direction'] = deal['BS_flag'].map(lambda x: 1 if x in ['买入', '卖出'] else -1) 
        deal['OC'] = deal['BS_flag'].map(lambda x: 1 if x in ['买入', '卖空'] else -1)
        deal['fill_volume'] = deal['fill_volume'].abs()

        deal = deal[deal['fill_volume'] != 0]

        deal['fill_volume'] = deal['fill_volume'] * deal['OC']
        print(deal.head(1))

        # check morning_hold + deal == hold
        tmp_deal = deal[['ticker', 'direction', 'fill_volume']].groupby(['ticker', 'direction']).sum()
        computed_hold = morning_hold.set_index(['ticker', 'direction'])['volume'].add(tmp_deal['fill_volume'], fill_value=0)
        diff_deal = hold.set_index(['ticker', 'direction'])['volume'].sub(computed_hold, fill_value=0)
        diff_deal = diff_deal[diff_deal != 0]
        if not (diff_deal == 0).all():
            diff_deal = diff_deal.to_frame('fill_volume').reset_index()
            diff_deal = pd.merge(diff_deal, close, on=['ticker'], how='left')
            diff_deal['OC'] = np.where(diff_deal['fill_volume'] > 0, 1, -1)
        
            logger.error(f'{self.account_name} morning_hold + deal != hold,  diff: \n{diff_deal}')
            confirm_flag = input("合并diff_deal 到 deal 中? y/n")
            if confirm_flag.lower() == 'y':
                diff_deal['fill_price'] = diff_deal['close']
                deal = pd.concat([deal, diff_deal], axis=0, ignore_index=True)
            
            
        deal_open = deal[deal['OC']==1]
        deal_close = deal[deal['OC']==-1]

        deal_open = deal_open[['ticker', 'direction', 'fill_volume', 'fill_price']].groupby(['ticker', 'direction']).agg(
            {'fill_volume': 'sum', 'fill_price': lambda x: np.average(x, weights=deal_open.loc[x.index, "fill_volume"])}
        ).reset_index().rename(columns={'fill_volume': 'open_volume', 'fill_price': 'open_price'})

        deal_close = deal_close[['ticker', 'direction', 'fill_volume', 'fill_price']].groupby(['ticker', 'direction']).agg(
            {'fill_volume': 'sum', 'fill_price': lambda x: np.average(x, weights=deal_close.loc[x.index, "fill_volume"])}
        ).reset_index().rename(columns={'fill_volume': 'close_volume', 'fill_price': 'close_price'})

        # deal = deal[['ticker', 'direction', 'fill_volume', 'fill_price']].groupby(['ticker', 'direction']).agg(
        #     {'fill_volume': 'sum', 'fill_price': lambda x: np.average(x, weights=deal.loc[x.index, "fill_volume"])}
        # ).reset_index()

        data = pd.merge(hold, morning_hold, on=['ticker', 'direction'], how='outer', suffixes=('', '_pre'))
        data = pd.merge(data, close, on=['ticker'], how='left' )
        data = pd.merge(data, pre_close, on=['ticker'], how='left', suffixes=('', '_pre'))
        data = pd.merge(data, deal_open, on=['ticker', 'direction'], how='outer')
        data = pd.merge(data, deal_close, on=['ticker', 'direction'], how='outer')

        data = data.fillna(0)
        # data columns
        # ticker  direction  volume  volume_pre  fill_volume  fill_price  close  close_pre

        info = {}

        long = data[data['direction'] == 1]
        short = data[data['direction'] == -1]
        info['long_value'] = long['volume'].mul(long['close']).sum()
        info['short_value'] = short['volume'].mul(short['close']).sum()
        info['pre_long_value'] = long['volume_pre'].mul(long['close_pre']).sum()
        info['pre_short_value'] = short['volume_pre'].mul(short['close_pre']).sum()

        info['long_pnl'] = (
            long['volume'].mul(long['close']).sum()
            - long['volume_pre'].mul(long['close_pre']).sum()
            - long['open_volume'].mul(long['open_price']).sum()
            - long['close_volume'].mul(long['close_price']).sum()
        )
        info['short_pnl'] = - (
            short['volume'].mul(short['close']).sum()
            - short['volume_pre'].mul(short['close_pre']).sum()
            - short['open_volume'].mul(short['open_price']).sum()
            - short['close_volume'].mul(short['close_price']).sum()
        )

        info["stock_commission"] = (
            data["open_volume"].abs().mul(data["open_price"]).sum()
            + data["close_volume"].abs().mul(data["close_price"]).sum()
        ) * (self.stock_commission_rate)

        info["stock_tax"] = data["close_volume"].abs().mul(
            data["close_price"]
        ).sum() * (self.stock_tax_rate)

        info["account_pnl"] = (
            info["long_pnl"]
            + info["short_pnl"]
            - info["stock_commission"]
            - info["stock_tax"]
        )

        # # detail record
        # data['pnl'] = (data['volume'].mul(data['close'])
        #     - data['volume_pre'].mul(data['close_pre'])
        #     - data['open_volume'].mul(data['open_price'])
        #     - data['close_volume'].mul(data['close_price'])
        # )
        # data['pnl'] = np.where(data['direction']==1, data['pnl'], -data['pnl'])
        # data['stock_commission'] = ((data['open_volume']*data['open_price']).abs() + ((data['close_volume']*data['close_price']).abs())).mul(self.stock_commission_rate)
        # data['stock_tax'] = (data['close_volume']*data['close_price']).abs().mul(self.stock_tax_rate)
        # data['account_pnl'] = data['pnl'] - data['stock_commission'] - data['stock_tax']
        # write_file(data, file_type='xls', dest_type='dav', dest_path=os.path.join(self.account_name, 'account', 'detail_pnl_{}.xls'.format(self.trade_date)), index=False)


        accountinfo_path = os.path.join(self.account_name, 'account', f'accountinfo_{self.trade_date}.xls')
        accountinfo = read_remote_file(accountinfo_path, src_type='dav')
        # merge
        info = pd.concat([accountinfo, pd.DataFrame(info, index=[0]) ], axis=1)

        if self.account_type == 'stockaccount':
            accout_file_path = os.path.join(self.account_name, 'account', f'stockaccount_{self.trade_date}.xls')
            write_file(info, file_type='xls', dest_type='dav', dest_path=accout_file_path, index=False)
        elif self.account_type == 'marginaccount':
            accout_file_path = os.path.join(self.account_name, 'account', f'marginaccount_{self.trade_date}.xls')
            write_file(info, file_type='xls', dest_type='dav', dest_path=accout_file_path, index=False)

        return info

class FuturesAccount:
    def __init__(self, product_name, account_name, trade_date,
                 owner='kf',
                 pre_price=None,
                 price=None,
                 contracts_info=None,
                #  , margin, net_asset, profit_loss, available_fund:
        ):
        self.product_name = product_name
        self.account_name = account_name
        self.trade_date = trade_date
        self.pre_date = Calendar.last_trading_day(trade_date).strftime('%Y%m%d')
        self.account_owner = owner
        # self.hold = []
        # self.long_value = 0
        # self.short_value = 0
        # # self.margin = margin
        # self.net_asset = 0
        # self.profit_loss = 0
        # self.available_fund = 0
        # self.initial_available_fund = 0
        # self.total_transfer_out = 0  # 记录转出金额总和

        self._Product = None
        self._contracts_info = contracts_info

        self._pre_hold = None
        self._minimal_hold = None

        self._pre_price = pre_price
        self._pre_net_asset = None

        self._hold = None
        self._minimal_pre_hold = None

        self._price = price
        self._net_asset = None

        self.margin_ratio = None
    # 更新期货持仓, 同时更新多头市值, 空头市值
    # def add_futures_position(self, futures_position):
    #     self.hold.append(futures_position)
    #     if futures_position.longshort == '多头':
    #         self.long_value += futures_position.quantity
    #     elif futures_position.longshort == '空头':
    #         self.short_value += futures_position.quantity
    # def _load_pre_close(self):
    #     futures_pre_close = tools.get_futures_price(self.pre_date)[['ticker', 'date', 'settle', 'close']]
    #     index_pre_close = tools.get_main_index_close(self.pre_date)
    #     self.futures_pre_close = pd.concat([index_pre_close, futures_pre_close])
    @property
    def Product(self):
        return self._Product
    @Product.setter
    def Product(self, Product):
        self._Product = Product
        
    @property
    def contracts_info(self):
        if self._contracts_info is None:
            self._contracts_info = tools.get_futures_contract_info(self.trade_date)
        return self._contracts_info

    @property
    def price(self):
        if self._price is None:
            self._price = self._load_price(self.trade_date)
        return self._price
    @property
    def pre_price(self):
        if self._pre_price is None:
            self._pre_price = self._load_price(self.pre_date)
        return self._pre_price

    @property
    def hold(self):
        if self._hold is None:
            self._hold = self._get_futures_hold(self.trade_date)[['ticker', 'longshort', 'volume']]
        return self._hold
    @property
    def pre_hold(self):
        if self._pre_hold is None:
            self._pre_hold = self._get_futures_hold(self.pre_date)[['ticker', 'longshort', 'volume']]
        return self._pre_hold

    @property
    def minimal_hold(self):
        if self._minimal_hold is None:
            df = self.hold.copy()
            df['ticker'] = df['ticker'].apply(self._map_ticker_index)
            df['volume'] = df['volume'].mul(df['longshort'].map({'多头': 1, '空头': -1}))
            df = df[['ticker', 'volume']].groupby('ticker').sum().reset_index()
            self._minimal_hold = df
        return self._minimal_hold
        # return df[['ticker', 'volume']]

    @property
    def minimal_pre_hold(self):
        if self._minimal_pre_hold is None:
            df = self.pre_hold.copy()
            df['ticker'] = df['ticker'].apply(self._map_ticker_index)
            df['volume'] = df['volume'].mul(df['longshort'].map({'多头': 1, '空头': -1}))
            df = df[['ticker', 'volume']].groupby('ticker').sum().reset_index()
            self._minimal_pre_hold = df
        return self._minimal_pre_hold
        # return df[['ticker', 'volume']]

    @property
    def net_asset(self):
        if self._net_asset is None:
            self._net_asset = self._get_futures_net_asset(self.trade_date)
        return self._net_asset
    @property
    def pre_net_asset(self):
        if self._pre_net_asset is None:
            self._pre_net_asset = self._get_futures_net_asset(self.pre_date)
        return self._pre_net_asset

    @property
    def long_value(self):
        return self._cal_market_value(self.hold, self.price, '多头')
    @property
    def short_value(self):
        return self._cal_market_value(self.hold, self.price, '空头')
    @property
    def pre_long_value(self):
        return self._cal_market_value(self.pre_hold, self.pre_price, '多头')
    @property
    def pre_short_value(self):
        return self._cal_market_value(self.pre_hold, self.pre_price, '空头')

    @property
    def long_value_by_index_price(self):
        return self._cal_future_value_by_index_price(self.hold, self.price, '多头')
    @property
    def short_value_by_index_price(self):
        return self._cal_future_value_by_index_price(self.hold, self.price, '空头')
    @property
    def pre_long_value_by_index_price(self):
        return self._cal_future_value_by_index_price(self.pre_hold, self.pre_price, '多头')
    @property
    def pre_short_value_by_index_price(self):
        return self._cal_future_value_by_index_price(self.pre_hold, self.pre_price, '空头')    

    @property
    def hold_long_num(self):
        return self._sum_hold_num(self.hold, '多头')
    @property
    def hold_short_num(self):
        return self._sum_hold_num(self.hold, '空头')
    @property
    def pre_hold_long_num(self):
        return self._sum_hold_num(self.pre_hold, '多头')
    @property
    def pre_hold_short_num(self):
        return self._sum_hold_num(self.pre_hold, '空头')

    @property
    def today_deal(self):
        if self.account_owner == 'kf':
            df = tools.get_futures_today_deal(self.account_name, self.trade_date)
        elif self.account_owner in ['jeff', 'chenyc']:
            df = tools.get_futuStra_futures_deal(self.account_name, self.trade_date)
        else:
            raise ValueError('Invalid account owner')
        df = df.rename(columns={'volume': 'fill_volume', 'price': 'fill_price'})
        return df

    @property
    def deal(self):
        return self.today_deal

    # @property
    def update_today_latest_hold(self):
        pre_hold = self.pre_hold
        today_deal = self.today_deal

        # if self.product_name == '宽辅泓涛专享1号':
        #     print(self.account_name)
        #     print(today_deal)

        today_deal = today_deal.rename(columns={'fill_volume': 'volume'})[['ticker', 'longshort', 'volume', 'operation']]

        today_deal['volume'] = today_deal['volume'].abs().mul(today_deal['operation'].map({'开仓': 1, '平仓': -1}))
        # today_deal = today_deal[['ticker', 'longshort', 'volume']]
        today_deal = today_deal.groupby(['ticker', 'longshort']).sum()
        today_hold = pre_hold.set_index(['ticker', 'longshort'])['volume'].add(today_deal['volume'], fill_value=0)
        today_hold = today_hold.to_frame('volume').reset_index()
        self._hold = today_hold
        return today_hold

    def _load_price(self, date):
        return tools.get_futures_price_include_index(date)

    def _calcu_volume_value(self, hold, price):
        # print(f'pre_close: {self.pre_close.head()}')
        value_df = pd.merge(hold.set_index('ticker'), price.set_index('ticker'),
                                            left_index=True, right_index=True,
                                            how='left')
        value = value_df['volume'].mul(value_df['close']).sum()
        return value

    def _cal_market_value(self, hold, price, longshort):
        hold = hold.merge(price, how='left', on='ticker', suffixes=['_bak', ''])
        hold['multiplier'] = hold['ticker'].map(cffex_multiplier)
        hold['market_value'] = hold['volume'].mul(hold['close']).mul(hold['multiplier'])
        hold = hold[hold['longshort']==longshort]
        return hold['market_value'].sum()

    def _cal_future_value_by_index_price(self, hold, price, longshort):
        hold['ticker'] = hold['ticker'].apply(self._map_ticker_index)
        hold = hold.merge(price, how='left', on='ticker', suffixes=['_bak', ''])
        hold['multiplier'] = hold['ticker'].map(cffex_multiplier)
        hold['market_value'] = hold['volume'].mul(hold['close']).mul(hold['multiplier'])
        hold = hold[hold['longshort']==longshort]
        return hold['market_value'].sum() 

    def _sum_hold_num(self, hold, longshort):
        num = hold[hold['longshort'==longshort]]['volume'].sum()
        return num

    def _get_futures_hold(self, date):
        if self.account_owner == 'kf':
            try:
                df = tools.get_futures_hold_df(self.account_name, date)
            except FileNotFoundError:
                df = self.update_today_latest_hold()
        elif self.account_owner in ['jeff', 'chenyc']:
            df = tools.get_latest_futuStra_future_hold(self.account_name, date)
        else:
            raise

        return df

    def _get_futures_net_asset(self, date):
        net_asset = tools.get_futures_net_asset(self.account_name, date)
        return net_asset

    def _map_ticker_index(self, ticker):
        if ticker.startswith('IC') or ticker.startswith('中证500'):
            return 'ZZ500'
        elif ticker.startswith('IM') or ticker.startswith('中证1000'):
            return 'ZZ1000'
        elif ticker.startswith('IF') or ticker.startswith('沪深300'):
            return 'HS300'
        elif ticker.startswith('IH') or ticker.startswith('上证50'):
            return 'SH50'
        else:
            return ticker

    def calcu_margin(self):
        if self.margin_ratio is None:
            margin_ratio = 0.12
        margin = self.hold.merge(self.price, how='left', left_on='ticker', right_on='ticker')
        margin['multiplier'] = margin['ticker'].map(cffex_multiplier)
        print(margin)
        # margin['margin'] = margin['volume'].mul(margin['close']).mul(margin['multiplier']) * margin_ratio
        margin['margin'] = margin['volume'].mul(margin['settle']).mul(margin['multiplier']) * margin_ratio
        margin = margin['margin'].sum()
        return margin
    # def load_data(self):
    #     self.get_pre_futures_hold()
    #     # print(self.pre_hold)
    #     self.load_pre_close()
    #     self.cal_market_value()
    #     self.get_pre_account_net_asset()
    #     self.get_pre_account_available_fund()

    def calcu_estimate_pnl(self):
        hold = self.hold
        # pre_hold = self.pre_hold
        pre_hold = self.pre_hold
        price = self.price
        pre_price = self.pre_price
        deal = self.today_deal
        contracts_info = self.contracts_info
        multiplier = pd.DataFrame(data={'ticker' : ['中证500', '中证1000', '沪深300', '上证50']})
        multiplier['multiplier'] = multiplier['ticker'].apply(cffex_multiplier)
        # print(multiplier)
        multiplier = pd.concat([multiplier, contracts_info[['ticker', 'multiplier']]], axis=0, ignore_index=True)
        
        # print(deal)
        # print(deal)
        # if 'fill_volume' not in deal.columns:
        #     deal.rename(columns={'volume': 'fill_volume', 'price': 'fill_price'}, inplace=True)

        deal["fill_volume"] = (
            (deal["longshort"] + deal["operation"])
            .apply(lambda x: 1 if x in ["多头开仓", "空头平仓"] else -1)
            * deal["fill_volume"]
        )
        deal = deal[deal["fill_volume"] != 0]
        print(f'----- deal ------')
        print(deal.head(2))
        # print((deal[deal['fill_volume'] > 0].copy())[['ticker', 'fill_volume', 'fill_price']])
        deal_buy = deal[deal['fill_volume'] > 0].copy()[['ticker', 'fill_volume', 'fill_price']].rename(columns={'fill_volume': 'buy_volume', 'fill_price': 'buy_price'})
        deal_sell = deal[deal['fill_volume'] < 0].copy()[['ticker', 'fill_volume', 'fill_price']].rename(columns={'fill_volume': 'sell_volume', 'fill_price': 'sell_price'})

        # 对deal 进行汇总, price为加权平均
        deal_buy = deal_buy.groupby(['ticker']).agg(
            {'buy_volume': 'sum', 'buy_price': lambda x: np.average(x, weights=deal_buy.loc[x.index, "buy_volume"])}
        ).reset_index()
        # print((deal_buy['buy_volume']*deal_buy['buy_price']).sum())
        
        deal_sell = deal_sell.groupby(['ticker']).agg(
            {'sell_volume': 'sum', 'sell_price': lambda x: np.average(x, weights=deal_sell.loc[x.index, "sell_volume"])}
        ).reset_index()
        # print((deal_sell['sell_volume']*deal_sell['sell_price']).sum())

        hold['volume'] = hold['longshort'].map({'多头':1, '空头':-1}) * hold['volume']
        pre_hold['volume'] = pre_hold['longshort'].map({'多头':1, '空头':-1}) * pre_hold['volume']
        hold = hold[['ticker', 'volume']].groupby(['ticker']).sum().reset_index()
        pre_hold = pre_hold[['ticker', 'volume']].groupby(['ticker']).sum().reset_index()

        data = pd.merge(hold, pre_hold, on='ticker', how='outer', suffixes=('', '_pre'))
        data = pd.merge(data, price[['ticker', 'close', 'settle']], on='ticker', how='left' )
        data = pd.merge(data, pre_price[['ticker', 'close', 'settle']], on='ticker', how='left', suffixes=('', '_pre'))
        data = pd.merge(data, deal_buy, on='ticker', how='outer')
        data = pd.merge(data, deal_sell, on='ticker', how='outer')
        data = pd.merge(data, multiplier, on='ticker', how='left')

        # write_file(data, file_type='xls', dest_type='dav', dest_path=os.path.join(self.account_name, 'account', 'estimate_pnl_{}.xls'.format(self.trade_date)), index=False)
        data.fillna(0, inplace=True)
        estimate_pnl_by_close = (
            data["volume"].mul(data["close"]).mul(data["multiplier"]).sum()
            - data["volume_pre"].mul(data["close_pre"]).mul(data["multiplier"]).sum()
            - data["buy_volume"].mul(data["buy_price"]).mul(data["multiplier"]).sum()
            - data["sell_volume"].mul(data["sell_price"]).mul(data["multiplier"]).sum()
        )
        
        estimate_pnl_by_settle = (
            data["volume"].mul(data["settle"]).mul(data["multiplier"]).sum()
            - data["volume_pre"].mul(data["settle_pre"]).mul(data["multiplier"]).sum()
            - data["buy_volume"].mul(data["buy_price"]).mul(data["multiplier"]).sum()
            - data["sell_volume"].mul(data["sell_price"]).mul(data["multiplier"]).sum()
        )
        return estimate_pnl_by_close, estimate_pnl_by_settle


# class FuturesPosition:
#     def __init__(self, ticker, longshort, volume):
#         self.ticker = ticker
#         self.longshort = longshort
#         self.volume = volume


class ProductAccount:
    def __init__(self, product_name, trade_date, product_attribute=None, owner='kf',
                #  t_minus_2_net_asset, t_minus_1_profit_loss, subscription_redemption_cash):
                # bv_type = None,
                # bv_coef = None,
                # bv_const = None,
                # bookvalue = None,
        ):
        self.product_name = product_name
        self.product_attribute = product_attribute
        self.product_owner = owner
        self.trade_date = trade_date
        self.pre_date = Calendar.last_trading_day(trade_date).strftime('%Y%m%d')

        
        self.t_minus_1_date = Calendar.last_trading_day(trade_date).strftime('%Y%m%d')
        self.t_minus_2_date = Calendar.last_trading_day(self.t_minus_1_date).strftime('%Y%m%d')
        self.t_minus_2_net_asset = 0
        self.t_minus_1_pnl = 0
        self.subscription_redemption_cash = 0

        
        # ========================
        self.stock_accounts = []
        self.futures_accounts = []
        
        self._future_minimal_hold = None
        
        
        # self.bv_type = bv_type
        # self._bookvalue = bookvalue
        

        self._net_asset = None
        self._stock_value = None
        self._short_value = None
        self._non_stock_long_value = None
        self._available_fund = None
        self._sub_redeem_fund = None
        
        self._pre_net_asset = None
        self._pre_stock_value = None
        self._pre_short_value = None
        self._pre_non_stock_long_value = None
        self._pre_available_fund = None

        self._stock_value_pre_close = None
        self.stock_value_pre_close_crpt_future = None
        
        self.future_summary = None
    # @property
    # def logic_type(self):
    #     return 
    
    def _sum_volume(self, *series_volume):
        result = pd.Series(dtype=float)
        for series in series_volume:
            result = result.add(series, fill_value=0)
        return result
    
    def _calcu_volume_value(self, hold, price):
        # print(f'pre_close: {self.pre_close.head()}')
        value_df = pd.merge(hold.set_index('ticker'), price.set_index('ticker'),
                                            left_index=True, right_index=True,
                                            how='left')
        value = value_df['volume'].mul(value_df['close']).sum()
        return value

    @property
    def future_minimal_hold(self):
        if self._future_minimal_hold is None:
            future_holds = []
            for futures_account in self.futures_accounts:
                # print(futures_account.account_name)
                # print(futures_account.minimal_hold)
                future_holds.append(futures_account.minimal_hold.set_index('ticker')['volume'])
            df = self._sum_volume(*future_holds).to_frame('volume').reset_index()
            if df.empty:
                df = pd.DataFrame(columns=['ticker', 'volume'])
            self._future_minimal_hold = df
            # print(df)
        return self._future_minimal_hold

            
        
    @property
    def stock_value(self):
        if self._stock_value is None:
            self._stock_value = sum(account.stock_value for account in self.stock_accounts)
        return self._stock_value
    
    @property
    def pre_stock_value(self):
        if self._pre_stock_value is None:
            self._pre_stock_value = sum(account.pre_stock_value for account in self.stock_accounts)
        return self._pre_stock_value
    
    @property
    def stock_value_pre_close(self):
        if self._stock_value_pre_close is None:
            self._stock_value_pre_close = sum(account.stock_value_pre_close for account in self.stock_accounts)
        return self._stock_value_pre_close
        
    
    def get_stock_value_pre_close_crpt_future(self, product_future_unit):
        try:
            stock_value_pre_close_crpt_future = self.stock_value_pre_close * product_future_unit.loc[self.product_name]
        except KeyError:
            stock_value_pre_close_crpt_future = pd.Series(index=['HS300', 'ZZ500', 'ZZ1000'], data=[0, 0, 0])
        self.stock_value_pre_close_crpt_future = stock_value_pre_close_crpt_future
        return stock_value_pre_close_crpt_future  # Series
    
    def get_product_exp(self):
        # print(self.stock_value_pre_close_crpt_future)
        # print(self.future_minimal_hold)
        ds = self.stock_value_pre_close_crpt_future.add(self.future_minimal_hold.set_index('ticker')['volume'], fill_value=0)
        return ds
        
        
        
    @property
    def net_asset(self):
        if self._net_asset is None:
            self._net_asset = self._get_product_net_asset(self.trade_date)
        return self._net_asset
        
    @property
    def pre_net_asset(self):
        if self._pre_net_asset is None:
            self._pre_net_asset = self._get_product_net_asset(self.pre_date)
        return self._pre_net_asset
    
    @property
    def sub_redeem_fund(self):
        if self._sub_redeem_fund is None:
            self._sub_redeem_fund = tools.get_product_sub_redeem_influence(self.product_name, self.trade_date)
        return self._sub_redeem_fund

    @property
    def future_long_value_by_index_price(self):
        return sum(account.long_value_by_index_price for account in self.futures_accounts)
    @property
    def future_short_value_by_index_price(self):
        return sum(account.short_value_by_index_price for account in self.futures_accounts)
        
        
    # @property
    # def bookvalue(self):
    #     if self._bookvale is None:
    #         self._bookvalue = self.cal_bookvalue()
    #     return self._bookvale
    
    @property
    def stock_accounts_pnl(self):
        return sum(account.account_pnl for account in self.stock_accounts)
    
    def _get_product_net_asset(self, date):
        if self.product_owner == 'fj':
            net_asset = tools.get_product_asset_from_pnl_fj(self.product_name, date)
        else:
            # 如果指定日期没有净资产, 往前估计获取
            try:
                net_asset = tools.get_product_data_from_nav(product_name=self.product_name, 
                                                                    date=date, 
                                                                    col_name='资产净值')
            except KeyError:
                try:
                    pre_date = Calendar.last_trading_day(date).strftime('%Y%m%d')
                    pre_asset = tools.get_product_data_from_nav(product_name=self.product_name, 
                                                        date=pre_date, 
                                                        col_name='资产净值')
                    print(pre_asset)
                    logger.warning(f'No net asset data on {date}, using estimated pre net asset {pre_date} instead.')
                except KeyError:
                    pre2_date = Calendar.last_trading_day(pre_date).strftime('%Y%m%d')
                    pre2_asset = tools.get_product_data_from_nav(product_name=self.product_name,
                                                                    date=pre2_date,
                                                                    col_name='资产净值')
                    pre_estimated_pnl = tools.get_product_data_from_pnl(product_name=self.product_name,
                                                                    date=pre_date,
                                                                    col_name='日盈亏')
                    # TODO 申购金要加上
                    pre_asset = pre2_asset + pre_estimated_pnl + self.subscription_redemption_cash
                    logger.warning(f'No net asset data on {self.trade_date}, using estimated pre net asset {pre2_date} instead.')
                    
                estimated_pnl = tools.get_product_data_from_pnl(product_name=self.product_name,
                                                                    date=date,
                                                                    col_name='日盈亏')
                print(estimated_pnl)
                # TODO
                net_asset = pre_asset + estimated_pnl + self.subscription_redemption_cash
        return net_asset
        
        
    
    def _get_sub_redeem_cash(self, date):
        pass
        

    def add_stock_account(self, stock_account):
        self.stock_accounts.append(stock_account)

    def add_futures_account(self, futures_account):
        self.futures_accounts.append(futures_account)
        
        
        
        
    # def load_product_data(self):
    #     if self.product_owner == 'kf':
    #         self.t_minus_2_net_asset = tools.get_product_data_from_nav(self.product_name, self.t_minus_2_date, col_name='资产净值')
    #         self.t_minus_1_pnl = tools.get_product_data_from_pnl(self.product_name, self.t_minus_1_date, col_name='日盈亏')
    #         self.estimated_t_net_asset = self.t_minus_2_net_asset + self.t_minus_1_pnl + self.subscription_redemption_cash
    #     elif self.product_owner == 'fj':
    #         self.estimated_t_net_asset = tools.get_product_asset_from_pnl_fj(self.product_name, self.t_minus_1_date)
    #     else:
    #         raise ValueError('Invalid product owner')

    # def summary_info(self):
    #     self.total_stock_value_long = sum(account.pre_stock_value_long for account in self.stock_accounts)

    # def calculate_adjusted_bookvalue(self, initial_bookvalue, adjustment_coefficient, adjustment_constant):
    #     return (adjustment_coefficient * initial_bookvalue) + adjustment_constant

    # def distribute_bookvalue(self, total_bookvalue, adjustments=None):
    #     if adjustments is not None:
    #         for adjustment in adjustments:
    #             pass

    #     remaining_bookvalue = total_bookvalue
    #     # total_stock_value = sum(account.pre_stock_value_long for account in self.stock_accounts)
    #     for account in self.stock_accounts:
    #         # if account not in adjustments:
    #         account_proportion = account.pre_stock_value_long / self.total_stock_value_long
    #         account.bookvalue = remaining_bookvalue * account_proportion

    # def calculate_bookvalue_long_only(self, adjustment_coefficient=1, adjustment_constant=0):
    #     # cal long value sum in future account
    #     [account.cal_market_value() for account in self.futures_accounts]
    #     total_futures_long_value = sum(account.pre_long_value for account in self.futures_accounts)
    #     initial_bookvalue = self.estimated_t_net_asset - total_futures_long_value
    #     adjusted_bookvalue = self.calculate_adjusted_bookvalue(initial_bookvalue, adjustment_coefficient, adjustment_constant)
    #     self.distribute_bookvalue(adjusted_bookvalue)

    # def calculate_bookvalue_neutral(self, stock_position_ratio=0.86, adjustment_coefficient=1, adjustment_constant=0):
    #     initial_amount = self.estimated_t_net_asset
    #     initial_bookvalue = initial_amount * stock_position_ratio
    #     adjusted_bookvalue = self.calculate_adjusted_bookvalue(initial_bookvalue, adjustment_coefficient, adjustment_constant)
    #     self.distribute_bookvalue(adjusted_bookvalue)

    # def calculate_dmatype_product_bookvalue(self, logic_config_df, index_close_df):
    #     # stock_account.bookvalue = calculate_product_future_value(stock_account.futures_positions)
    #     for stock_account in self.stock_accounts:
    #         account_name = stock_account.account_name
    #         bookvalue = tools.get_dma_account_logic_size(account_name, logic_config_df, index_close_df)
    #         stock_account.bookvalue = bookvalue
        
    # def _cal_bookvalue(self, 
    #                 #   logic_config_df, index_close_df,
    #                 #   stock_position_ratio=0.86, adjustment_coefficient=1, adjustment_constant=0
    #                   ):
        
        
    #     if self.product_owner == 'fj':
    #         print('run')
    #         self.calculate_dmatype_product_bookvalue(logic_config_df, index_close_df)
    #     else:
    #         if self.product_attribute == 'long':
    #             self.calculate_bookvalue_long_only(adjustment_coefficient, adjustment_constant)
    #         elif self.product_attribute == 'neutral':
    #             self.calculate_bookvalue_neutral(stock_position_ratio, adjustment_coefficient, adjustment_constant)
    #         elif self.product_attribute == 'dma':
    #             self.calculate_dmatype_product_bookvalue(logic_config_df, index_close_df)
    #         else:
    #             raise ValueError(f"Invalid product attribute: {self.product_attribute}")

    # def transfer_funds_if_needed(self, min_transfer_amount=50000):
    #     for stock_account in self.stock_accounts:
    #         bookvalue_increase = stock_account.bookvalue - stock_account.market_value
            
    #         if bookvalue_increase > stock_account.available_fund:
    #             required_transfer = bookvalue_increase - stock_account.available_fund
    #             transfer_amount = max(min_transfer_amount, round(required_transfer / min_transfer_amount) * min_transfer_amount)
                
    #             for futures_account in self.futures_accounts:
    #                 if transfer_amount <= 0:
    #                     break  # 已完成所需转账
    #                 if futures_account.available_fund > 0:
    #                     actual_transfer = min(transfer_amount, futures_account.available_fund)
    #                     futures_account.available_fund -= actual_transfer
    #                     futures_account.total_transfer_out += actual_transfer
                        
    #                     stock_account.available_fund += actual_transfer
    #                     stock_account.total_transfer_in += actual_transfer
                        
    #                     transfer_amount -= actual_transfer
                        
    #                     print(f"Transferred {actual_transfer} from futures to stock account.")
                
    #             if transfer_amount > 0:
    #                 print("Insufficient total funds in futures accounts for transfer.")
