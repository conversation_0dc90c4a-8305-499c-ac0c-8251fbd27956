from imapclient import IMAPClient
import email
from email.header import decode_header
from email.message import EmailMessage
import os
import re
from datetime import datetime, timedelta
import random

def decode_mime_words(s):
    """
    Decode MIME encoded words in a string to a human-readable form.
    """
    decoded_words = decode_header(s)
    # Join decoded parts and handle different charsets
    return ''.join(
        part.decode(encoding or 'utf-8') if isinstance(part, bytes) else part
        for part, encoding in decoded_words
    )
def format_date_for_imap(date_str):
    """
    Convert a date string from 'YYYY-MM-DD' to 'DD-Mon-YYYY' which is required by IMAP.
    """
    date_obj = datetime.strptime(date_str, '%Y%m%d')
    return date_obj.strftime('%d-%b-%Y')

def download_excel_attachments(server, email_user, email_pass, folder, start_date=None, end_date=None, title_reg=None, attachment_reg=None):
    """
    Downloads Excel attachments from email based on specified criteria.
    
    :param server: IMAP server address
    :param email_user: Username for the email account
    :param email_pass: Password for the email account
    :param folder: Mailbox folder to search in
    :param start_date: Start date for email search (YYYY-MM-DD)
    :param subject_keyword: Optional keyword to filter emails by subject
    :param attachment_keyword: Optional keyword to search in attachment filenames
    :return: List of pandas DataFrames read from the downloaded Excel files
    """
    tmp_dir = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
    if not os.path.exists(os.path.join(tmp_dir, 'tmp', 'attachments')):
        os.makedirs(os.path.join(tmp_dir, 'tmp', 'attachments'))
    # print(tmp_path)
    
    with IMAPClient(server) as client:
        client.login(email_user, email_pass)
        client.select_folder(folder)

        # Convert date format for IMAP usage
        search_criteria = []
        if start_date:
            formatted_start_date = format_date_for_imap(start_date)
            search_criteria += ['SINCE', formatted_start_date]
        if end_date:
            formatted_end_date = format_date_for_imap(end_date)
            search_criteria += ['BEFORE', formatted_end_date]
        # print(formatted_start_date)

        # Fetch all emails since start date
        messages = client.search(search_criteria)
        # dataframes = []
        attachment_list = []

        # Compile the regex pattern
        if title_reg is None:
            title_reg = r'.*'
        pattern = re.compile(title_reg)

        for message_id in messages:
            # Fetch only the envelope (lightweight fetch)
            response = client.fetch(message_id, ['ENVELOPE'])
            envelope = response[message_id][b'ENVELOPE']
            subject = decode_mime_words(envelope.subject.decode())

            # Check if the subject matches the regex pattern
            if pattern.search(subject):
                # Fetch the full email if the subject matches
                response = client.fetch(message_id, ['RFC822'])
                message = email.message_from_bytes(response[message_id][b'RFC822'])

                for part in message.walk():
                    if part.get_content_maintype() == 'multipart' or part.get('Content-Disposition') is None:
                        continue

                    filename = part.get_filename()
                    if filename:
                        filename = decode_mime_words(filename)
                        # print(filename)

                    if attachment_reg is None:
                        attachment_reg = r'.*'
                    if filename and re.compile(attachment_reg).search(filename):
                    # if filename and filename.endswith(('.xls', '.xlsx', '.csv')) and (attachment_reg in filename if attachment_reg else True):
                        extension = filename.split('.')[-1]
                        # print(extension)
                        file_path = os.path.join(tmp_dir, 'tmp', 'attachments', subject + '_' + str(random.randrange(1000)) + '.' + extension)
                        # print(file_path)
                        with open(file_path, 'wb') as f:
                            f.write(part.get_payload(decode=True))
                        # df = pd.read_excel(file_path)
                        attachment_list.append(file_path)
                        # os.remove(file_path)
        return attachment_list
