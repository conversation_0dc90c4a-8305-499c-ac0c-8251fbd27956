import os
from duckdb import order
import pandas as pd
import numpy as np
from loguru import logger
from time import sleep
# from misc.kdb import Kdb
from datetime import date, datetime, timedelta
import re
# from typing import Literal
import uuid

import math
try:
    from typing import Literal
except ImportError:
    from typing_extensions import Literal

from data_utils.trading_calendar import Calendar

from misc.utils import symbol_with_market, symbol_to_exchange_cn, symbol_to_swapcode


# ======================================
KF_ALGO_DICT = {
    'kf_twap_plus' : 101,
    'kf_vwap_plus' : 102,
    'kf_twap_core' : 103,
    'kf_vwap_core' : 104,
    'kf_pov_core'  : 105,
    'kf_t0'        : 106,
    'kf_passthru'  : 201,
    'JINGLE_VWAP'  : 213,
    'HX_SMART_VWAP': 502,
    '(gtja)_HX_SMART_VWAP': 207,
    
    'JINGLE_T0'    : 214,
    'HX_SMART_T0'  : 503,
    '(gtja)_ld_vwap'      : 210,
    'ld_t0'        : 225,

    'ft_vwap_ai_plus'  : 204,
}

YULIANG_ALGO_DICT = {
    # 1、皓兴TWAP:10060101
    # 2、皓兴VWAP：10060103
    # 3、皓兴T0：10060102
    # 4、跃然T0：10020101
    # 5、馥数：10210101
    'YLKF-smartvwap'  : 10210101,
    'HX_SMART_TWAP'   : 10060101,
    'HX_SMART_VWAP'   : 10060103,
    'HX_T0'     : 10060102,
    'YR_T0'     : 10020101,
}


# ======================================
def get_scores(scores_dir, date_str, tag):
    scores_file = os.path.join(scores_dir, "predict_{}_{}_v6.fea".format(date_str.replace("-", ""), tag))
    logger.info("waiting for scores file: {} ...".format(scores_file))
    while not os.path.exists(scores_file):
        sleep(2)
    # 再等待5s，确保实盘的时候文件传输完整
    sleep(5)
    scores = pd.read_feather(scores_file)

    scores['code_int'] = scores['code_int'].astype(int)
    scores = scores.pivot(index='date', columns='code_int', values='zscore')
    logger.info('scores: \n {}'.format(scores))
    return scores

# def convert_orders(orders, format):
#     if format == 'ld':
#         return convert_ld_orders(orders)
#     elif format == 'zx':
#         return convert_zx_orders(orders)
#     elif format == 'matic':
#         return convert_matic_orders(orders)
#     elif format == 'hx':
#         return convert_hx_orders(orders)
#     elif format == 'xt':
#         return convert_xt_orders(orders)
#     else:
#         raise ValueError('format {} not supported'.format(format))

# 生成ld格式的下单文件
def convert_ld_orders(orders):
    new_orders = pd.DataFrame(columns=['证券代码', '导入数量', '导入价格', '方向', '市场'])
    new_orders['证券代码'] = orders['code']
    new_orders['导入数量'] = orders['num']
    new_orders['方向'] = new_orders['导入数量'].apply(lambda x: '买入' if x > 0 else '卖出')
    new_orders['导入数量'] = new_orders['导入数量'].apply(lambda x: abs(x))
    new_orders['导入价格'] = ''
    new_orders['市场'] = ''
    return new_orders

# 生成zx格式的下单文件
def convert_zx_orders(orders):
    new_orders = orders.rename(columns={'code': 'sym', 'num': 'amount'})
    new_orders['market'] = ''
    new_orders['weight'] = 0
    new_orders['side'] = new_orders['amount'].apply(lambda x: 1 if x >= 0 else 2)
    new_orders['amount'] = new_orders['amount'].apply(lambda x: abs(x))
    new_orders = new_orders[['sym', 'market', 'amount', 'weight', 'side']]
    return new_orders

# 生成matic格式的下单文件
# 证券代码	市场	委托方向	委托数量	限价
# 514	2	4	100	0
# 600448	1	4	100	0
# 市场：上海1，深圳2 沪港通G 深港通S
# 交易方向：买入1，卖出2，担保品买入3，担保品卖出4，融资买入A，融券卖出（专项）B，买券还券（专项）C
# def convert_matic_orders(orders):
#     new_orders = pd.DataFrame(columns=['证券代码', '市场', '委托方向', '委托数量', '限价'])
#     new_orders['证券代码'] = orders['code']
#     new_orders['市场'] = new_orders['证券代码'].apply(lambda x: 1 if x >= 600000 else 2)
#     new_orders['委托数量'] = orders['num']
#     new_orders['委托方向'] = new_orders['委托数量'].apply(lambda x: 1 if x > 0 else 2)
#     new_orders['委托数量'] = new_orders['委托数量'].abs()
#     new_orders['限价'] = 0
#     return new_orders

# 交易所	成份代码	权重	成份数量	方向	替代交易所	替代代码	替代数量
# 2	415		100	1			
# 1	601668		100	2			
# 1	688148		200	1			
# 备注：第一行字段名必须有，交易所：1（沪A），2（深A），方向：1（买入），2（卖出）
# def convert_hx_orders(orders):
#     new_orders = pd.DataFrame(columns=['交易所', '成份代码', '权重', '成份数量', '方向', '替代交易所', '替代代码', '替代数量'])
#     new_orders['成份代码'] = orders['code']
#     new_orders['交易所'] = new_orders['成份代码'].apply(lambda x: 1 if x >= 600000 else 2)
#     new_orders['成份数量'] = orders['num']
#     new_orders['方向'] = new_orders['成份数量'].apply(lambda x: 1 if x > 0 else 2)
#     new_orders['成份数量'] = new_orders['成份数量'].abs()
#     for col in ['权重', '替代交易所', '替代代码', '替代数量']:
#         new_orders[col] = ''
#     return new_orders

# sym	market	amount	weight	side		
# 100		200	0	1		
# 2108		100	0	0		
# 601778		100	0	0		
# 688223		200	0	1		
# 备注：side：0（买入），1（卖出）
def convert_xt_orders(orders):
    new_orders = pd.DataFrame(columns=['sym', 'market', 'amount', 'weight', 'side'])
    new_orders['sym'] = orders['code']
    new_orders['amount'] = orders['num']
    new_orders['market'] = ''
    new_orders['weight'] = 0
    new_orders['side'] = new_orders['amount'].apply(lambda x: 0 if x >= 0 else 1)
    new_orders['amount'] = new_orders['amount'].apply(lambda x: abs(x))
    return new_orders

# def convert_xt1_orders(orders):
#     new_orders = pd.DataFrame(columns=['代码', '市场', '数量', '相对权重', '方向'])
#     new_orders['代码'] = orders['code']
#     new_orders['数量'] = orders['num']
#     new_orders['市场'] = ''
#     new_orders['相对权重'] = 0
#     new_orders['方向'] = new_orders['数量'].apply(lambda x: 0 if x >= 0 else 1)
#     new_orders['数量'] = new_orders['数量'].apply(lambda x: abs(x))
#     return new_orders


# ======================================

def convert_matic_orders(orders):
    new_orders = pd.DataFrame(columns=['证券代码', '市场', '委托方向', '委托数量', '限价'])
    new_orders['证券代码'] = orders['symbol']
    new_orders['市场'] = new_orders['证券代码'].apply(lambda x: 1 if x >= 600000 else 2)
    new_orders['委托数量'] = orders['order_shares']
    new_orders['委托方向'] = new_orders['委托数量'].apply(lambda x: 1 if x > 0 else 2)
    new_orders['委托数量'] = new_orders['委托数量'].abs()
    new_orders['限价'] = 0
    orders_buy = new_orders[new_orders['委托方向'] == 1].drop(columns=['委托方向'])
    orders_sell = new_orders[new_orders['委托方向'] == 2].drop(columns=['委托方向'])
    return orders_buy, orders_sell

def convert_matic_ai_orders(orders):
    new_orders = pd.DataFrame(columns=['证券代码', '市场', '交易方向', '委托数量', '限价'])
    new_orders['证券代码'] = orders['symbol']
    new_orders['市场'] = new_orders['证券代码'].apply(lambda x: 1 if x >= 600000 else 2)
    new_orders['委托数量'] = orders['order_shares']
    new_orders['交易方向'] = new_orders['委托数量'].apply(lambda x: 3 if x > 0 else 4)
    new_orders['委托数量'] = new_orders['委托数量'].abs()
    new_orders['限价'] = 0
    return new_orders
    
def order_num_attatch_to_hundreds(num):
    pass
    
    
def convert_xt1_orders(orders):
    new_orders = pd.DataFrame(columns=['代码', '市场', '数量', '相对权重', '方向'])
    new_orders['代码'] = orders['symbol']
    new_orders['数量'] = orders['order_shares']
    new_orders['市场'] = ''
    new_orders['相对权重'] = 0
    new_orders['方向'] = new_orders['数量'].apply(lambda x: 0 if x >= 0 else 1)
    new_orders['数量'] = new_orders['数量'].apply(lambda x: abs(x))
    return new_orders

def convert_jx_twap_orders(orders):
    cols = [
            'symbol',
            'order_type',
            'bid_ask',
            'price_type',
            'price',
            'volume',
            'account_ID',
            'owner_code',
            'algo_type',
            'start_time',
            'end_time',
            'interval',
            'algrithm_price_type',
            'algrithm_price',
            'execute_type',
    ]
    new_orders = pd.DataFrame(columns=cols)
    new_orders['symbol'] = orders['symbol'].apply(symbol_with_market)
    new_orders['order_type'] = '算法'
    new_orders['bid_ask'] = orders['order_shares'].apply(lambda x: '买入' if x > 0 else '卖出')
    new_orders['price_type'] = ''
    new_orders['price'] = ''
    new_orders['volume'] = orders['order_shares'].abs()
    new_orders['account_ID'] = ''
    new_orders['owner_code'] = ''
    new_orders['algo_type'] = 'TWAP'
    new_orders['start_time'] = 93000
    new_orders['end_time'] = 100000
    new_orders['interval'] = 120
    new_orders['algrithm_price_type'] = '本方价一'
    new_orders['algrithm_price'] = 0
    new_orders['execute_type'] = '全部成交'
    return new_orders


def convert_jx_iceberg_orders(orders):
    cols = [
        'symbol',
        'order_type',
        'bid_ask',
        'price_type',
        'price',
        'volume',
        'account_ID',
        'owner_code',
        'algo_type',
        'start_time',
        'end_time',
        'cancel_interval',
        'algrithm_price_type',
        'execute_type',
        'min_volume',
        'max_volume',
        'float_ratio',
    ]
    new_orders = pd.DataFrame(columns=cols)
    new_orders['symbol'] = orders['symbol'].apply(symbol_with_market)
    new_orders['order_type'] = '算法'
    new_orders['bid_ask'] = orders['order_shares'].apply(lambda x: '买入' if x > 0 else '卖出')
    new_orders['price_type'] = ''
    new_orders['price'] = ''
    new_orders['volume'] = orders['order_shares'].abs()
    new_orders['account_ID'] = ''
    new_orders['owner_code'] = ''
    new_orders['algo_type'] = 'Iceberg'
    new_orders['start_time'] = '093000'
    new_orders['end_time'] = '100000'
    new_orders['cancel_interval'] = 40
    new_orders['algrithm_price_type'] = '本方价一'
    new_orders['execute_type'] = '全成即补'
    new_orders['min_volume'] = new_orders['volume'].apply(lambda x: iceberg_split_volume(x, 5))
    new_orders['max_volume'] = 10000
    new_orders['float_ratio'] = ''  # int n, price of 1*(1+n%)
    return new_orders
    
# return min of 100 shares or num_splits of volume
def iceberg_split_volume(volume, num_splits):
    min_floor = math.ceil(max(volume,100) // num_splits / 100) *100
    min_ceil = 2000
    return min(min_floor, min_ceil)
    

def trans_posiion_split_changes(changes, full_hold, hold_1, hold_2, subject_trans= False, subject_trans_amount=0):
    # changes = pd.concat([hold.set_index(['ticker']), pos.set_index(['ticker'])], axis=1, join='outer').fillna(0)

    # changes['order_volume'] = changes['target_volume'] - changes['volume']
    """
    把调仓交易拆成两个账户的交易
    changes: symbol, order_shares
    hold:    symbol, hold_shares, available_shares
    hold_1:  symbol, hold_shares, available_shares
    hold_2:  symbol, hold_shares, available_shares
    subject_trans: bool, 是否主动移仓
    subject_trans_amount: int, 主动移仓金额
    """
    # print(changes)
    
    changes.set_index('symbol', inplace=True)
    
    changes = changes.merge(full_hold.set_index('symbol'), left_index=True, right_index=True, how='outer')

    changes = changes.merge(hold_1.set_index('symbol'), left_index=True, right_index=True, how='outer', suffixes=('', '_h1'))
    changes = changes.merge(hold_2.set_index('symbol'), left_index=True, right_index=True, how='outer', suffixes=('', '_h2'))
    changes.fillna(0, inplace=True)

    changes['order_volume_h1'] = np.max([ np.where( changes['order_shares'].values <0, changes['order_shares'].values, 0), -changes['available_shares_h1'].values], axis=0)
    changes['order_volume_h2'] = changes['order_shares'] - changes['order_volume_h1']

    changes_1 = changes[changes['order_volume_h1'] != 0]['order_volume_h1'].to_frame().rename(columns={'order_volume_h1': 'order_shares'}).reset_index()
    changes_2 = changes[changes['order_volume_h2'] != 0]['order_volume_h2'].to_frame().rename(columns={'order_volume_h2': 'order_shares'}).reset_index()
    changes_1['tag'] = 'h1'
    changes_2['tag'] = 'h2'
    
    return changes_1, changes_2


def convert_gtrade_orders(changes, **kwargs):
    account_id = kwargs.get('account_id')
    strategy_paras = kwargs.get('strategy_paras', None)
    
    strategy_type = kwargs.get('strategy_type', 'XT_TWAP')
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '102000')
    no_trade_extreme = kwargs.get('no_trade_extreme', '1')

    
    if account_id is None:
        raise ValueError('account_id is required')
    
    if strategy_paras is None:
        if strategy_type in ['XT_TWAP', 'XT_VWAP', 'XT_DMA', 'XT_PINLINE', 'XT_VP']:
            strategy_paras = 'PR=0.10;MOA=2500'
        else:
            raise ValueError('strategy_paras is required')
        
    cols = [
        '策略名称',
        '资金账户',
        '证券代码',
        '交易板块',
        '委托数量',
        '起始时间',
        '结束时间',
        '买卖方向',
        '价格类型',
        '委托价格',
        '涨停不卖跌停不买标志',
        '参数说明',
        '投资备注'
    ]

    # 移仓模式
    if False:
        pass
    # if kwargs.get('trans_posiion', False) == True:
    #     pre_close = kwargs.get('pre_close', pd.DataFrame(columns=['symbol', 'close_date', 'close']))
    #     old_account_id = kwargs['old_account_id']
    #     new_account_id = kwargs['new_account_id']
        
    #     old_account_name = kwargs['old_account_name']
    #     new_account_name = kwargs['new_account_name']
    #     acc_name = kwargs.get('acc', 0)   # 账户名, 用于信息展示的 index
        
    #     from .tools import read_stand_gtrade_hold
    #     hold_1 = read_stand_gtrade_hold(old_account_name)
    #     hold_2 = read_stand_gtrade_hold(new_account_name)
        
    #     hold_1.rename(columns={
    #         'ticker': 'symbol',
    #         'volume': 'hold_shares',
    #         'available_volume': 'available_shares'
    #     }, inplace=True)
    #     hold_2.rename(columns={
    #         'ticker': 'symbol',
    #         'volume': 'hold_shares',
    #         'available_volume': 'available_shares'
    #     }, inplace=True)
    #     full_hold = hold_1.set_index('symbol').add(hold_2.set_index('symbol'), fill_value=0).fillna(0).reset_index()
        
    #     changes_1, changes_2 = trans_posiion_split_changes(changes, full_hold, hold_1, hold_2)
        
    #     full_changes = pd.concat([changes_1, changes_2], axis=0, ignore_index=True)
        
    #     # merge pre_close and statistic data for trading info
    #     # ======================================================================
    #     full_changes = full_changes.merge(pre_close[['symbol', 'close']], on='symbol', how='left')
    #     # write_file(full_changes, file_type='xls', dest_type='dav', dest_path='full_changes.xls', index=False)
        
    #     tag_h1_buy = (full_changes['tag'] == 'h1') & (full_changes['order_shares'] > 0)
    #     tag_h1_sell = (full_changes['tag'] == 'h1') & (full_changes['order_shares'] < 0)
    #     tag_h2_buy = (full_changes['tag'] == 'h2') & (full_changes['order_shares'] > 0)
    #     tag_h2_sell = (full_changes['tag'] == 'h2') & (full_changes['order_shares'] < 0)
        
    #     h1_buy_value = (full_changes.loc[tag_h1_buy, 'order_shares'] * full_changes.loc[tag_h1_buy, 'close']).sum()
    #     h1_sell_value = (full_changes.loc[tag_h1_sell, 'order_shares'] * full_changes.loc[tag_h1_sell, 'close']).sum()
    #     h2_buy_value = (full_changes.loc[tag_h2_buy, 'order_shares'] * full_changes.loc[tag_h2_buy, 'close']).sum()
    #     h2_sell_value = (full_changes.loc[tag_h2_sell, 'order_shares'] * full_changes.loc[tag_h2_sell, 'close']).sum()
        
    #     info = {
    #         'acc_1_buy' : f'{h1_buy_value/10000:,.2f} 万',
    #         'acc_1_sell' : f'{h1_sell_value/10000:,.2f} 万',
    #         'acc_2_buy' : f'{h2_buy_value/10000:,.2f} 万',
    #         'acc_2_sell' : f'{h2_sell_value/10000:,.2f} 万',
    #     }
    #     logger.warning(f'{acc_name} 移仓:')
    #     print('---------------------------')
    #     print(pd.DataFrame(info, index=[acc_name]))    
    #     print('---------------------------')
    #     # ======================================================================
        
    #     full_order = pd.DataFrame(columns=cols)
    #     full_order['证券代码'] = full_changes['symbol'].apply(lambda x: str(x).zfill(6))
    #     full_order['策略名称'] = strategy_type
    #     full_order['资金账户'] = full_changes['tag'].apply(lambda x: old_account_id if x == 'h1' else new_account_id)
    #     full_order['交易板块'] = full_order['证券代码'].apply(lambda x: '10' if x.startswith('6') else '00')
    #     full_order['委托数量'] = full_changes['order_shares'].abs().astype(int)
    #     full_order['起始时间'] = start_time
    #     full_order['结束时间'] = end_time
    #     full_order['买卖方向'] = full_changes['order_shares'].apply(lambda x: '100' if x > 0 else '101')
    #     full_order['价格类型'] = '120'
    #     full_order['委托价格'] = '0'
    #     full_order['涨停不卖跌停不买标志'] = no_trade_extreme
    #     full_order['参数说明'] = strategy_paras
    #     full_order['投资备注'] = 'hx_kf_001'
    #     return full_order

        
    else:
        # 常规交易模式
        new_orders = pd.DataFrame(columns=cols)
        new_orders['证券代码'] = changes['symbol'].apply(lambda x: str(x).zfill(6))
        new_orders['策略名称'] = strategy_type
        new_orders['资金账户'] = account_id
        new_orders['交易板块'] = new_orders['证券代码'].apply(lambda x: '10' if x.startswith('6') else '00')
        new_orders['委托数量'] = changes['order_shares'].abs()
        new_orders['起始时间'] = start_time
        new_orders['结束时间'] = end_time
        new_orders['买卖方向'] = changes['order_shares'].apply(lambda x: '100' if x > 0 else '101')
        new_orders['价格类型'] = '120'
        new_orders['委托价格'] = '0'
        new_orders['涨停不卖跌停不买标志'] = no_trade_extreme
        new_orders['参数说明'] = strategy_paras
        new_orders['投资备注'] = 'hx_kf_001'
        
        return new_orders

        
def convert_gtrade_t0_orders(t0_volume, **kwargs):
    cols = [
        '策略名称',
        '资金账户',
        '证券代码',
        '交易板块',
        '委托数量',
        '起始时间',
        '结束时间',
        '买卖方向',       # 0
        '价格类型',       # 120
        '委托价格',       # 0
        '涨停不卖跌停不买标志',       # 1,使用,  0,不使用
        '参数说明',
        '投资备注'
    ]
    account_id = kwargs.get('account_id')
    strategy_paras = kwargs.get('strategy_paras', None)
    
    strategy_type = kwargs.get('strategy_type', 'YR_T0')
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '150000')
    no_trade_extreme = kwargs.get('no_trade_extreme', '1')

    
    if account_id is None:
        raise ValueError('account_id is required')
    
    if strategy_paras is None:
        if strategy_type in ['YR_T0']:
            strategy_paras = 'BTI=1;STI=2;BN=;MAA=2e+07'
        else:
            raise ValueError('strategy_paras is required')
        
    # 常规交易模式
    new_orders = pd.DataFrame(columns=cols)
    new_orders['证券代码'] = t0_volume['ticker'].apply(lambda x: str(x).zfill(6))
    new_orders['策略名称'] = strategy_type
    new_orders['资金账户'] = account_id
    new_orders['交易板块'] = new_orders['证券代码'].apply(lambda x: '10' if x.startswith('6') else '00')
    new_orders['委托数量'] = t0_volume['t0_volume'].abs()
    new_orders['起始时间'] = start_time
    new_orders['结束时间'] = end_time
    new_orders['买卖方向'] = 0
    new_orders['价格类型'] = '120'
    new_orders['委托价格'] = '0'
    new_orders['涨停不卖跌停不买标志'] = no_trade_extreme
    new_orders['参数说明'] = strategy_paras
    new_orders['投资备注'] = ''
    
    return new_orders
        
        
    
def convert_ehaifangzhou_orders(changes, **kwargs):
    cols = [
        '代码',
        '样本数量',
        '权重'
        '委托方式',
    ]
    new_orders = pd.DataFrame(columns=cols)
    new_orders['代码'] = changes['symbol'].apply(lambda x: str(x).zfill(6))
    new_orders['样本数量'] = changes['order_shares'].abs()
    new_orders['委托方式'] = changes['order_shares'].apply(lambda x: '买入' if x > 0 else '卖出')
    new_orders['权重'] = '50'
    
    return new_orders

def convert_kafang_atx_orders(changes, **kwargs):
    cols = [
        '算法类型',
        '账户名称',
        '算法实例',
        '证券代码',
        '任务数量',
        '交易方向',
        '开始时间',
        '结束时间',
        '涨跌停是否继续执行',
        '过期后是否继续执行',
        '其他参数',
        '交易市场',
    ]
    strategy_type = kwargs.get('strategy_type', 'VWAP')
    account_id = kwargs.get('account_id')
    strategy_instance = kwargs.get('strategy_instance', 'kf_vwap_plus')
    strategy_paras = kwargs.get('strategy_paras', None)
    
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    trade_extreme = kwargs.get('limit_action', '否')
    trade_expire = kwargs.get('trade_expire', '否')
    # order_remark = kwargs.get('order_remark', '')
    # participation_rate = kwargs.get('participation_rate', '')
    
    # if strategy_paras is None:
    #     strategy_paras = ':'.join([f'市场参与率={participation_rate}', f'篮子编号={order_remark}'])
    
    if strategy_paras is not None and isinstance(strategy_paras, dict):
        strategy_paras = ':'.join([f'{key}={value}' for key, value in strategy_paras.items()])
    
    new_orders = pd.DataFrame(columns=cols)
    new_orders['证券代码'] = changes['symbol'].apply(symbol_with_market)
    new_orders['任务数量'] = changes['order_shares'].abs()
    new_orders['交易方向'] = changes['order_shares'].apply(lambda x: '买入' if x > 0 else '卖出')
    new_orders['算法类型'] = strategy_type
    new_orders['账户名称'] = account_id
    new_orders['算法实例'] = strategy_instance
    new_orders['开始时间'] = '{}T{}000'.format(trading_date, start_time)
    new_orders['结束时间'] = '{}T{}000'.format(trading_date, end_time)
    new_orders['涨跌停是否继续执行'] = trade_extreme
    new_orders['过期后是否继续执行'] = trade_expire
    new_orders['其他参数'] = strategy_paras
    new_orders['交易市场'] = new_orders['证券代码'].apply(symbol_to_exchange_cn)
    return new_orders

def convert_zhongjin_kafang_orders(changes, **kwargs):
    cols = [
        '算法类型',
        '账户名称',
        '算法实例',
        '证券代码',
        '任务数量',
        '交易方向',
        '开始时间',
        '结束时间',
        '涨跌停设置',
        '过期后执行',
        '其他参数',
        '交易市场',
    ]
    strategy_type = kwargs.get('strategy_type', 'VWAP')
    account_id = kwargs.get('account_id')
    strategy_instance = kwargs.get('strategy_instance', 'kf_vwap_plus')
    strategy_paras = kwargs.get('strategy_paras', '')
    
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '102000')
    no_trade_extreme = kwargs.get('no_trade_extreme', '涨停不买跌停不卖')
    no_trade_expire = kwargs.get('no_trade_expire', '否')
    order_remark = kwargs.get('order_remark', '')
    
    new_orders = pd.DataFrame(columns=cols)
    new_orders['证券代码'] = changes['symbol'].apply(lambda x: str(x).zfill(6))
    new_orders['任务数量'] = changes['order_shares'].abs()
    new_orders['交易方向'] = changes['order_shares'].apply(lambda x: '买入' if x > 0 else '卖出')
    new_orders['算法类型'] = strategy_type
    new_orders['账户名称'] = account_id
    new_orders['算法实例'] = strategy_instance
    
    new_orders['开始时间'] = pd.to_datetime(trading_date + ' ' + start_time).strftime('%Y%m%dT%H%M%S')+'000'
    # '{}T{}000'.format(trading_date, start_time)
    new_orders['结束时间'] = pd.to_datetime(trading_date + ' ' + end_time).strftime('%Y%m%dT%H%M%S')+'000'
    # '{}T{}000'.format(trading_date, end_time)
    new_orders['涨跌停设置'] = no_trade_extreme
    new_orders['过期后执行'] = no_trade_expire
    new_orders['其他参数'] = order_remark
    new_orders['交易市场'] = new_orders['证券代码'].apply(symbol_to_exchange_cn)
    return new_orders

def convert_ax_onequant_lanzi_orders(changes, **kwargs):
    cols = [
        '代码',
        '单篮数量/相对权重',
    ]
    new_orders = pd.DataFrame(columns=cols)
    new_orders['代码'] = changes['symbol'].apply(symbol_with_market)
    new_orders['单篮数量/相对权重'] = changes['order_shares'].abs()
    return new_orders

def convert_jinshida_lanzi_orders(changes, **kwargs):
    start_time = kwargs.get('start_time', '09:30:00')
    end_time = kwargs.get('end_time', '10:20:00')
    tradingday = kwargs.get('tradingday', datetime.now().strftime('%Y%m%d'))
    start_time = pd.to_datetime(tradingday + ' ' + start_time).strftime('%H:%M:%S')
    end_time = pd.to_datetime(tradingday + ' ' + end_time).strftime('%H:%M:%S')
    
    cols = [
        '挂钩标的代码',
        '多空方向',
        '意向名义数量',
        '意向价格',
        '保护限价',
        '算法选择',
        '价格方式',
        '交易意向类型',
        '开始时间',
        '结束时间',
        '时间间隔(秒)',
        '量占比(%)',
    ]
    
    new_orders = pd.DataFrame(columns=cols)
    new_orders['挂钩标的代码'] = changes['symbol'].apply(lambda x: str(x).zfill(6))
    new_orders['多空方向'] = '看多'
    # new_orders['多空方向'] = changes['order_shares'].apply(lambda x: '看多' if x > 0 else '看空')
    new_orders['意向名义数量'] = changes['order_shares'].abs()
    new_orders['意向价格'] = '最新价'
    new_orders['保护限价'] = ''
    new_orders['算法选择'] = 'TWAP'
    new_orders['价格方式'] = '限价'
    new_orders['交易意向类型'] = changes['order_shares'].apply(lambda x: '买' if x > 0 else '卖')
    new_orders['开始时间'] = start_time
    new_orders['结束时间'] = end_time
    new_orders['时间间隔(秒)'] = '40'
    new_orders['量占比(%)'] = ''
    
    new_cols = [
'''挂钩标的代码
(合约代码/合约代码.市场)''',

'''多空方向
(看多/看空)''',

'''意向名义数量
(整数类型)''',

'''意向价格
(限价-不使用算法:浮点数类型)
(限价-TWAP或POV模式:最新价/最低价
/买一价/买二价/买三价/买四价/买五价
/卖一价/卖二价/卖三价/卖四价/卖五价)
(市价:对方最优价/本方最优价/即时成
交剩余撤销/全额成交或撤销/最优五档
剩余撤销/最优五档剩余转限/最优一档
剩余撤销/最优一档剩余转限)''',

'''保护限价
(上交所市价:浮点数)
(上交所限价及其他:不填)''',

'''算法选择
(不使用算法/TWAP/POV)''',

'''价格方式
(限价/市价)''',

'''交易意向类型
(买/卖)''',

'''开始时间
(不使用算法:不填)
(TWAP/POV:文本时间格式,不得大于结束时间)''',

'''结束时间
(不使用算法:不填)
(TWAP/POV:文本时间格式,不得小于开始时间)''',

'''时间间隔(秒)
(不使用算法:不填)
(TWAP/POV:文本时间格式。
时间间隔不得大于"结束时间"-"开始时间"。
间隔时间不得超过999秒)''',

'''量占比(%)
(不使用算法/TWAP:不填)
(POV:浮点数类型。
量占比必须大于0小于100。)''',
    ]
    new_orders.columns = new_cols
    return new_orders

def convert_guangfa_swap_orders(changes, **kwargs):
    start_time = kwargs.get('start_time', '09:30:00')
    end_time = kwargs.get('end_time', '10:20:00')
    tradingday = kwargs.get('tradingday', datetime.now().strftime('%Y%m%d'))
    start_time = pd.to_datetime(tradingday + ' ' + start_time).strftime('%H:%M:%S')
    end_time = pd.to_datetime(tradingday + ' ' + end_time).strftime('%H:%M:%S')
    strategy_type = kwargs.get('strategy_type', 'TWAP')
    algo_interval = kwargs.get('algo_interval', '45')
    volume_ratio = kwargs.get('volume_ratio', 0.1)
    
    cols = [
        '挂钩标的代码',
        '多空方向',
        '意向名义数量',
        '意向价格',
        # '保护限价',
        '算法选择',
        '价格方式',
        '交易意向类型',
        '开始时间',
        '结束时间',
        '时间间隔(秒)',
        '量占比(%)',
    ]
    
    new_orders = pd.DataFrame(columns=cols)
    new_orders['挂钩标的代码'] = changes['symbol'].apply(symbol_with_market)
    new_orders['多空方向'] = '看多'
    new_orders['意向名义数量'] = changes['order_shares'].abs()
    # new_orders['多空方向'] = changes['order_shares'].apply(lambda x: '看多' if x > 0 else '看空')
    new_orders['意向价格'] = '最新价'
    # new_orders['保护限价'] = ''
    new_orders['算法选择'] = strategy_type
    new_orders['价格方式'] = '限价'
    new_orders['交易意向类型'] = changes['order_shares'].apply(lambda x: '买' if x > 0 else '卖')
    new_orders['开始时间'] = start_time
    new_orders['结束时间'] = end_time
    new_orders['时间间隔(秒)'] = algo_interval
    new_orders['量占比(%)'] = volume_ratio
    
    new_cols = [
'''挂钩标的代码
(合约代码/合约代码.市场)''',

'''多空方向
(看多/看空)''',

'''意向名义数量
(整数类型)''',

'''意向价格
(限价-不使用算法:浮点数类型)

''',

'''算法选择
(不使用算法)''',

'''价格方式
(限价)''',

'''交易意向类型
(买/卖)''',

'''开始时间
(不使用算法:不填)
(TWAP/POV:文本时间格式,不得大于结束时间)''',

'''结束时间
(不使用算法:不填)
(TWAP/POV:文本时间格式,不得小于开始时间)''',

'''时间间隔(秒)
(不使用算法:不填)
(TWAP/POV:文本时间格式。
时间间隔不得大于"结束时间"-"开始时间"。
间隔时间不得超过999秒)''',

'''量占比(%)
(不使用算法/TWAP:不填)
(POV:浮点数类型。
量占比必须大于0小于100。)''',
    ]
    new_orders.columns = new_cols
    return new_orders

def convert_jinshida_file_orders(changes, **kwargs):
    cols = [
        '自定义编号',
        '产品编号',
        '组合编号',
        '挂钩标的代码',
        '多空方向',
        '意向名义数量',
        '意向价格',
        '保护限价',
        '价格方式',
        '交易意向类型',
    ]
    
def convert_guangfa_file_order(changes, **kwargs):
    cols = [
        '自定义编号',
        '产品编号',
        '组合编号',
        '标的代码',
        '多空方向',
        '意向名义数量',
        '意向价格',
        '交易意向类型',
        '算法方式',
        '价格限制',
        '开始时间',
        '结束时间',
        '涨跌停是否继续执行',
        '过期后是否继续执行',
    ]
    
    product_id = kwargs['product_id']
    port_id = kwargs['port_id']
    strategy_type = kwargs.get('strategy_type', 'vwap_plus')
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    # price_premium = kwargs.get('price_premium', 0)
    updown_limit_rule = kwargs.get('updown_limit_rule', '0')
    no_trade_expire = kwargs.get('no_trade_expire', '否')
    mkt_limit = kwargs['mkt_limit']
    pre_close = kwargs.get('pre_close')
    # print(pre_close)
    
    if mkt_limit.empty:
        # raise ValueError('mkt_limit is required')
        from decimal import Decimal
        def up_limit_ratio(symbol):
            symbol = str(symbol).zfill(6)
            if symbol.startswith('68') or symbol.startswith('30'):
                return 1.2
            else:
                return 1.1
        def down_limit_ratio(symbol):
            symbol = str(symbol).zfill(6)
            if symbol.startswith('68') or symbol.startswith('30'):
                return 0.8
            else:
                return 0.9
        pre_close['uplimit'] = (pre_close['close'] * pre_close['symbol'].apply(up_limit_ratio)).apply(lambda x: Decimal(str(x)).quantize(Decimal("0.01"), rounding = "ROUND_HALF_UP")).astype(float)
        pre_close['downlimit'] = (pre_close['close'] * pre_close['symbol'].apply(down_limit_ratio)).apply(lambda x: Decimal(str(x)).quantize(Decimal("0.01"), rounding = "ROUND_HALF_UP")).astype(float)
        mkt_limit = pre_close[['symbol', 'uplimit', 'downlimit']]
    
    
    changes = changes.merge(mkt_limit, on='symbol', how='left')
    changes.loc[changes['order_shares']>0, 'order_price'] = changes[changes['order_shares']>0]['uplimit']
    changes.loc[changes['order_shares']<0, 'order_price'] = changes[changes['order_shares']<0]['downlimit']
    
    
    new_orders = pd.DataFrame(columns=cols)
    new_orders['标的代码'] = changes['symbol'].apply(symbol_with_market)
    
    # new_orders['now'] = datetime.now().strftime('%Y%m%d')
    # new_orders['order_id'] = pd.Series(range(1, len(new_orders)+1)).apply(lambda x : str(x).zfill(5))
    # new_orders['自定义编号'] = new_orders['now'] + new_orders['order_id']

    new_orders['自定义编号'] = new_orders['自定义编号'].apply(lambda x: str(uuid.uuid4()))
    new_orders['产品编号'] = product_id
    new_orders['组合编号'] = port_id
    new_orders['多空方向'] = '看多'
    new_orders['意向名义数量'] = changes['order_shares'].abs()
    new_orders['意向价格'] = changes['order_price']
    new_orders['交易意向类型'] = changes['order_shares'].apply(lambda x: '买' if x > 0 else '卖')
    new_orders['算法方式'] = strategy_type
    new_orders['价格限制'] = '限价'
    new_orders['开始时间'] = pd.to_datetime(trading_date + ' ' + start_time).strftime('%H:%M:%S')
    new_orders['结束时间'] = pd.to_datetime(trading_date + ' ' + end_time).strftime('%H:%M:%S')
    new_orders['涨跌停是否继续执行'] = updown_limit_rule
    new_orders['过期后是否继续执行'] = no_trade_expire
    return new_orders[cols]

def convert_haitong_file_order(changes, **kwargs):
    cols = [
        '自定义编号',
        '自定义编号2',
        '客户编号',
        '合约编号',
        '挂钩标的代码',
        '多空方向',
        '意向名义数量',
        '意向价格',
        '交易意向类型',
        '价格方式',
        '开始时间',
        '结束时间',
        '量占比(%)',
        '涨跌停是否继续执行',
        '过期后是否继续执行',
        '投资备注',
    ]
    
    product_id = kwargs['product_id']
    port_id = kwargs['port_id']
    strategy_type = kwargs.get('strategy_type', 'kf_vwap_plus')
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    # price_premium = kwargs.get('price_premium', 0)
    participate_rate = kwargs.get('participate_rate', 5)
    updown_limit_rule = kwargs.get('updown_limit_rule', '否')
    no_trade_expire = kwargs.get('no_trade_expire', '否')
    # mkt_limit = kwargs['mkt_limit']
    
    # changes = changes.merge(mkt_limit, on='symbol', how='left')
    # changes.loc[changes['order_shares']>0, 'order_price'] = changes[changes['order_shares']>0]['uplimit']
    # changes.loc[changes['order_shares']<0, 'order_price'] = changes[changes['order_shares']<0]['downlimit']
    
    new_orders = pd.DataFrame(columns=cols)
    new_orders['挂钩标的代码'] = changes['symbol'].apply(symbol_with_market)
    
    new_orders['now'] = datetime.now().strftime('%H%M%S')
    new_orders['order_id'] = range(1, len(new_orders)+1)
    new_orders['自定义编号'] = new_orders['now'] + new_orders['order_id'].apply(lambda x:str(x).zfill(6))
    new_orders['自定义编号2'] = new_orders['自定义编号']
    new_orders['客户编号'] = product_id
    new_orders['合约编号'] = port_id
    new_orders['多空方向'] = '看多'
    new_orders['意向名义数量'] = changes['order_shares'].abs()
    new_orders['意向价格'] = ''
    new_orders['交易意向类型'] = changes['order_shares'].apply(lambda x: '开' if x > 0 else '平')
    new_orders['价格方式'] = strategy_type
    new_orders['价格限制'] = '限价'
    new_orders['开始时间'] = pd.to_datetime(trading_date + ' ' + start_time).strftime('%H:%M:%S')
    new_orders['结束时间'] = pd.to_datetime(trading_date + ' ' + end_time).strftime('%H:%M:%S')
    new_orders['量占比(%)'] = participate_rate
    new_orders['涨跌停是否继续执行'] = updown_limit_rule
    new_orders['过期后是否继续执行'] = no_trade_expire
    return new_orders[cols]

def convert_dfdma_kafang_orders(changes, **kwargs):
    cols = [
        '产品名称',
        '单元名称',
        '资产账户名称',
        '算法实例',
        '交易市',
        '证券代码',
        '母单数量',
        '母单方向',
        '交易日',
        '开始时间',
        '结束时间',
        '涨跌停是否交易',
        '过期是否交易',
        '其他参数',
    ]
    
    prdt_name = kwargs['prdt_name']
    unit_name = kwargs['unit_name']
    acc_name = kwargs['acc_name']
    strategy_instance = kwargs.get('strategy_instance', 'kf_vwap_core')
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    updown_limit_rule = kwargs.get('updown_limit_rule', '涨跌停终止交易')
    no_trade_expire = kwargs.get('no_trade_expire', '否')
    order_remark = kwargs.get('order_remark', '')
    
    new_orders = pd.DataFrame(columns=cols)
    new_orders['证券代码'] = changes['symbol'].apply(lambda x: str(x).zfill(6))
    new_orders['产品名称'] = prdt_name
    new_orders['单元名称'] = unit_name
    new_orders['资产账户名称'] = acc_name
    new_orders['算法实例'] = strategy_instance
    new_orders['交易市'] = new_orders['证券代码'].apply(symbol_to_exchange_cn)
    new_orders['母单数量'] = changes['order_shares'].abs()
    new_orders['母单方向'] = changes['order_shares'].apply(lambda x: '买入' if x > 0 else '卖出')
    new_orders['交易日'] = trading_date
    new_orders['开始时间'] = start_time
    new_orders['结束时间'] = end_time
    new_orders['涨跌停是否交易'] = updown_limit_rule
    new_orders['过期是否交易'] = no_trade_expire
    new_orders['其他参数'] = order_remark
    return new_orders
    
def convert_yinhedma_kafang_orders(changes, **kwargs):
    cols = [
        'strategy',
        'clientName',
        'orderType',
        'symbol',
        'orderQty',
        'side',
        'effectiveTime',
        'expireTime',
        'limitAction',
        'afterAction',
        'algoParam',
    ]
    
    clientName = kwargs['clientName']
    #  = kwargs['unit_name']
    # acc_name = kwargs['acc_name']
    strategy = kwargs.get('strategy', 'TWAP_CORE')
    orderType = kwargs.get('orderType', 'kf_twap_core')
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    limitAction = kwargs.get('limitAction', 0)
    afterAction = kwargs.get('afterAction', 0)
    algoParam = kwargs.get('algoParam', '')
    
    new_orders = pd.DataFrame(columns=cols)
    new_orders['symbol'] = changes['symbol'].apply(symbol_with_market)
    new_orders['orderQty'] = changes['order_shares'].abs()
    new_orders['strategy'] = strategy
    new_orders['clientName'] = clientName
    new_orders['orderType'] = orderType
    new_orders['side'] = changes['order_shares'].apply(lambda x: 1 if x > 0 else 2)  # 1是买入, 2是卖出
    new_orders['effectiveTime'] = '{}T{}000'.format(trading_date, start_time)
    new_orders['expireTime'] = '{}T{}000'.format(trading_date, end_time)
    new_orders['limitAction'] = limitAction
    new_orders['afterAction'] = afterAction
    new_orders['algoParam'] = algoParam
    return new_orders
    
# 招商卡方
def convert_zhaoshang_kafang_orders(changes, **kwargs):
    cols = [
        'strategy',
        'clientName',
        'orderType',
        'symbol',
        'orderQty',
        'side',
        'effectiveTime',
        'expireTime',
        'limitAction',
        'afterAction',
        'algoParam',
    ]
    
    clientName = kwargs['clientName']
    #  = kwargs['unit_name']
    # acc_name = kwargs['acc_name']
    orderType = kwargs.get('orderType', 'VWAP_Core')
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    limitAction = kwargs.get('limitAction', 0)
    afterAction = kwargs.get('afterAction', 0)
    algoParam = kwargs.get('algoParam', '')
    
    # changes = filter_small_order(changes)
   
    new_orders = pd.DataFrame(columns=cols)
    new_orders['symbol'] = changes['symbol'].apply(symbol_with_market)
    new_orders['orderQty'] = changes['order_shares'].abs()
    new_orders['strategy'] = 'APLUS'
    new_orders['clientName'] = clientName
    new_orders['orderType'] = orderType
    new_orders['side'] = changes['order_shares'].apply(lambda x: 'B' if x > 0 else 'S')  # B是买入, S是卖出
    new_orders['effectiveTime'] = '{}T{}000'.format(trading_date, start_time)
    new_orders['expireTime'] = '{}T{}000'.format(trading_date, end_time)
    new_orders['limitAction'] = limitAction
    new_orders['afterAction'] = afterAction
    new_orders['algoParam'] = algoParam
    return new_orders

# 招商交易大师 普通交易栏目
def convert_zhaoshang_orders(changes, **kwargs):
    cols = [
        '证券代码',
        '证券名称',
        '市场',
        '样本数量',
        '单位',
        '买卖方向',
        '权重',
        '指定价',
    ]
        
    # changes = filter_small_order(changes)
   
    new_orders = pd.DataFrame(columns=cols)
    new_orders['证券代码'] = changes['symbol'].apply(lambda x: str(x).zfill(6))
    new_orders['证券名称'] = ''
    new_orders['市场'] = new_orders['证券代码'].apply(symbol_to_exchange_cn)
    new_orders['样本数量'] = changes['order_shares'].abs()
    new_orders['单位'] = '股'
    new_orders['买卖方向'] = changes['order_shares'].apply(lambda x: '买入' if x > 0 else '卖出')
    new_orders['权重'] = 0.0
    new_orders['指定价'] = 0.0
    return new_orders
        
# 招商交易大师 衍生品交易栏目
def convert_zhaoshang_swap_orders(changes, **kwargs):
    cols = [
        '资金账号',
        '标的代码',
        '申请数量',
        '买卖方向',
        '起始时间',
        '截止时间',
        '算法类型',
        '节点',
    ]
    
    # changes = filter_small_order(changes)
    
    account_id = kwargs['account_id']
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '113000')
    tradingday = kwargs.get('tradingday', datetime.now().strftime('%Y%m%d'))
    start_time = pd.to_datetime(tradingday + ' ' + start_time).strftime('%H:%M:%S')
    end_time = pd.to_datetime(tradingday + ' ' + end_time).strftime('%H:%M:%S')
    strategy_type = kwargs.get('strategy_type', 'MISE-TWAP')
    node = kwargs.get('node', '')
        
    new_orders = pd.DataFrame(columns=cols)
    new_orders['标的代码'] = changes['symbol'].apply(symbol_to_swapcode)
    new_orders['资金账号'] = account_id
    new_orders['申请数量'] = changes['order_shares'].abs()
    new_orders['买卖方向'] = changes['order_shares'].apply(lambda x: '买开' if x > 0 else '卖平')
    new_orders['起始时间'] = start_time
    new_orders['截止时间'] = end_time
    new_orders['算法类型'] = strategy_type
    new_orders['节点'] = node
    
    return new_orders
    
    
# 浙商卡方, 基本和招商的卡方一样
def convert_kafang_algo_orders(changes, **kwargs):
    cols = [
        'strategy',
        'clientName',
        'orderType',
        'symbol',
        'orderQty',
        'side',
        'effectiveTime',
        'expireTime',
        'limitAction',
        'afterAction',
        'algoParam',
    ]
    
    if kwargs.get('sh_sz_double_mode', False) == True and kwargs.get('sh_clientname') is not None and kwargs.get('sz_clientname') is not None:
        pre_close = kwargs.get('pre_close', pd.DataFrame(columns=['symbol', 'close_date', 'close']))
        acc_name = kwargs.get('acc', 0)   # 账户名, 用于信息展示的 index
        
        sh_clientname = kwargs['sh_clientname']
        sz_clientname = kwargs['sz_clientname']
        
        # print(changes.loc[changes['direction'] == -1, :])
        changes['tmp_exchange'] = changes['symbol'].apply(symbol_to_exchange_cn)
        changes.loc[changes['tmp_exchange'] == '上交所', 'ClientName'] = sh_clientname
        changes.loc[changes['tmp_exchange'] == '深交所', 'ClientName'] = sz_clientname
        
        
        full_changes = changes
        # full_changes = pd.concat([changes_1, changes_2], axis=0, ignore_index=True)
        
        # merge pre_close and statistic data for trading info
        # ======================================================================
        full_changes = full_changes.merge(pre_close[['symbol', 'close']], on='symbol', how='left')
        # write_file(full_changes, file_type='xls', dest_type='dav', dest_path='full_changes.xls', index=False)
        
        tag_h1_buy = (full_changes['tmp_exchange'] == '上交所') & (full_changes['order_shares'] > 0)
        tag_h1_sell = (full_changes['tmp_exchange'] == '上交所') & (full_changes['order_shares'] < 0)
        tag_h2_buy = (full_changes['tmp_exchange'] == '深交所') & (full_changes['order_shares'] > 0)
        tag_h2_sell = (full_changes['tmp_exchange'] == '深交所') & (full_changes['order_shares'] < 0)

        
        h1_buy_value = (full_changes.loc[tag_h1_buy, 'order_shares'] * full_changes.loc[tag_h1_buy, 'close']).sum()
        h1_sell_value = (full_changes.loc[tag_h1_sell, 'order_shares'] * full_changes.loc[tag_h1_sell, 'close']).sum()
        h2_buy_value = (full_changes.loc[tag_h2_buy, 'order_shares'] * full_changes.loc[tag_h2_buy, 'close']).sum()
        h2_sell_value = (full_changes.loc[tag_h2_sell, 'order_shares'] * full_changes.loc[tag_h2_sell, 'close']).sum()
        
        info = {
            'sh_buy' : f'{h1_buy_value/10000:,.2f} 万',
            'sh_sell' : f'{h1_sell_value/10000:,.2f} 万',
            'sz_buy' : f'{h2_buy_value/10000:,.2f} 万',
            'sz_sell' : f'{h2_sell_value/10000:,.2f} 万',
            'sh_net' : f'{(h1_buy_value+h1_sell_value)/10000:,.2f} 万',
            'sz_net' : f'{(h2_buy_value+h2_sell_value)/10000:,.2f} 万',
        }
        logger.warning(f'{acc_name} 上海深圳双节点交易:')
        print('---------------------------')
        print(pd.DataFrame(info, index=[acc_name]))    
        print('---------------------------')
        # ======================================================================
    
    else:
        changes['ClientName'] = kwargs['ClientName']

    
    # clientName = kwargs['clientName']
    #  = kwargs['unit_name']
    # acc_name = kwargs['acc_name']
    orderType = kwargs.get('orderType', 'VWAP_Core')
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    limitAction = kwargs.get('limitAction', 0)
    afterAction = kwargs.get('afterAction', 0)
    algoParam = kwargs.get('algoParam', '')
    
    # changes = filter_small_order(changes)
   
    new_orders = pd.DataFrame(columns=cols)
    new_orders['symbol'] = changes['symbol'].apply(symbol_with_market)
    new_orders['orderQty'] = changes['order_shares'].abs()
    new_orders['strategy'] = 'APLUS'
    new_orders['clientName'] = changes['ClientName']
    new_orders['orderType'] = orderType 
    new_orders['side'] = changes['order_shares'].apply(lambda x: 'B' if x > 0 else 'S')  # b是买入, s是卖出
    new_orders['effectiveTime'] = '{}T{}000'.format(trading_date, start_time)
    new_orders['expireTime'] = '{}T{}000'.format(trading_date, end_time)
    new_orders['limitAction'] = limitAction
    new_orders['afterAction'] = afterAction
    new_orders['algoParam'] = algoParam
    return new_orders
    

def convert_kafang_algo_scan_orders(changes, **kwargs):
    cols = [
        'ExternalId',
        'ClientName',
        'Symbol',
        'Side',
        'OrderQty',
        'OrdType',
        'EffTime',
        'ExpTime',
        'LimAction',
        'AftAction',
        'AlgoParam',
    ]
    changes.reset_index(inplace=True, drop=True)
    changes['Side'] = 0
    changes.loc[(changes['direction'] == 1) & (changes['order_shares'] > 0), 'Side'] = 1
    changes.loc[(changes['direction'] == 1) & (changes['order_shares'] < 0), 'Side'] = 2
    
    algorithm = kwargs.get('algorithm', 'kf_vwap_core')
    if algorithm not in KF_ALGO_DICT:
        raise ValueError(f'algorithm {algorithm} not supported')

    if len(changes) > 0:
        if kwargs.get('secondary_account', False) == True and kwargs.get('secondary_account_clientname') is not None:
            short_sell_flag = kwargs['short_sell_flag']
            close_short_flag = kwargs['close_short_flag']
            
            # print(changes.loc[changes['direction'] == -1, :])
            changes.loc[changes['direction'] == -1, 'ClientName'] = kwargs['secondary_account_clientname']
            changes.loc[changes['direction'] == 1, 'ClientName'] = kwargs['ClientName']
            changes.loc[(changes['direction'] == -1) & (changes['order_shares'] > 0), 'Side'] = short_sell_flag
            changes.loc[(changes['direction'] == -1) & (changes['order_shares'] < 0), 'Side'] = close_short_flag
            
        elif kwargs.get('sh_sz_double_mode', False) == True and kwargs.get('sh_clientname') is not None and kwargs.get('sz_clientname') is not None:
            pre_close = kwargs.get('pre_close', pd.DataFrame(columns=['symbol', 'close_date', 'close']))
            acc_name = kwargs.get('acc', 0)   # 账户名, 用于信息展示的 index
            only_SH = kwargs.get('only_SH', False)
            only_SZ = kwargs.get('only_SZ', False)
            
            sh_clientname = kwargs['sh_clientname']
            sz_clientname = kwargs['sz_clientname']
            
            # print(changes.loc[changes['direction'] == -1, :])
            changes['tmp_exchange'] = changes['symbol'].apply(symbol_to_exchange_cn)
            changes.loc[changes['tmp_exchange'] == '上交所', 'ClientName'] = sh_clientname
            changes.loc[changes['tmp_exchange'] == '深交所', 'ClientName'] = sz_clientname
            
            if only_SH == True:
                changes = changes[changes['tmp_exchange'] == '上交所']
            if only_SZ == True:
                changes = changes[changes['tmp_exchange'] == '深交所']
            
            full_changes = changes
            # full_changes = pd.concat([changes_1, changes_2], axis=0, ignore_index=True)
            
            # merge pre_close and statistic data for trading info
            # ======================================================================
            full_changes = full_changes.merge(pre_close[['symbol', 'close']], on='symbol', how='left')
            # write_file(full_changes, file_type='xls', dest_type='dav', dest_path='full_changes.xls', index=False)
            
            tag_h1_buy = (full_changes['tmp_exchange'] == '上交所') & (full_changes['order_shares'] > 0)
            tag_h1_sell = (full_changes['tmp_exchange'] == '上交所') & (full_changes['order_shares'] < 0)
            tag_h2_buy = (full_changes['tmp_exchange'] == '深交所') & (full_changes['order_shares'] > 0)
            tag_h2_sell = (full_changes['tmp_exchange'] == '深交所') & (full_changes['order_shares'] < 0)

            
            h1_buy_value = (full_changes.loc[tag_h1_buy, 'order_shares'] * full_changes.loc[tag_h1_buy, 'close']).sum()
            h1_sell_value = (full_changes.loc[tag_h1_sell, 'order_shares'] * full_changes.loc[tag_h1_sell, 'close']).sum()
            h2_buy_value = (full_changes.loc[tag_h2_buy, 'order_shares'] * full_changes.loc[tag_h2_buy, 'close']).sum()
            h2_sell_value = (full_changes.loc[tag_h2_sell, 'order_shares'] * full_changes.loc[tag_h2_sell, 'close']).sum()
            
            info = {
                'sh_buy' : f'{h1_buy_value/10000:,.2f} 万',
                'sh_sell' : f'{h1_sell_value/10000:,.2f} 万',
                'sz_buy' : f'{h2_buy_value/10000:,.2f} 万',
                'sz_sell' : f'{h2_sell_value/10000:,.2f} 万',
                'sh_net' : f'{(h1_buy_value+h1_sell_value)/10000:,.2f} 万',
                'sz_net' : f'{(h2_buy_value+h2_sell_value)/10000:,.2f} 万',
            }
            logger.warning(f'{acc_name} 上海深圳双节点交易:')
            print('---------------------------')
            print(pd.DataFrame(info, index=[acc_name]))    
            print('---------------------------')
            # ======================================================================
        
        else:
            changes['ClientName'] = kwargs['ClientName']


        # ClientName = kwargs['ClientName']
        
        start_time = kwargs.get('start_time', '093000')
        end_time = kwargs.get('end_time', '103000')
        OrdType = KF_ALGO_DICT[algorithm]
        
        LimAction = kwargs.get('LimAction', 0)
        AftAction = kwargs.get('AftAction', 0)
        AlgoParam = kwargs.get('AlgoParam', {})         # 中文用;拼接, 英文用:拼接
        
        trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
        basket_id = kwargs.get('basket_id', datetime.now().strftime('%H%M%S')) + str(uuid.uuid4())[:4]
        AlgoParam.update({'basket_id': basket_id})
        AlgoParam = ':'.join([f'{key}={value}' for key, value in AlgoParam.items()])
        
        orders = pd.DataFrame(columns=cols)
        orders['Symbol'] = changes['symbol'].map(symbol_with_market)
        orders['Side'] = changes['Side']
        orders['OrderQty'] = changes['order_shares'].abs().astype(int)
        orders['ExternalId'] = [basket_id + '_' + str(i).zfill(3) for i in range(1, len(orders)+1)]
        orders['ClientName'] = changes['ClientName']
        orders['OrdType'] = OrdType
        orders['EffTime'] = '{}{}000'.format(trading_date, start_time)
        orders['ExpTime'] = '{}{}000'.format(trading_date, end_time)
        orders['LimAction'] = LimAction
        orders['AftAction'] = AftAction
        orders['AlgoParam'] = AlgoParam
        
    else:
        orders = pd.DataFrame(columns=cols)
    return orders
    
    
def convert_touyitong_scan_orders(changes, **kwargs):
    cols = [
        'local_group_no',
        'local_group_name',
        'local_report_no',
        'projectid',
        'market',
        'stkcode',
        'hedgeflag',
        'bsflag',
        'price',
        'qty',
        'price_mode',
        'price_type',
        'diy1',
        'diy2',
        'algo'
    ]

    changes.reset_index(inplace=True, drop=True)
    changes['Side'] = 0
    changes.loc[(changes['direction'] == 1) & (changes['order_shares'] > 0), 'Side'] = '0B'
    changes.loc[(changes['direction'] == 1) & (changes['order_shares'] < 0), 'Side'] = '0S'
    changes.loc[(changes['direction'] == -1) & (changes['order_shares'] > 0), 'Side'] = '1S'
    changes.loc[(changes['direction'] == -1) & (changes['order_shares'] < 0), 'Side'] = '1B'
    
    projectid = kwargs['projectid']
    algo_params = kwargs.get('algo_params', {})
    basket_id = kwargs.get('basket_id', datetime.now().strftime('%H%M%S')) + '_' + str(uuid.uuid4())[:4]
    # algo_type=1001|start_time=103000|end_time=150000|algo_price=0|limit_action=0|part_rate=0.3|min_amt=10000
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    algo_params.update({'start_time':start_time, 'end_time':end_time})
    algo_params = '|'.join([f'{key}={value}' for key, value in algo_params.items()])

    if len(changes) > 0:
        
        orders = pd.DataFrame(columns=cols)
        orders['stkcode'] = changes['symbol'].astype(str).str.zfill(6)
        orders['local_group_no'] = datetime.now().strftime('%H%M%S')
        orders['local_group_name'] = basket_id
        orders['local_report_no'] = pd.Series(range(1, len(orders)+1), index=orders.index)
        orders['local_report_no'] = orders['local_group_no'] + '-' + orders['local_report_no'].astype(str)
        orders['projectid'] = projectid
        orders['market'] = changes['symbol'].map(symbol_to_exchange_cn).map({'上交所':'1', '深交所':'0', '北交所':'9'})
        orders['hedgeflag'] = '0'
        orders['bsflag'] = changes['Side']
        orders['price'] = ''
        orders['qty'] = changes['order_shares'].abs().astype(int)
        orders['price_mode'] = ''    # '1': '市价'  '2': '限价' 3': '跟盘价
        orders['price_type'] = ''
        orders['diy1'] = ''
        orders['diy2'] = ''
        orders['algo'] = algo_params
        
    else:
        orders = pd.DataFrame(columns=cols)
    return orders
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
def convert_zhongxin_cats_orders(changes, **kwargs):
    cols = [
        '交易账户类型', 
        '交易账户', 
        '标的', 
        '目标数量', 
        '交易方向', 
        '算法类型', 
        '算法参数',
    ]
    hold = kwargs['hold'].copy()
    target = kwargs['target'].copy()
    recall = kwargs.get('recall', pd.DataFrame())
    pre_close = kwargs.get('pre_close', pd.DataFrame(columns=['symbol', 'close_date', 'close']))
    acc_name = kwargs.get('acc', 0)
    # print(kwargs)
    
    tradingaccount_type = kwargs['tradingaccount_type']
    tradingaccount = kwargs['tradingaccount']
    algorithm = kwargs.get('algorithm', 'SmartVWAP3')
    
    algorithm_params = kwargs.get('algorithm_params', {})
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    algorithm_params.update({'beginTime':start_time, 'endTime':end_time})
    # if algorithm == 'VWAP':
    #     beginTime = kwargs.get('start_time', '093000')
    #     endTime = kwargs.get('end_time', '100000')
    #     limitPrice = kwargs.get('limitPrice', 0)
    #     participateRate = kwargs.get('participateRate', 0.0)
    #     tradingStyle = kwargs.get('tradingStyle', 1)
    #     algorithm_params.update({
    #         'beginTime': beginTime,
    #         'endTime'  : endTime,
    #         'limitPrice': limitPrice,
    #         'participateRate': participateRate,
    #         'tradingStyle': tradingStyle,
    #     })
    algorithm_params = ';'.join([f'{key}={value}' for key, value in algorithm_params.items()])
    # print('zhongxin_cats 算法参数: ', algorithm_params)

    # 新逻辑, 包含多空策略, 调整多头, 空头数量
    hold['longshort'] = hold['hold_shares'].apply(lambda x: 'long' if x > 0 else 'short')
    hold['hold_shares'] = hold['hold_shares'].abs()
    target['longshort'] = target['target_shares'].apply(lambda x: 'long' if x > 0 else 'short')
    target['target_shares'] = target['target_shares'].abs()

    
    from .hold_position_to_orders import adjust_changes
    merged_hold_target = pd.concat([hold.set_index(['symbol', 'longshort']), target.set_index(['symbol', 'longshort'])], axis=1, join='outer')
    merged_hold_target = merged_hold_target.fillna(0)
    merged_hold_target = pd.merge(merged_hold_target.reset_index(), pre_close[['symbol', 'close']], on='symbol', how='left')
    
    # merged_hold_target = merged_hold_target.set_index(['symbol', 'longshort'])
    # print(merged_hold_target)
    
    longside = merged_hold_target['longshort'] == 'long'
    shortside = merged_hold_target['longshort'] == 'short'
    
    # info, hold and target, 因为下面会调整target数据, 所以target市值要提前计算
    hold_long_value = (merged_hold_target.loc[longside, 'hold_shares'] * merged_hold_target.loc[longside, 'close']).sum()
    hold_short_value = (merged_hold_target.loc[shortside, 'hold_shares'] * merged_hold_target.loc[shortside, 'close']).sum()
    target_long_value = (merged_hold_target.loc[longside, 'target_shares'] * merged_hold_target.loc[longside, 'close']).sum()
    target_short_value = (merged_hold_target.loc[shortside, 'target_shares'] * merged_hold_target.loc[shortside, 'close']).sum()
    # ========
    
    # # 调整计入 场外recall 下单 
    # if tradingaccount_type=='SZQFEQD' and isinstance(recall, pd.DataFrame) and not recall.empty:
    #     recall['longshort'] = recall['trade_type'].map(lambda x: 'long' if x in [10, 20, '10', '20'] else 'short') 
    #     recall['recall_order'] = recall['trade_type'].map(lambda x: 1 if x in [10, 30, '10', '30'] else -1) * recall['recall_volume']  # 开仓是正
    #     merged_hold_target = pd.concat([merged_hold_target.set_index(['symbol', 'longshort']), recall.set_index(['symbol', 'longshort'])], axis=1, join='outer')
    #     merged_hold_target = merged_hold_target.fillna(0)
    #     merged_hold_target['target_shares'] = merged_hold_target['target_shares'] - merged_hold_target['recall_order']
    
    merged_hold_target['order_shares'] = merged_hold_target['target_shares'] - merged_hold_target['hold_shares']
    merged_hold_target.reset_index(inplace=True)
    merged_hold_target = merged_hold_target.apply(adjust_changes, axis=1)
    merged_hold_target.loc[longside, 'BS_flag'] = merged_hold_target.loc[longside, 'order_shares'].apply(lambda x: '1' if x > 0 else '2')
    if tradingaccount_type == 'SZQFEQD':
        merged_hold_target.loc[shortside, 'BS_flag'] = merged_hold_target.loc[shortside, 'order_shares'].apply(lambda x: 'E30' if x > 0 else 'E40')
    elif tradingaccount_type in ['C', 'SHHTSC', 'SZHTSC']:  # 两融 融券卖出, 买券还券
        merged_hold_target.loc[shortside, 'BS_flag'] = merged_hold_target.loc[shortside, 'order_shares'].apply(lambda x: 'B' if x > 0 else 'C')
    else:
        raise ValueError(f'error tradingaccount_type: {tradingaccount_type}') 
        
    
    # 统计order信息
    # info of order
    long_buy_value = (merged_hold_target.loc[longside & (merged_hold_target['order_shares'] > 0), 'order_shares'] * merged_hold_target.loc[longside & (merged_hold_target['order_shares'] > 0), 'close']).sum()    
    long_sell_value = (merged_hold_target.loc[longside & (merged_hold_target['order_shares'] < 0), 'order_shares'] * merged_hold_target.loc[longside & (merged_hold_target['order_shares'] < 0), 'close']).sum()
    short_open_value = (merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] > 0), 'order_shares'] * merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] > 0), 'close']).sum()
    short_close_value = (merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] < 0), 'order_shares'] * merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] < 0), 'close']).sum()
    info = {
        '多头持仓市值' : f'{hold_long_value/10000:,.2f} 万',
        '空头持仓市值' : f'{hold_short_value/10000:,.2f} 万',
        '多头目标市值' : f'{target_long_value/10000:,.2f} 万',
        '空头目标市值' : f'{target_short_value/10000:,.2f} 万',
        '多头买入' : f'{long_buy_value/10000:,.2f} 万',
        '多头卖出' : f'{long_sell_value/10000:,.2f} 万',
        '空头开仓' : f'{short_open_value/10000:,.2f} 万',
        '空头平仓' : f'{short_close_value/10000:,.2f} 万',
        '多头买入%' : f'{long_buy_value / hold_long_value:.2%}',
        '多头卖出%' : f'{long_sell_value / hold_long_value:.2%}',
        '空头开仓%' : f'{short_open_value / hold_short_value:.2%}',
        '空头平仓%' : f'{short_close_value / hold_short_value:.2%}',
    }
    print('---------------------------')
    print(pd.DataFrame(info, index=[acc_name]))    
    print('---------------------------')
    # ==========================
    
    # transfer to order file
    merged_hold_target = merged_hold_target[merged_hold_target['order_shares'] != 0]
    
    # 包括多空的order
    orders = pd.DataFrame(columns=cols)
    orders['标的'] = merged_hold_target['symbol'].apply(symbol_with_market)
    orders['目标数量'] = merged_hold_target['order_shares'].abs().astype(int)
    orders['交易方向'] = merged_hold_target['BS_flag']
    orders['交易账户类型'] = tradingaccount_type
    orders['交易账户'] = tradingaccount
    orders['算法类型'] = algorithm
    orders['算法参数'] = algorithm_params

    # return new_orders
    return orders

def bak_old_convert_cats_generic_orders(changes, **kwargs):
    cols = [
        '客户号',
        '交易账户类型', 
        '交易账户', 
        '标的', 
        '目标数量', 
        '交易方向', 
        '指令类型', 
        '指令参数',
    ]
    hold = kwargs['hold'].copy()
    target = kwargs['target'].copy()
    recall = kwargs.get('recall', pd.DataFrame())
    pre_close = kwargs.get('pre_close', pd.DataFrame(columns=['symbol', 'close_date', 'close']))
    acc_name = kwargs.get('acc', 0)   # 账户名, 用于信息展示的 index
    # print(kwargs)  
    
    tradingaccount_type = kwargs['tradingaccount_type']
    tradingaccount = kwargs['tradingaccount']
    algorithm = kwargs.get('algorithm', 'SmartVWAP3')
    
    algorithm_params = kwargs.get('algorithm_params', {})
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    algorithm_params.update({'AlgoType':algorithm, 'beginTime':start_time, 'endTime':end_time})
    # if algorithm == 'VWAP':
    #     beginTime = kwargs.get('start_time', '093000')
    #     endTime = kwargs.get('end_time', '100000')
    #     limitPrice = kwargs.get('limitPrice', 0)
    #     participateRate = kwargs.get('participateRate', 0.0)
    #     tradingStyle = kwargs.get('tradingStyle', 1)
    #     algorithm_params.update({
    #         'beginTime': beginTime,
    #         'endTime'  : endTime,
    #         'limitPrice': limitPrice,
    #         'participateRate': participateRate,
    #         'tradingStyle': tradingStyle,
    #     })
    algorithm_params = ';'.join([f'{key}={value}' for key, value in algorithm_params.items()])
    # print('zhongxin_cats 算法参数: ', algorithm_params)

    # 新逻辑, 包含多空策略, 调整多头, 空头数量
    hold['longshort'] = hold['hold_shares'].apply(lambda x: 'long' if x > 0 else 'short')
    hold['hold_shares'] = hold['hold_shares'].abs()
    target['longshort'] = target['target_shares'].apply(lambda x: 'long' if x > 0 else 'short')
    target['target_shares'] = target['target_shares'].abs()

    from .hold_position_to_orders import adjust_changes
    merged_hold_target = pd.concat([hold.set_index(['symbol', 'longshort']), target.set_index(['symbol', 'longshort'])], axis=1, join='outer')
    
    # 调整计入 场外recall 下单 
    # hold 要减去 recall中 (recall_volume - deal_volume) 的数量, 避免recall 还在交易中, hold还没有完全反映
    if tradingaccount_type=='SZQFEQD' and isinstance(recall, pd.DataFrame) and not recall.empty:
        recall['longshort'] = recall['trade_type'].map(lambda x: 'long' if x in [10, 20, '10', '20'] else 'short') 
        recall['recall_order'] = recall['trade_type'].map(lambda x: 1 if x in [10, 30, '10', '30'] else -1) * (recall['recall_order_volume'] - recall['recall_deal_volume'])  # 开仓是正, 剩余还没交易的部分
        merged_hold_target = pd.concat([merged_hold_target, recall.set_index(['symbol', 'longshort'])], axis=1, join='outer')
        # merged_hold_target = merged_hold_target.fillna(0)
        # merged_hold_target['target_shares'] = merged_hold_target['target_shares'] - merged_hold_target['recall_order']
    else:
        merged_hold_target['recall_order'] = 0
        
    # merged_hold_target = merged_hold_target.set_index(['symbol', 'longshort'])
    # print(merged_hold_target)
    
    merged_hold_target.fillna(0, inplace=True)
    merged_hold_target = pd.merge(merged_hold_target.reset_index(), pre_close[['symbol', 'close']], on='symbol', how='left')
    longside = merged_hold_target['longshort'] == 'long'
    shortside = merged_hold_target['longshort'] == 'short'
    
    # info, hold and target, 因为下面会调整target数据, 所以target市值要提前计算
    hold_long_value = (merged_hold_target.loc[longside, 'hold_shares'] * merged_hold_target.loc[longside, 'close']).sum()
    hold_short_value = (merged_hold_target.loc[shortside, 'hold_shares'] * merged_hold_target.loc[shortside, 'close']).sum()
    target_long_value = (merged_hold_target.loc[longside, 'target_shares'] * merged_hold_target.loc[longside, 'close']).sum()
    target_short_value = (merged_hold_target.loc[shortside, 'target_shares'] * merged_hold_target.loc[shortside, 'close']).sum()
    # ========
    


    merged_hold_target['order_shares'] = merged_hold_target['target_shares'] - (merged_hold_target['hold_shares'] - merged_hold_target['recall_order'])
    # merged_hold_target.reset_index(inplace=True)
    merged_hold_target = merged_hold_target.apply(adjust_changes, axis=1)
    merged_hold_target.loc[longside, 'BS_flag'] = merged_hold_target.loc[longside, 'order_shares'].apply(lambda x: '1' if x > 0 else '2')
    if len(merged_hold_target.loc[shortside]) > 0:
        print(merged_hold_target.loc[shortside])
        if tradingaccount_type == 'SZQFEQD':
            merged_hold_target.loc[shortside, 'BS_flag'] = merged_hold_target.loc[shortside, 'order_shares'].apply(lambda x: 'E30' if x > 0 else 'E40')
        elif tradingaccount_type in ['C', 'SHHTSC', 'SZHTSC']:  # 两融 融券卖出, 买券还券
            merged_hold_target.loc[shortside, 'BS_flag'] = merged_hold_target.loc[shortside, 'order_shares'].apply(lambda x: 'B' if x > 0 else 'C')
        else:
            raise ValueError(f'error tradingaccount_type: {tradingaccount_type}') 
        
    

    # 统计order信息
    # info of order
    long_buy_value = (merged_hold_target.loc[longside & (merged_hold_target['order_shares'] > 0), 'order_shares'] * merged_hold_target.loc[longside & (merged_hold_target['order_shares'] > 0), 'close']).sum()    
    long_sell_value = (merged_hold_target.loc[longside & (merged_hold_target['order_shares'] < 0), 'order_shares'] * merged_hold_target.loc[longside & (merged_hold_target['order_shares'] < 0), 'close']).sum()
    short_open_value = (merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] > 0), 'order_shares'] * merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] > 0), 'close']).sum()
    short_close_value = (merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] < 0), 'order_shares'] * merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] < 0), 'close']).sum()
    info = {
        '多头持仓市值' : f'{hold_long_value/10000:,.2f} 万',
        '空头持仓市值' : f'{hold_short_value/10000:,.2f} 万',
        '多头目标市值' : f'{target_long_value/10000:,.2f} 万',
        '空头目标市值' : f'{target_short_value/10000:,.2f} 万',
        '多头买入' : f'{long_buy_value/10000:,.2f} 万',
        '多头卖出' : f'{long_sell_value/10000:,.2f} 万',
        '空头开仓' : f'{short_open_value/10000:,.2f} 万',
        '空头平仓' : f'{short_close_value/10000:,.2f} 万',
        '多头买入%' : f'{long_buy_value / hold_long_value:.2%}',
        '多头卖出%' : f'{long_sell_value / hold_long_value:.2%}',
        '空头开仓%' : f'{short_open_value / hold_short_value:.2%}',
        '空头平仓%' : f'{short_close_value / hold_short_value:.2%}',
    }
    print('---------------------------')
    print(pd.DataFrame(info, index=[acc_name]))    
    print('---------------------------')
    # ==========================
    
    # transfer to order file
    merged_hold_target = merged_hold_target[merged_hold_target['order_shares'] != 0]
    
    # 包括多空的order
    orders = pd.DataFrame(columns=cols)
    orders['标的'] = merged_hold_target['symbol'].apply(symbol_with_market)
    orders['目标数量'] = merged_hold_target['order_shares'].abs().astype(int)
    orders['交易方向'] = merged_hold_target['BS_flag']
    orders['交易账户类型'] = tradingaccount_type
    orders['交易账户'] = tradingaccount
    orders['指令类型'] = 'algo'   # algo 算法委托   sp 扫盘买卖   singleorder 普通委托
    orders['指令参数'] = algorithm_params
    orders['客户号'] = pd.Series(range(1, len(orders)+1), index=orders.index).apply(lambda x : 'id_' + str(x).zfill(4))

    # return new_orders
    return orders[cols]


def convert_cats_generic_orders(changes, **kwargs):
    cols = [
        '客户号',
        '交易账户类型', 
        '交易账户', 
        '标的', 
        '目标数量', 
        '交易方向', 
        '指令类型', 
        '指令参数',
    ]
    # hold = kwargs['hold'].copy()
    # target = kwargs['target'].copy()
    recall = kwargs.get('recall', pd.DataFrame())
    # pre_close = kwargs.get('pre_close', pd.DataFrame(columns=['symbol', 'close_date', 'close']))
    # acc_name = kwargs.get('acc', 0)   # 账户名, 用于信息展示的 index
    # print(kwargs)  
    
    tradingaccount_type = kwargs['tradingaccount_type']
    tradingaccount = kwargs['tradingaccount']
    algorithm = kwargs.get('algorithm', 'SmartVWAP3')
    
    algorithm_params = kwargs.get('algorithm_params', {})
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    algorithm_params.update({'AlgoType':algorithm, 'beginTime':start_time, 'endTime':end_time})

    algorithm_params = ';'.join([f'{key}={value}' for key, value in algorithm_params.items()])
    # print('zhongxin_cats 算法参数: ', algorithm_params)

    # 新逻辑, 包含多空策略, 调整多头, 空头数量
    # hold['longshort'] = hold['hold_shares'].apply(lambda x: 'long' if x > 0 else 'short')
    # hold['hold_shares'] = hold['hold_shares'].abs()
    # target['longshort'] = target['target_shares'].apply(lambda x: 'long' if x > 0 else 'short')
    # target['target_shares'] = target['target_shares'].abs()

    # from .hold_position_to_orders import adjust_changes
    # merged_hold_target = pd.concat([hold.set_index(['symbol', 'longshort']), target.set_index(['symbol', 'longshort'])], axis=1, join='outer')
    
    
    merged_hold_target = changes.set_index(['symbol', 'direction'])
    # 调整计入 场外recall 下单 
    # hold 要减去 recall中 (recall_volume - deal_volume) 的数量, 避免recall 还在交易中, hold还没有完全反映
    if tradingaccount_type=='SZQFEQD' and isinstance(recall, pd.DataFrame) and not recall.empty:
        print(recall)
        recall['direction'] = recall['trade_type'].map(lambda x: 1 if x in [10, 20, '10', '20'] else -1) 
        recall['recall_order'] = recall['trade_type'].map(lambda x: 1 if x in [10, 30, '10', '30'] else -1) * (recall['recall_order_volume'] - recall['recall_deal_volume'])  # 开仓是正, 剩余还没交易的部分
        merged_hold_target = pd.concat([merged_hold_target, recall.set_index(['symbol', 'direction'])], axis=1, join='outer')
        # merged_hold_target = merged_hold_target.fillna(0)
        # merged_hold_target['target_shares'] = merged_hold_target['target_shares'] - merged_hold_target['recall_order']
    else:
        merged_hold_target['recall_order'] = 0
        
    # merged_hold_target = merged_hold_target.set_index(['symbol', 'longshort'])
    # print(merged_hold_target)
    
    merged_hold_target = merged_hold_target.reset_index().fillna(0)
    # merged_hold_target = pd.merge(merged_hold_target.reset_index(), pre_close[['symbol', 'close']], on='symbol', how='left')
    longside = merged_hold_target['direction'] == 1
    shortside = merged_hold_target['direction'] == -1
    
    # info, hold and target, 因为下面会调整target数据, 所以target市值要提前计算
    # hold_long_value = (merged_hold_target.loc[longside, 'hold_shares'] * merged_hold_target.loc[longside, 'close']).sum()
    # hold_short_value = (merged_hold_target.loc[shortside, 'hold_shares'] * merged_hold_target.loc[shortside, 'close']).sum()
    # target_long_value = (merged_hold_target.loc[longside, 'target_shares'] * merged_hold_target.loc[longside, 'close']).sum()
    # target_short_value = (merged_hold_target.loc[shortside, 'target_shares'] * merged_hold_target.loc[shortside, 'close']).sum()
    # ========
    


    merged_hold_target['order_shares'] = merged_hold_target['order_shares'] - merged_hold_target['recall_order']
    # merged_hold_target.reset_index(inplace=True)
    # merged_hold_target = merged_hold_target.apply(adjust_changes, axis=1)
    
    merged_hold_target.loc[longside, 'BS_flag'] = merged_hold_target.loc[longside, 'order_shares'].apply(lambda x: '1' if x > 0 else '2')
    if len(merged_hold_target.loc[shortside]) > 0:
        # print(merged_hold_target.loc[shortside])
        if tradingaccount_type == 'SZQFEQD':
            merged_hold_target.loc[shortside, 'BS_flag'] = merged_hold_target.loc[shortside, 'order_shares'].apply(lambda x: 'E30' if x > 0 else 'E40')
        elif tradingaccount_type in ['C', 'SHHTSC', 'SZHTSC']:  # 两融 融券卖出, 买券还券
            merged_hold_target.loc[shortside, 'BS_flag'] = merged_hold_target.loc[shortside, 'order_shares'].apply(lambda x: 'B' if x > 0 else 'C')
        else:
            raise ValueError(f'error tradingaccount_type: {tradingaccount_type}') 
        
    

    # 统计order信息
    # info of order
    # long_buy_value = (merged_hold_target.loc[longside & (merged_hold_target['order_shares'] > 0), 'order_shares'] * merged_hold_target.loc[longside & (merged_hold_target['order_shares'] > 0), 'close']).sum()    
    # long_sell_value = (merged_hold_target.loc[longside & (merged_hold_target['order_shares'] < 0), 'order_shares'] * merged_hold_target.loc[longside & (merged_hold_target['order_shares'] < 0), 'close']).sum()
    # short_open_value = (merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] > 0), 'order_shares'] * merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] > 0), 'close']).sum()
    # short_close_value = (merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] < 0), 'order_shares'] * merged_hold_target.loc[shortside & (merged_hold_target['order_shares'] < 0), 'close']).sum()
    # info = {
    #     '多头持仓市值' : f'{hold_long_value/10000:,.2f} 万',
    #     '空头持仓市值' : f'{hold_short_value/10000:,.2f} 万',
    #     '多头目标市值' : f'{target_long_value/10000:,.2f} 万',
    #     '空头目标市值' : f'{target_short_value/10000:,.2f} 万',
    #     '多头买入' : f'{long_buy_value/10000:,.2f} 万',
    #     '多头卖出' : f'{long_sell_value/10000:,.2f} 万',
    #     '空头开仓' : f'{short_open_value/10000:,.2f} 万',
    #     '空头平仓' : f'{short_close_value/10000:,.2f} 万',
    #     '多头买入%' : f'{long_buy_value / hold_long_value:.2%}',
    #     '多头卖出%' : f'{long_sell_value / hold_long_value:.2%}',
    #     '空头开仓%' : f'{short_open_value / hold_short_value:.2%}',
    #     '空头平仓%' : f'{short_close_value / hold_short_value:.2%}',
    # }
    # print('---------------------------')
    # print(pd.DataFrame(info, index=[acc_name]))    
    # print('---------------------------')
    # ==========================
    
    # transfer to order file
    merged_hold_target = merged_hold_target[merged_hold_target['order_shares'] != 0]
    
    # 包括多空的order
    orders = pd.DataFrame(columns=cols)
    orders['标的'] = merged_hold_target['symbol'].apply(symbol_with_market)
    orders['目标数量'] = merged_hold_target['order_shares'].abs().astype(int)
    orders['交易方向'] = merged_hold_target['BS_flag']
    orders['交易账户类型'] = tradingaccount_type
    orders['交易账户'] = tradingaccount
    orders['指令类型'] = 'algo'   # algo 算法委托   sp 扫盘买卖   singleorder 普通委托
    orders['指令参数'] = algorithm_params
    orders['客户号'] = pd.Series(range(1, len(orders)+1), index=orders.index).apply(lambda x : 'id_' + str(x).zfill(4))

    # return new_orders
    return orders[cols]

    
    
def convert_cats_generic_t0_orders(t0_volume:pd.DataFrame, **kwargs):
    cols = [
        '客户号',
        '交易账户类型', 
        '交易账户', 
        '标的', 
        '目标数量', 
        '交易方向', 
        '指令类型', 
        '指令参数',
    ]

    tradingaccount_type = kwargs['tradingaccount_type']
    tradingaccount = kwargs['tradingaccount']
    algorithm = kwargs.get('algorithm', 'KF_DTrade_Plus')
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '145500')
    arg_remark = kwargs.get('arg_remark')
    arg_extra_param = kwargs.get('arg_extra_param')
    
    algorithm_params = {}

    algorithm_params.update({'AlgoType':algorithm, 'startTime':start_time, 'endTime':end_time,
                             'buyType':1,
                             'sellType':2,
                            #  'remark':'',
                            #  'extraParam':'',
                             })

    if arg_remark is not None:
        algorithm_params.update({'remark':arg_remark})

    if arg_extra_param is not None:
        algorithm_params.update({'extraParam':arg_extra_param})

    t0_volume = t0_volume.copy()
    t0_volume['ticker'] = t0_volume['ticker'].apply(symbol_with_market).apply(lambda x: 'symbol_' + x)
    t0_volume.set_index('ticker', inplace=True)
    # print(t0_volume)
    arg_t0_volume = t0_volume['t0_volume'].to_dict()
    # print(arg_t0_volume)
    
    algorithm_params.update(arg_t0_volume)
    algorithm_params = ';'.join([f'{key}={value}' for key, value in algorithm_params.items()])
    # print('zhongxin_cats 算法参数: ', algorithm_params)

    # 包括多空的order
    orders = pd.DataFrame(columns=cols)
    orders.loc[0, '客户号'] = algorithm + str(len(t0_volume)) + datetime.now().strftime('%M%S%f')
    # pd.Series(range(1, len(orders)+1), index=orders.index).apply(lambda x : 'id_' + str(x).zfill(4))
    orders['交易账户类型'] = tradingaccount_type
    orders['交易账户'] = tradingaccount
    orders['标的'] = ''
    orders['目标数量'] = ''
    orders['交易方向'] = ''
    orders['指令类型'] = 'dtrade'   # algo 算法委托   sp 扫盘买卖   singleorder 普通委托
    orders['指令参数'] = algorithm_params

    # return new_orders
    return orders[cols]

    

def convert_dfemc_basket_orders(changes, **kwargs):
    cols = [
        'fundID',
        'algoType',
        'stkCode',
        'market',
        'direction',
        'orderQty',
        'beginTime',
        'endTime',
        'limitAction',
        'afterAction',
        'algoParam',
    ]
    fundID = kwargs['fundID']
    algoType = kwargs.get('algoType', 'KFTWAPCORE')
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    algoParam = kwargs.get('algoParam', {})
    limitAction = algoParam.get('limit_action', 0)
    afterAction = algoParam.get('after_action', 0)
    algoTag = kwargs.get('algoTag')
    if algoTag is None and 'KF' in algoType:
        algoTag = 'KF_ATGO'
    elif algoTag is None and 'HX' in algoType:
        algoTag = 'HX_SMART'
    
    algoParam = ':'.join([f'{key}={value}' for key, value in algoParam.items()])
    
    orders = pd.DataFrame(columns=cols)
    orders['stkCode'] = changes['symbol'].map(lambda x: str(x).zfill(6))
    orders['fundID'] = fundID
    orders['algoType'] = algoType
    orders['market'] = orders['stkCode'].map(symbol_to_exchange_cn)
    orders['market'] = orders['market'].map(lambda x: 0 if x == '深交所' else 1)
    orders['direction'] = changes['order_shares'].apply(lambda x: 1 if x > 0 else 2)
    orders['orderQty'] = changes['order_shares'].abs()
    orders['beginTime'] = start_time[0:2]+':'+start_time[2:4]+':'+start_time[4:6]
    orders['endTime'] = end_time[0:2]+':'+end_time[2:4]+':'+end_time[4:6]
    orders['limitAction'] = limitAction
    orders['afterAction'] = afterAction if 'KF' in algoType else ''
    orders[algoTag] = ''
    return orders
    

def convert_dfemc_sweep_orders(changes, **kwargs):
    cols = [
        'fundID',
        'algoType',
        'stkCode',
        'market',
        'direction',
        'orderQty',
        'orderPrice',
        'beginTime',
        'endTime',
        'customerOrderRef',
        'algoParam',
        'algoParam2'
    ]
    fundID = kwargs['fundID']
    algoType = kwargs.get('algoType', 'KFTWAPCORE')
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    algoParam = kwargs.get('algoParam', {})
    # limitAction = kwargs.get('limitAction', 0)
    # afterAction = kwargs.get('afterAction', 0)
    algoTag = kwargs.get('algoTag')
    if algoTag is None and 'KF' in algoType:
        algoTag = 'KF_ATGO'
    elif algoTag is None and 'HX' in algoType:
        algoTag = 'HX_SMART'
    
    algoParam = ':'.join([f'{key}={value}' for key, value in algoParam.items()])
    
    orders = pd.DataFrame(columns=cols)
    orders['stkCode'] = changes['symbol'].map(lambda x: str(x).zfill(6))
    orders['fundID'] = fundID
    orders['algoType'] = algoType
    orders['market'] = orders['stkCode'].map(symbol_to_exchange_cn)
    orders['market'] = orders['market'].map(lambda x: 0 if x == '深交所' else 1)
    orders['direction'] = changes['order_shares'].apply(lambda x: 1 if x > 0 else 2)
    orders['orderQty'] = changes['order_shares'].abs()
    orders['orderPrice'] = ''
    orders['beginTime'] = start_time[0:2]+':'+start_time[2:4]+':'+start_time[4:6]
    orders['endTime'] = end_time[0:2]+':'+end_time[2:4]+':'+end_time[4:6]
    orders = orders.reset_index(drop=True)
    orders['customerOrderRef'] = orders['customerOrderRef'].apply(lambda x: str(uuid.uuid4()))
    # pd.Series(range(1, len(orders)+1)).apply(lambda x : datetime.now().strftime('%Y%m%d')+str(x).zfill(5))
    orders['algoParam'] = algoParam
    orders['algoParam2'] = ''
    orders[algoTag] = ''
    return orders
    
def convert_matic_kafangalgo_orders(changes, **kwargs):
    cols = [
        '证券代码',
        '证券名称',
        '市场',
        '委托数量',
        '限价',
        '交易方向',
        '算法开始时间',
        '算法过期时间',
        '委托属性',
        '涨幅限制',
        '跌幅限制',
        '涨跌停是否继续交易',
        '过期后是否继续交易',
        '备注',
    ]
    pass
    # fundID = kwargs['fundID']
    # algoType = kwargs.get('algoType', 'KFTWAPCORE')
    # start_time = kwargs.get('start_time', '093000')
    # end_time = kwargs.get('end_time', '100000')
    # algoParam = kwargs.get('algoParam', {})
    # limitAction = kwargs.get('limitAction', 0)
    # afterAction = kwargs.get('afterAction', 0)
    # algoTag = kwargs.get('algoTag')
    # if algoTag is None and 'KF' in algoType:
    #     algoTag = 'KF_ATGO'
    # elif algoTag is None and 'HX' in algoType:


def convert_matic_otc_orders(changes, **kwargs):
    cols = [
        '序号',
        '资金账号',
        '证券代码',
        '买卖方向',
        '委托价格',
        '委托数量',
        '委托金额',
        '金额币种',
        '价格类型',
        '算法类型',
        '算法生效时间',
        '算法失效时间',
        'Participation Rate(%)',
        '子单规模',
        '算法参数',
        '交易风格',
        '涨停不卖跌停不买',
        '下单模式',
        '交易时段',
    ]
    fund_account = kwargs['fund_account']
    algorithm = kwargs.get('algorithm', 'VWAP')
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '100000')
    participate_rate = kwargs.get('participaterate', 10)
    trading_zone = kwargs.get('trading_zone', 'Regular')  # Regular, PreOpen
    
    date = datetime.now().strftime('%Y%m%d')
    start_time = pd.to_datetime(date + ' ' + start_time).strftime('%H:%M:%S')
    end_time = pd.to_datetime(date + ' ' + end_time).strftime('%H:%M:%S')
    
    
    orders = pd.DataFrame(columns=cols)
    orders['证券代码'] = changes['symbol'].map(symbol_with_market)
    orders['资金账号'] = fund_account
    orders['序号'] = range(1, len(orders)+1)
    
    orders['买卖方向'] = changes['order_shares'].map(lambda x: '买入' if x > 0 else '卖出')
    orders['委托价格'] = ''
    orders['委托数量'] = changes['order_shares'].abs()
    orders['委托金额'] = ''
    orders['金额币种'] = ''
    orders['价格类型'] = '市价'
    orders['算法类型'] = algorithm
    orders['算法生效时间'] = start_time
    orders['算法失效时间'] = end_time
    orders['Participation Rate(%)'] = participate_rate
    orders['子单规模'] = ''
    orders['算法参数'] = ''
    orders['交易风格'] = '平稳'
    orders['涨停不卖跌停不买'] = '启用'
    orders['下单模式'] = ''
    orders['交易时段'] = trading_zone
    return orders
    
def convert_matic_otc_basket_orders(changes, **kwargs):
    cols = [
        '序号',
        '证券代码',
        '买卖方向',
        '价格类型',
        '委托价格',
        '委托数量',
        '委托金额',
        '金额币种',
        '实际委托数量是否取当天剩余可卖空数量与当前委托数量的最小值',
        '算法类型',
        '算法生效时间',
        '算法失效时间',
        'Participation Rate(%)',
        '交易风格',
        '涨停不卖跌停不买',
        '市价委托类型',
    ]
    strategy_type = kwargs.get('strategy_type', 'VWAP')
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '103000')
    strategy_style = kwargs.get('strategy_style', '平稳')  # 平稳, 激进
    LimAction = kwargs.get('LimAction', True)

    # 涨跌停价计算
    mkt_limit = kwargs['mkt_limit']
    pre_close = kwargs.get('pre_close')
    # print(pre_close)
    
    if mkt_limit.empty:
        # raise ValueError('mkt_limit is required')
        from decimal import Decimal
        def up_limit_ratio(symbol):
            symbol = str(symbol).zfill(6)
            if symbol.startswith('68') or symbol.startswith('30'):
                return 1.2
            else:
                return 1.1
        def down_limit_ratio(symbol):
            symbol = str(symbol).zfill(6)
            if symbol.startswith('68') or symbol.startswith('30'):
                return 0.8
            else:
                return 0.9
        pre_close['uplimit'] = (pre_close['close'] * pre_close['symbol'].apply(up_limit_ratio)).apply(lambda x: Decimal(str(x)).quantize(Decimal("0.01"), rounding = "ROUND_HALF_UP")).astype(float)
        pre_close['downlimit'] = (pre_close['close'] * pre_close['symbol'].apply(down_limit_ratio)).apply(lambda x: Decimal(str(x)).quantize(Decimal("0.01"), rounding = "ROUND_HALF_UP")).astype(float)
        mkt_limit = pre_close[['symbol', 'uplimit', 'downlimit']]
    
    
    changes = changes.merge(mkt_limit, on='symbol', how='left')
    # changes.loc[changes['order_shares']>0, 'order_price'] = changes[changes['order_shares']>0]['uplimit']
    # changes.loc[changes['order_shares']<0, 'order_price'] = changes[changes['order_shares']<0]['downlimit']
    
    
    changes.reset_index(inplace=True, drop=True)
    changes['Side'] = 0
    changes.loc[(changes['direction'] == 1) & (changes['order_shares'] > 0), 'Side'] = '买入'
    changes.loc[(changes['direction'] == 1) & (changes['order_shares'] < 0), 'Side'] = '卖出'
    changes.loc[(changes['direction'] == -1) & (changes['order_shares'] > 0), 'Side'] = '卖空'  # short sell
    changes.loc[(changes['direction'] == -1) & (changes['order_shares'] < 0), 'Side'] = '买平'  # close short
    
    changes.loc[changes['Side'].isin(['买入', '买平']), 'order_price'] = changes[changes['Side'].isin(['买入', '买平'])]['uplimit']
    changes.loc[changes['Side'].isin(['卖出', '卖空']), 'order_price'] = changes[changes['Side'].isin(['卖出', '卖空'])]['downlimit']
    
    orders = pd.DataFrame(columns=cols)
    orders['证券代码'] = changes['symbol'].map(symbol_with_market)
    orders['序号'] = range(1, len(orders)+1)
    orders['买卖方向'] = changes['Side']
    orders['价格类型'] = '市价'
    orders['委托价格'] = changes['order_price']
    orders['委托数量'] = changes['order_shares'].abs()
    orders['委托金额'] = ''
    orders['金额币种'] = ''
    orders['实际委托数量是否取当天剩余可卖空数量与当前委托数量的最小值'] = '否'
    orders['算法类型'] = strategy_type
    orders['算法生效时间'] = pd.to_datetime(datetime.now().strftime('%Y%m%d')+start_time).strftime('%H:%M:%S')
    orders['算法失效时间'] = pd.to_datetime(datetime.now().strftime('%Y%m%d')+end_time).strftime('%H:%M:%S')
    orders['Participation Rate(%)'] = 10
    orders['交易风格'] = strategy_style
    orders['涨停不卖跌停不买'] = '启用' if LimAction else '不启用'
    
    orders['市价委托类型'] = '市价最优五档即成剩撤'
    
    return orders
    
    
def convert_xuntou_dbf_orders(changes, **kwargs):
    # cols顺序不能换
    cols = [
        'order_type',
        'stock_code',
        'price_type',
        'act_type',
        'brokertype',
        'volume',
        'account_id',
        'tradeparam',
        'command_id',
        'inserttime',
    ]
    changes.reset_index(inplace=True, drop=True)
    changes['Side'] = 0
    changes.loc[(changes['direction'] == 1) & (changes['order_shares'] > 0), 'Side'] = '23'
    changes.loc[(changes['direction'] == 1) & (changes['order_shares'] < 0), 'Side'] = '24'


    """ brokertype
    1	期货账号
    2	股票账号
    3	信用账号
    6	股票期权账号
    7	沪港通账号
    8	收益互换账号
    10	全国股转账号
    11	深港通账号

    """
    if len(changes) > 0:

        start_time = kwargs.get('start_time', '093000')
        end_time = kwargs.get('end_time', '103000')
        brokertype = kwargs.get('brokertype', '2')
        account_id = kwargs.get('account_id', '')
        price_type = kwargs.get('price_type', '')
        AlgoParam = kwargs.get('AlgoParam', {})         # 拼接
        
        more_param = {
            'kssj' : start_time[0:2]+':'+start_time[2:4]+':'+start_time[4:6],
            'jssj' : end_time[0:2]+':'+end_time[2:4]+':'+end_time[4:6],
        }
        
        AlgoParam.update(more_param)
        AlgoParam = ','.join([f'{key}={value}' for key, value in AlgoParam.items()])
        
        orders = pd.DataFrame(columns=cols)
        orders['stock_code'] = changes['symbol'].map(lambda x: str(x).zfill(6))
        orders['order_type'] = changes['Side']
        orders['price_type'] = price_type   # 9003 智能算法 ,  9004 主动算法
        orders['act_type'] = '49'        # 49 其他资金账号
        orders['brokertype'] = brokertype       # 1 券商
        orders['volume'] = changes['order_shares'].abs()
        orders['account_id'] = account_id
        orders['tradeparam'] = AlgoParam
        orders['command_id'] = datetime.now().strftime('%m%d_%H%M%S%f')
        orders['inserttime'] = ''
        
    else:
        orders = pd.DataFrame(columns=cols)
    return orders
    
    

def convert_kafang_atx_scan_orders(changes, **kwargs):
    # cols顺序不能换
    cols = [
        'ExternalId',
        'ClientName',
        'Symbol',
        'Side',
        'OrderQty',
        'OrdType',
        'EffTime',
        'ExpTime',
        'LimAction',
        'AftAction',
        'AlgoParam',
    ]
    changes.reset_index(inplace=True, drop=True)
    changes['Side'] = 0
    changes.loc[(changes['direction'] == 1) & (changes['order_shares'] > 0), 'Side'] = 1
    changes.loc[(changes['direction'] == 1) & (changes['order_shares'] < 0), 'Side'] = 2
    
    algorithm = kwargs.get('algorithm', 'kf_vwap_core')
    if algorithm not in KF_ALGO_DICT:
        raise ValueError(f'algorithm {algorithm} not supported')

    if len(changes) > 0:
        if kwargs.get('secondary_account', False) == True and kwargs.get('secondary_account_clientname') is not None:
            short_sell_flag = kwargs['short_sell_flag']
            close_short_flag = kwargs['close_short_flag']
            
            # print(changes.loc[changes['direction'] == -1, :])
            changes.loc[changes['direction'] == -1, 'ClientName'] = kwargs['secondary_account_clientname']
            changes.loc[changes['direction'] == 1, 'ClientName'] = kwargs['ClientName']
            changes.loc[(changes['direction'] == -1) & (changes['order_shares'] > 0), 'Side'] = short_sell_flag
            changes.loc[(changes['direction'] == -1) & (changes['order_shares'] < 0), 'Side'] = close_short_flag
            
        else:
            changes['ClientName'] = kwargs['ClientName']
        
        # ClientName = kwargs['ClientName']
        
        start_time = kwargs.get('start_time', '093000')
        end_time = kwargs.get('end_time', '103000')
        OrdType = KF_ALGO_DICT[algorithm]
        
        LimAction = kwargs.get('LimAction', 0)
        AftAction = kwargs.get('AftAction', 0)
        AlgoParam = kwargs.get('AlgoParam', {})         # 中文用;拼接, 英文用:拼接
        
        trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
        basket_id = kwargs.get('basket_id', datetime.now().strftime('%H%M%S')) + '_' + str(uuid.uuid4())[:4]
        AlgoParam.update({'basket_id': basket_id})
        AlgoParam = ':'.join([f'{key}={value}' for key, value in AlgoParam.items()])
        
        orders = pd.DataFrame(columns=cols)
        orders['Symbol'] = changes['symbol'].map(symbol_with_market)
        orders['Side'] = changes['Side']
        orders['OrderQty'] = changes['order_shares'].abs().astype(int)
        orders['ExternalId'] = [basket_id + '_' + str(i).zfill(3) for i in range(1, len(orders)+1)]
        orders['ClientName'] = changes['ClientName']
        orders['OrdType'] = OrdType
        orders['EffTime'] = '{}{}000'.format(trading_date, start_time)
        orders['ExpTime'] = '{}{}000'.format(trading_date, end_time)
        orders['LimAction'] = LimAction
        orders['AftAction'] = AftAction
        orders['AlgoParam'] = AlgoParam
        
    else:
        orders = pd.DataFrame(columns=cols)
    return orders

    
def convert_kafang_atx_T0_scan_orders(t0_volume, **kwargs):
    """
    t0_volume: dataframe: ticker, t0_volume
    """
    cols = [
        'ExternalId',
        'ClientName',
        'Symbol',
        'Side',       # 0, T0
        'OrderQty',
        'OrdType',    # 算法代码
        'EffTime',
        'ExpTime',
        'LimAction',
        'AftAction',
        'AlgoParam',  # buyside=1:sellside=2
    ]
    
    t0_volume.reset_index(inplace=True, drop=True)
    
    algorithm = kwargs.get('algorithm', 'kf_t0')
    if algorithm not in KF_ALGO_DICT:
        raise ValueError(f'algorithm {algorithm} not supported')
    
    ClientName = kwargs['ClientName']
    
    
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '150000')
    OrdType = KF_ALGO_DICT[algorithm]
    
    LimAction = kwargs.get('LimAction', 0)
    AftAction = kwargs.get('AftAction', 0)
    AlgoParam = kwargs.get('AlgoParam', {})         # 中文用;拼接, 英文用:拼接
    
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    basket_id = kwargs.get('basket_id', datetime.now().strftime('%H%M%S')) + '_' + str(uuid.uuid4())[:4]
    AlgoParam.update({'basket_id': basket_id})
    AlgoParam = ':'.join([f'{key}={value}' for key, value in AlgoParam.items()])
    
    orders = pd.DataFrame(columns=cols)
    orders['Symbol'] = t0_volume['ticker'].map(symbol_with_market)
    orders['Side'] = 0
    orders['OrderQty'] = t0_volume['t0_volume'].abs().astype(int)
    orders['ExternalId'] = [basket_id + '_' + str(i).zfill(3) for i in range(1, len(orders)+1)]
    orders['ClientName'] = ClientName
    orders['OrdType'] = OrdType
    orders['EffTime'] = '{}{}000'.format(trading_date, start_time)
    orders['ExpTime'] = '{}{}000'.format(trading_date, end_time)
    orders['LimAction'] = LimAction
    orders['AftAction'] = AftAction
    orders['AlgoParam'] = AlgoParam
        
    return orders
    
    
# def convert_kafang_atx_scan_orders_short_support(changes, **kwargs):
#     cols = [
#         'ExternalId',
#         'ClientName',
#         'Symbol',
#         'Side',
#         'OrderQty',
#         'OrdType',
#         'EffTime',
#         'ExpTime',
#         'LimAction',
#         'AftAction',
#         'AlgoParam',
#     ]
#     changes.reset_index(inplace=True, drop=True)
    
#     algorithm = kwargs.get('algorithm', 'kf_vwap_core')
#     if algorithm not in KF_ALGO_DICT:
#         raise ValueError(f'algorithm {algorithm} not supported')

#     ClientName = kwargs['ClientName']
#     start_time = kwargs.get('start_time', '093000')
#     end_time = kwargs.get('end_time', '103000')
#     OrdType = KF_ALGO_DICT[algorithm]
    
#     LimAction = kwargs.get('LimAction', 0)
#     AftAction = kwargs.get('AftAction', 0)
#     AlgoParam = kwargs.get('AlgoParam', {})         # 中文用;拼接, 英文用:拼接
    
#     trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
#     basket_id = kwargs.get('basket_id', datetime.now().strftime('%H%M%S')) + '_' + str(uuid.uuid4())[:4]
#     AlgoParam.update({'basket_id': basket_id})
#     AlgoParam = ':'.join([f'{key}={value}' for key, value in AlgoParam.items()])
    
#     orders = pd.DataFrame(columns=cols)
#     orders['Symbol'] = changes['symbol'].map(symbol_with_market)
#     orders['Side'] = changes['order_shares'].map(lambda x: 1 if x > 0 else 2)  # 1是买入, 2是卖出
#     orders['OrderQty'] = changes['order_shares'].abs().astype(int)
#     orders['ExternalId'] = [basket_id + '_' + str(i).zfill(3) for i in range(1, len(orders)+1)]
#     orders['ClientName'] = ClientName
#     orders['OrdType'] = OrdType
#     orders['EffTime'] = '{}{}000'.format(trading_date, start_time)
#     orders['ExpTime'] = '{}{}000'.format(trading_date, end_time)
#     orders['LimAction'] = LimAction
#     orders['AftAction'] = AftAction
#     orders['AlgoParam'] = AlgoParam
#     return orders
    
def convert_kafang_ato_scan_orders(changes, **kwargs):
    cols = [
        'ExternalId',
        'ClientName',
        'Symbol',
        'Side',
        'OrderQty',
        'OrdType',
        'EffTime',
        'ExpTime',
        'LimAction',
        'AftAction',
        'AlgoParam',
        'UnitId',
        'ProductId',
    ]
    changes.reset_index(inplace=True, drop=True)
    algorithm = kwargs.get('algorithm', 'kf_vwap_core')
    if algorithm not in KF_ALGO_DICT:
        raise ValueError(f'algorithm {algorithm} not supported')

    ClientName = kwargs['ClientName']
    UnitId = kwargs.get('UnitId', '')
    ProductId = kwargs.get('ProductId', '')
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '103000')
    OrdType = KF_ALGO_DICT[algorithm]
    
    LimAction = kwargs.get('LimAction', 0)
    AftAction = kwargs.get('AftAction', 0)
    AlgoParam = kwargs.get('AlgoParam', {})   # 中文用;拼接, 英文用:拼接
    
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    basket_id = kwargs.get('basket_id', datetime.now().strftime('%H%M%S')) + '_' + str(uuid.uuid4())[:4]
    AlgoParam.update({'篮子编号': basket_id})
    AlgoParam = ';'.join([f'{key}={value}' for key, value in AlgoParam.items()])
    
    orders = pd.DataFrame(columns=cols)
    orders['Symbol'] = changes['symbol'].map(symbol_with_market)
    orders['Side'] = changes['order_shares'].map(lambda x: 1 if x > 0 else 2)  # 1是买入, 2是卖出
    orders['OrderQty'] = changes['order_shares'].abs().astype(int)
    orders['ExternalId'] = [basket_id + '_' + str(i).zfill(3) for i in range(1, len(orders)+1)]
    orders['ClientName'] = ClientName
    orders['OrdType'] = OrdType
    orders['EffTime'] = '{}{}000'.format(trading_date, start_time)
    orders['ExpTime'] = '{}{}000'.format(trading_date, end_time)
    orders['LimAction'] = LimAction
    orders['AftAction'] = AftAction
    orders['AlgoParam'] = AlgoParam
    orders['UnitId'] = UnitId
    orders['ProductId'] = ProductId
    return orders
    
def convert_yuliang_scan_orders(changes, **kwargs):
    # upper column name
    # changes.columns = [c.upper() for c in changes.columns]
    cols = [
        'EXTERNALID',
        'CLIENTNAME',
        'SYMBOL',
        'SIDE',
        'ORDERQTY',
        'ORDTYPE',
        'EFFTIME',
        'EXPTIME',
        'LIMACTION',
        'AFTACTION',
        'ALGOPARAM',
        'STOCKNUM',
        'SHAREHOLD',
        'CLIENTID',
        'FUNDID',
        'STRCODE'
    ]
    changes.reset_index(inplace=True, drop=True)
    algorithm = kwargs.get('algorithm', 'kuanfu')
    if algorithm not in YULIANG_ALGO_DICT:
        raise ValueError(f'algorithm {algorithm} not supported')

    ClientName = kwargs['ClientName']
    ClientId = kwargs.get('ClientId', '')
    FundId = kwargs.get('FundId', '')
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '103000')
    OrdType = YULIANG_ALGO_DICT[algorithm]
    
    LimAction = kwargs.get('LimAction', 0)
    AftAction = kwargs.get('AftAction', 0)
    AlgoParam = kwargs.get('AlgoParam', {})   # 中文用;拼接, 英文用:拼接
    
    trading_date = kwargs.get('trading_date', datetime.now().strftime('%Y%m%d'))
    basket_id = kwargs.get('basket_id', datetime.now().strftime('%H%M%S')) + '_' + str(uuid.uuid4())[:4]
    AlgoParam.update({'basket_id': basket_id})
    AlgoParam = ':'.join([f'{key}={value}' for key, value in AlgoParam.items()])
    
    orders = pd.DataFrame(columns=cols)
    orders['SYMBOL'] = changes['symbol'].map(symbol_with_market)
    orders['SIDE'] = changes['order_shares'].map(lambda x: 1 if x > 0 else 2)  # 1是买入, 2是卖出
    orders['ORDERQTY'] = changes['order_shares'].abs().astype(int)
    orders['EXTERNALID'] = basket_id
    orders['CLIENTNAME'] = ClientName
    orders['ORDTYPE'] = 101
    orders['EFFTIME'] = '{}{}000'.format(trading_date, start_time)
    orders['EXPTIME'] = '{}{}000'.format(trading_date, end_time)
    orders['LIMACTION'] = LimAction
    orders['AFTACTION'] = AftAction
    orders['ALGOPARAM'] = AlgoParam
    orders['STOCKNUM'] = len(orders)
    orders['SHAREHOLD'] = ''
    orders['CLIENTID'] = ClientId
    orders['FUNDID'] = FundId
    orders['STRCODE'] = OrdType

    return orders
    
    
def convert_hxtrade_algo_orders(changes, **kwargs):
    cols = [
        'account_id',
        'start_time',
        'end_time',
        'algo_name',
        'symbol',
        'trade_type',
        'task_qty',
        'basket_id',
        'limit_up_down_trading',
        'algo_params',
    ]
    
    changes.reset_index(inplace=True, drop=True)
    algorithm = kwargs.get('algorithm', 'HX_SMART_VWAP')
    account_id = kwargs['account_id']
    
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '103000')
    start_time = start_time[0:2]+':'+start_time[2:4]+':'+start_time[4:6]
    end_time = end_time[0:2]+':'+end_time[2:4]+':'+end_time[4:6]

    LimAction = kwargs.get('LimAction', 1)
    AlgoParam = kwargs.get('AlgoParam', {})   # 中文用;拼接, 英文用:拼接
    basket_id = kwargs.get('basket_id', datetime.now().strftime('%H%M%S')) + '_' + str(uuid.uuid4())[:4]
    AlgoParam = ';'.join([f'{key}={value}' for key, value in AlgoParam.items()])
    
    orders = pd.DataFrame(columns=cols)
    orders['symbol'] = changes['symbol'].map(symbol_with_market)
    
    orders['account_id'] = account_id
    orders['start_time'] = start_time
    orders['end_time'] = end_time
    orders['algo_name'] = algorithm
    orders['trade_type'] = changes['order_shares'].map(lambda x: 1 if x > 0 else 2)  # 1是买入, 2是卖出
    orders['task_qty'] = changes['order_shares'].abs().astype(int)
    orders['basket_id'] = basket_id
    orders['limit_up_down_trading'] = LimAction
    orders['algo_params'] = AlgoParam

    return orders

def convert_hxtrade_scan_orders(changes, **kwargs):
    cols = [
        'localid',
        'account_id',
        'start_time',
        'end_time',
        'algo_name',
        'symbol',
        'trade_type',
        'task_qty',
        'basket_id',
        'limit_up_down_trading',
        'algo_params',
    ]
    
    changes.reset_index(inplace=True, drop=True)
    algorithm = kwargs.get('algorithm', 'HX_SMART_VWAP')
    account_id = kwargs['account_id']
    
    start_time = kwargs.get('start_time', '093000')
    end_time = kwargs.get('end_time', '103000')
    start_time = start_time[0:2]+':'+start_time[2:4]+':'+start_time[4:6]
    end_time = end_time[0:2]+':'+end_time[2:4]+':'+end_time[4:6]

    LimAction = kwargs.get('LimAction', 1)
    AlgoParam = kwargs.get('AlgoParam', {})   # 中文用;拼接, 英文用:拼接
    basket_id = kwargs.get('basket_id', datetime.now().strftime('%H%M%S')) + '_' + str(uuid.uuid4())[:4]
    AlgoParam = ';'.join([f'{key}={value}' for key, value in AlgoParam.items()])
    
    # 额外的
    account_name = kwargs['acc']
    
    orders = pd.DataFrame(columns=cols)
    orders['symbol'] = changes['symbol'].map(symbol_with_market)
    
    # 额外的
    date = datetime.now().strftime('%Y%m%d')
    last_order_id = get_last_scan_order_id(account_name, date)
    
    # 额外的
    orders['localid'] = range(last_order_id+1, last_order_id+len(orders)+1)
    
    orders['account_id'] = account_id
    orders['start_time'] = start_time
    orders['end_time'] = end_time
    orders['algo_name'] = algorithm
    orders['trade_type'] = changes['order_shares'].map(lambda x: 1 if x > 0 else 2)  # 1是买入, 2是卖出
    orders['task_qty'] = changes['order_shares'].abs().astype(int)
    orders['basket_id'] = basket_id
    orders['limit_up_down_trading'] = LimAction
    orders['algo_params'] = AlgoParam

    # 额外的
    append_scan_order_ids(account_name, date, orders['localid'].tolist())    
    return orders

def get_last_scan_order_id(account_name, date):
    SCAN_ORDER_IDS_DIR = os.path.realpath(os.path.join(os.path.dirname(__file__), '..', 'accounts', 'OrderIdRecord'))
    ids_file_name = f'ids_{account_name}_{date}.xlsx'
    ids_file_path = os.path.join(SCAN_ORDER_IDS_DIR, ids_file_name)
    
    print(ids_file_path)
    try:
        ids = pd.read_excel(ids_file_path)
        ids = ids.sort_values('ids', ascending=True)
        last_id = ids['ids'].iloc[-1]
    except FileNotFoundError:
        last_id = 0
    return last_id

def append_scan_order_ids(account_name, date, ids):
    SCAN_ORDER_IDS_DIR = os.path.realpath(os.path.join(os.path.dirname(__file__), '..', 'accounts', 'OrderIdRecord'))
    ids_file_name = f'ids_{account_name}_{date}.xlsx'
    ids_file_path = os.path.join(SCAN_ORDER_IDS_DIR, ids_file_name)
    # ids = pd.DataFrame({'ids' : ids})
    # ids.to_excel(ids_file_path, sheet_name=account_name, index=False)
    try:
        original_ids = pd.read_excel(ids_file_path)
        original_ids = original_ids.sort_values('ids', ascending=True)
    except FileNotFoundError:
        original_ids = pd.DataFrame({'ids' : []})
    ids = pd.DataFrame({'ids' : ids})
    ids = pd.concat([original_ids, ids], axis=0, ignore_index=True)
    ids.to_excel(ids_file_path, index=False)
