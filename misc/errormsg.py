#%%
from loguru import logger
from io import StringIO

from misc.feishu import msgBot


def log_message_buffer(level='ERROR'):
    # 创建一个字符串缓冲区对象
    log_buffer = StringIO()

    # 添加目的地，将日志消息写入字符串缓冲区
    logger.add(log_buffer, level=level)

    # print(log_content)
    return log_buffer

#%%

def deal_error_msg(log_buffer, send_success=False, success_msg=None):
    log_content = log_buffer.getvalue()
    if send_success == True:
        if log_content:
            msgBot.send(log_content)
        else:
            if success_msg:
                msgBot.send(success_msg)
    else:
        if log_content:
            msgBot.send(log_content)
    


def print_errors(log_buffer):
    log_content = log_buffer.getvalue()
    if log_content:
        print('以下为任务警告信息: \n\n\n\n\n')
        print(log_content)
