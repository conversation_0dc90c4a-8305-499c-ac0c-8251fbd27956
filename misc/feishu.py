#!/usr/bin/env python3
# coding:utf-8
#%%

import base64
import hashlib
import hmac
from datetime import datetime

import requests
#%%
WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/db29b3e5-cb8a-4808-9fee-c40c5e17dddc"
WEBHOOK_SECRET = "rvx103naDoWE0RE2tqaP5f"


QHCL_WEB_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/50c42809-e530-4fb7-a5c3-1a9661ef6674"
QHCL_SECRET = "jryeMdecGBfHwv1cPJpstc"

IX_FUTURE_WEB_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/ee85585f-dc20-4f84-94d9-b36d92b4e963"
IX_FUTURE_SECRET = "sgC455CgTAN4n1QxaHgmsg"


class LarkBot:
    def __init__(self, hookurl:str, secret: str) -> None:
        if not secret:
            raise ValueError("invalid secret key")
        self.secret = secret
        self.hookurl = hookurl

    def gen_sign(self, timestamp: int) -> str:
        string_to_sign = '{}\n{}'.format(timestamp, self.secret)
        hmac_code = hmac.new(
            string_to_sign.encode("utf-8"), digestmod=hashlib.sha256
        ).digest()
        sign = base64.b64encode(hmac_code).decode('utf-8')

        return sign

    def send(self, content: str) -> None:
        timestamp = int(datetime.now().timestamp())
        sign = self.gen_sign(timestamp)

        params = {
            "timestamp": timestamp,
            "sign": sign,
            "msg_type": "text",
            "content": {"text": content},
        }
        resp = requests.post(url=self.hookurl, json=params)
        resp.raise_for_status()
        result = resp.json()
        if result.get("code") and result["code"] != 0:
            print(result["msg"])
            return
        print("飞书机器人消息发送成功")

# %%
msgBot = LarkBot(WEBHOOK_URL, WEBHOOK_SECRET)
qhcl_msgBot = LarkBot(QHCL_WEB_URL, QHCL_SECRET)
IX_future_msgBot = LarkBot(IX_FUTURE_WEB_URL, IX_FUTURE_SECRET)
