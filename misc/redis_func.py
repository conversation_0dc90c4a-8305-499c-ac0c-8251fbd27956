#%%
import redis
import io
import pandas as pd
# import os
# import pickle
# import joblib

# pool = redis.ConnectionPool(host='host', port='port', db='db')
# # cur = redis.Redis(connection_pool=pool)
# rs = redis.StrictRedis(connection_pool=pool)


def conn_redis(host='localhost', port=6379, db=0):
    pool = redis.ConnectionPool(host=host, port=port, db=db)
    cur = redis.Redis(connection_pool=pool)
    return cur

# parquet 序列化方式
def df_write_redis(rs, key, df, expire:int=432000): # expire 5d
    df_bytes = io.BytesIO()
    df.to_parquet(df_bytes, engine='pyarrow')
    df_bytes.seek(0)  # 回到字节流的开始位置
    res = rs.set(key, df_bytes.read())
    if res != True:
        raise ValueError('df not cached')
    if expire > 0:
        rs.expire(key, expire)

def df_read_redis(rs, key):
    df_bytes_from_redis = rs.get(key)
    if df_bytes_from_redis is None:
        return
    df_from_redis = pd.read_parquet(io.BytesIO(df_bytes_from_redis), engine='pyarrow')
    return df_from_redis


# joblib 序列化方式
# def df_write_redis(rs, key, df, expire:int=432000): # expire 5d
#     df_bytes = io.BytesIO()
#     joblib.dump(df, df_bytes)
#     df_bytes.seek(0)  # 回到字节流的开始位置
#     res = rs.set(key, df_bytes.read())
#     if res != True:
#         raise ValueError('df not cached')
#     if expire > 0:
#         rs.expire(key, expire)

# def df_read_redis(rs, key):
#     df_bytes_from_redis = rs.get(key)
#     if df_bytes_from_redis is None:
#         return
#     df_from_redis = joblib.load(io.BytesIO(df_bytes_from_redis))
#     return df_from_redis


# # pickle 序列化方式
# def df_write_redis(rs, key, df, expire:int=432000): # expire 5d
#     df_bytes = pickle.dumps(df)
#     res = rs.set(key, df_bytes)
#     if res != True:
#         raise ValueError('df not cached')
#     if expire > 0:
#         rs.expire(key, expire)

# def df_read_redis(rs, key):
#     df_bytes_from_redis = rs.get(key)
#     if df_bytes_from_redis is None:
#         return
#     df_from_redis = pickle.loads(df_bytes_from_redis)
#     return df_from_redis

