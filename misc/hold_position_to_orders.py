# input: account args
# output: order DataFrame

from loguru import logger
import os
import pandas as pd
from datetime import datetime

# from accounts_config.general import inner_ftp_root
# from misc.utils import exec_scp_command
from misc.utils import symbol_with_market
from misc.convert_orders import (
    convert_matic_orders,
    convert_matic_ai_orders,
    convert_jx_iceberg_orders,
    convert_xt1_orders,
    convert_gtrade_orders,
    convert_ehaifangzhou_orders,
    convert_kafang_atx_orders,
    convert_dfdma_kafang_orders,
    convert_yinhedma_kafang_orders,
    convert_zhaoshang_kafang_orders,
    convert_ax_onequant_lanzi_orders,
    convert_jinshida_lanzi_orders,
    convert_zhaoshang_orders,
    convert_zhaoshang_swap_orders,
    convert_guangfa_swap_orders,
    convert_guangfa_file_order,
    convert_zhongjin_kafang_orders,
    convert_kafang_algo_orders,
    convert_zhongxin_cats_orders,
    convert_haitong_file_order,
    convert_dfemc_basket_orders,
    convert_dfemc_sweep_orders,
    convert_matic_otc_orders,
    convert_matic_otc_basket_orders,
    convert_cats_generic_orders,
    convert_kafang_atx_scan_orders,
    convert_kafang_ato_scan_orders,
    convert_yuliang_scan_orders,
    # convert_cats_generic_orders_new
    convert_hxtrade_algo_orders,
    convert_hxtrade_scan_orders,
    convert_kafang_algo_scan_orders,
    convert_touyitong_scan_orders,
    convert_xuntou_dbf_orders,
)
# from misc.ssh_conn import sftp_clent_inner

def handle_hold_position_to_change(
    local_account_dir,
    accounts,  # accounts group dict
    account_name,
    hold_file,
    position_file,
):

    logger.info("#" * 50 + '\n')
    logger.info('account_name: {} \n'.format(account_name))

    hold_file_format = accounts[account_name]['format']
    hold_file_rename_cols = accounts[account_name]['cols']
    hold_file_args = accounts[account_name]['args']

    # save hold and position file to local files
    local_hold_file = os.path.join(local_account_dir, hold_file)
    local_position_file = os.path.join(local_account_dir, position_file)

    # inner_account_dir = os.path.join(inner_ftp_root, account_name)
    # inner_position_file = os.path.join(inner_account_dir, 'position', position_file)
    # inner_hold_file = os.path.join(inner_account_dir, 'hold', hold_file)

    # exec_scp_command(inner_position_file, local_position_file)
    # exec_scp_command(inner_hold_file, local_hold_file) 
    # sftp_clent_inner.get(inner_position_file, local_position_file)
    sftp_clent_inner.get(os.path.join(account_name, 'position', position_file), 
                         local_position_file)
    logger.info('sftp get position done: {} - {}'.format(account_name, position_file))
    sftp_clent_inner.get(os.path.join(account_name, 'hold', hold_file), 
                         local_hold_file)
    logger.info('sftp get hold done: {} - {}'.format(account_name, hold_file))
    
    # first day, no hold file
    if accounts[account_name].get('first_day_no_hold', False) == True:
        logger.warning("{} account first day, no hold file".format(account_name))
        local_hold_file = None
        
    # convert position to changes
    changes = position_to_changes(
            local_hold_file, 
            local_position_file, 
            hold_file_format,
            hold_file_rename_cols,
            **hold_file_args
        )
    return changes

    
def position_to_changes(hold_file_path, 
                        position_file_path, 
                        hold_format, 
                        hold_cols, 
                        **hold_args
                        ):
    # account first day, no hold file
    if hold_file_path == None:
        hold = pd.DataFrame(columns=['symbol', 'hold_shares'])
        hold = hold.set_index('symbol')        

    else:
        # hold = pd.read_excel(hold_file_path)
        hold = read_table(hold_file_path, hold_format, **hold_args)
        hold = hold.rename(columns=hold_cols)
        hold = hold[['ticker', 'shares']]
        hold = hold.rename(columns={'ticker': 'symbol', 'shares': 'hold_shares'})
        hold = hold.set_index('symbol')

    target = pd.read_csv(position_file_path, header=None, names=['symbol', 'target_shares'])
    # target['target_shares'] = (target['target_shares'] * 100).astype(int)
    target['target_shares'] = target['target_shares'].astype(int)
    target = target.set_index('symbol')

    changes = pd.concat([hold, target], axis=1, join='outer')
    changes = changes.fillna(0)
    changes['order_shares'] = changes['target_shares'] - changes['hold_shares']
    changes = changes[changes['order_shares'] != 0]
    changes = changes.reset_index()

    changes = changes.apply(adjust_changes, axis=1)
    changes['order_shares'] = changes['order_shares'].astype(int)
    
    # filter 688xxx less than 100 shares
    mask = (changes['symbol'] >= 688000) & (changes['order_shares'].abs() < 200)
    kcb_not_trade = changes[mask]
    if len(kcb_not_trade) > 0:
        logger.warning('kcb_not_trade: \n {}'.format(kcb_not_trade))
        changes = changes[~mask]
    changes['symbol'] = changes['symbol'].astype(int)
    changes = changes.reset_index()

    return changes
        
def adjust_changes(row):
    """
    根据主板创业板, 科创板交易规则,调整委托数量
    科创板: 
    1. 如果hold >= 200, 
        买 如果 order >= 100 并 < 200,  买 200
        如果 order < 100 , 不买

        卖, 如果 order >= 100 并 < 200, 卖 200, 如hold 245, pos 135, 卖200
            如果 order < 100, 不卖

    2. 如果hold < 200,  
        买 如果 order > 100 并 < 200,  买 200
        如果 order < 100 , 不买

        卖, 如果 pos < 100 , 卖 hold, 如hold 145, pos 95, ,卖145
            如果 pos > 100,  不卖,  如 hold 145, pos 115, 不卖

    主板创业板:
    余数 = hold % 100
    1. 买, 如果 order余数 >= 50, 向上取整
        如果 order余数 < 50, 向下取整

    2. 卖,
        如果 order余数 != hold余数, 卖order 向下取整 , 如 hold 275, order -225, 卖200
        如果 order余数 = hold余数, 卖order 
    """
    # print(type(row))
    if row['symbol'] >= 688000:
        # 科创板
        if row['hold_shares'] >= 200:
            # hold >= 200
            if 100 <= row['order_shares'] < 200:
                row['order_shares'] = 200
            elif -200 < row['order_shares'] <= -100:
                row['order_shares'] = -200
            elif -100 < row['order_shares'] < 100:
                row['order_shares'] = 0
        else:
            # hold < 200 and hold >= 0
            if 100 <= row['order_shares'] < 200:
                row['order_shares'] = 200
            elif 0 < row['order_shares'] < 100:
                row['order_shares'] = 0
            elif row['order_shares'] < 0:
                if row['target_shares'] < 100:
                    row['order_shares'] = -row['hold_shares']
                elif row['target_shares'] >= 100:
                    row['order_shares'] = 0
                    
    else:
        # 主板创业板
        order_remainder = abs(row['order_shares']) % 100
        if row['order_shares'] > 0:
            if order_remainder >= 50:
                row['order_shares'] = row['order_shares'] - order_remainder + 100
            else:
                row['order_shares'] = row['order_shares'] - order_remainder
        elif row['order_shares'] < 0:
            if (row['hold_shares'] % 100) != order_remainder:
                row['order_shares'] = row['order_shares'] + order_remainder
    return row

def read_table(file_path, format, **kwargs):
    if format == 'csv':
        df = pd.read_csv(file_path, **kwargs)
    elif format == 'excel':
        df = pd.read_excel(file_path, **kwargs)
    else:
        raise TypeError('file format not supported')
    return df


def handle_hold_position_to_target_lk(
    local_account_dir,
    account_name,
    position_file,
    date
):

    logger.info("#" * 50 + '\n')
    logger.info('account_name: {} \n'.format(account_name))

    # save position file to local files
    local_position_file = os.path.join(local_account_dir, position_file)

    # inner_account_dir = os.path.join(inner_ftp_root, account_name)
    # inner_position_file = os.path.join(inner_account_dir, 'position', position_file)
    # inner_hold_file = os.path.join(inner_account_dir, 'hold', hold_file)

    # exec_scp_command(inner_position_file, local_position_file)
    sftp_clent_inner.get(os.path.join(account_name, 'position', position_file),
                         local_position_file)
    logger.info('sftp get position done: {} - {} \n'.format(account_name, position_file))

    target = pd.read_csv(local_position_file, header=None, names=['symbol', 'target_shares'])
    target['qty'] = target['target_shares'].astype(int)
    target['date'] = date
    target = target[['date', 'symbol', 'qty']]

    # date	symbol	qty
    # ********	002460.SZ	200
    # ********	601198.SH	1500
    target['symbol'] = target['symbol'].apply(symbol_with_market)
    return target


def generate_order_files_list(changes, order_type, local_account_dir, account_name, date, suffix='', **kwargs):
    local_order_files = []
    # order type: xt1
    if order_type == 'xt1':
        new_orders = convert_xt1_orders(changes)

        # save orders to file
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    # order type: matic
    elif order_type == 'matic':
        orders_buy, orders_sell = convert_matic_orders(changes)
        # convert orders
        local_order_buy = os.path.join(local_account_dir, "{}_{}_buy{}.csv".format(account_name, date, suffix))
        local_order_sell = os.path.join(local_account_dir, "{}_{}_sell{}.csv".format(account_name, date, suffix))

        # save orders to file
        orders_buy.to_csv(local_order_buy, index=False)
        # logger.info("save orders_buy to file: {} ...".format(local_order_buy))
        orders_sell.to_csv(local_order_sell, index=False)
        # logger.info("save orders_sell to file: {} ...".format(local_order_sell))
        local_order_files.append(local_order_buy)
        local_order_files.append(local_order_sell)

    # order type: matic_ai
    elif order_type == 'matic_ai':
        new_orders = convert_matic_ai_orders(changes)

        # save orders to file
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, index=False)
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'jx_iceberg':
        # order type: jx_iceberg
        new_orders = convert_jx_iceberg_orders(changes)

        # save orders to file
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=False, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'gtrade':
        new_orders = convert_gtrade_orders(changes, **kwargs)

        order_filename = kwargs['order_filename'].format(
            account = account_name.replace('_','').replace('.',''),
            num = len(new_orders),
            date = date,
            suffix = suffix
        )
        local_order_file = os.path.join(local_account_dir, order_filename)
        # local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'ehaifangzhou':
        new_orders = convert_ehaifangzhou_orders(changes, **kwargs)

        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.xlsx".format(account_name, date, suffix))
        new_orders.to_excel(local_order_file, header=False, index=False)
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'kafang_atx':
        new_orders = convert_kafang_atx_orders(changes, **kwargs)

        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
            
    elif order_type == 'dfdma_kafang':
        new_orders = convert_dfdma_kafang_orders(changes, **kwargs)

        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)

    elif order_type == 'yinhedma_kafang':
        new_orders = convert_yinhedma_kafang_orders(changes, **kwargs)

        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'zhaoshang_kafang':
        new_orders = convert_zhaoshang_kafang_orders(changes, **kwargs)

        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='utf-8')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'kafang_algo':
        new_orders = convert_kafang_algo_orders(changes, **kwargs)

        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='utf-8')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'zhaoshang':
        new_orders = convert_zhaoshang_orders(changes, **kwargs)

        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.xlsx".format(account_name, date, suffix))
        new_orders.to_excel(local_order_file, header=True, index=False)
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'zhaoshang_swap':
        new_orders = convert_zhaoshang_swap_orders(changes, **kwargs)

        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'guangfa_swap':
        new_orders = convert_guangfa_swap_orders(changes, **kwargs)
        
        chunk_size = 500
        order_chunks = [new_orders[i:i+chunk_size] for i in range(0, len(new_orders), chunk_size)]
        
        for i, chunk in enumerate(order_chunks):
            local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.xlsx".format(account_name, date, suffix+'_'+str(i+1)))
            chunk.to_excel(local_order_file, header=True, index=False)
            # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
            local_order_files.append(local_order_file)
        
    elif order_type == 'ax_onequant_lanzi':
        new_orders = convert_ax_onequant_lanzi_orders(changes, **kwargs)
        
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'jinshida':
        new_orders = convert_jinshida_lanzi_orders(changes, **kwargs)
        
        chunk_size = 500
        order_chunks = [new_orders[i:i+chunk_size] for i in range(0, len(new_orders), chunk_size)]
        
        for i, chunk in enumerate(order_chunks):
            local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.xlsx".format(account_name, date, suffix+'_'+str(i+1)))
            chunk.to_excel(local_order_file, header=True, index=False)
            # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
            local_order_files.append(local_order_file)
            
    elif order_type == 'guangfa_file':
        new_orders = convert_guangfa_file_order(changes, **kwargs)
        
        chunk_size = 500
        order_chunks = [new_orders[i:i+chunk_size] for i in range(0, len(new_orders), chunk_size)]
        
        for i, chunk in enumerate(order_chunks):
            local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix+'_'+str(i+1)))
            chunk.to_csv(local_order_file, header=True, index=False, encoding='gbk')
            # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
            local_order_files.append(local_order_file)

    elif order_type == 'touyitong_scan':
        new_orders = convert_touyitong_scan_orders(changes, **kwargs)
        
        chunk_size = 1000
        order_chunks = [new_orders[i:i+chunk_size] for i in range(0, len(new_orders), chunk_size)]
        
        for i, chunk in enumerate(order_chunks):
            # local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix+'_'+str(i+1)))
            order_filename = kwargs['order_filename'].format(
                date_time = datetime.now().strftime('%Y%m%d%H%M%S%f')[:-3],
                suffix = suffix.replace('_', '') +str(i+1)
            )
            local_order_file = os.path.join(local_account_dir, order_filename)
            
            chunk.to_csv(local_order_file, header=True, index=False, encoding='gbk')
            # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
            local_order_files.append(local_order_file)

    elif order_type == 'zhongjin_kafang':
        new_orders = convert_zhongjin_kafang_orders(changes, **kwargs)

        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
            
        # local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        # new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        # local_order_files.append(local_order_file)
        
    elif order_type == 'zhongxin_cats':
        new_orders = convert_zhongxin_cats_orders(changes, **kwargs)
        
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=False, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'cats_generic':
        new_orders = convert_cats_generic_orders(changes, **kwargs)
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=False, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'haitong_file':
        new_orders = convert_haitong_file_order(changes, **kwargs)
        
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, index=False, encoding='gbk')
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)
        
    elif order_type == 'dfemc_basket':
        new_orders = convert_dfemc_basket_orders(changes, **kwargs)

        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.xlsx".format(account_name, date, suffix))
        new_orders.to_excel(local_order_file, index=False)
        # logger.info("save orders_buy/sell to file: {} ...".format(local_order_file))
        local_order_files.append(local_order_file)

    elif order_type == 'dfemc_sweep':
        new_orders = convert_dfemc_sweep_orders(changes, **kwargs)
        order_filename = kwargs['order_filename'].format(
            num = len(new_orders),
            account = account_name.replace('_','').replace('.',''),
            nowtime = datetime.now().strftime('%Y%m%d%H%M%S%f')[:-3]
        )
        local_order_file = os.path.join(local_account_dir, order_filename)
        new_orders.to_csv(local_order_file, index=False, encoding='gbk')
        local_order_files.append(local_order_file)
        
    elif order_type == 'matic_otc_basket':
        new_orders = convert_matic_otc_basket_orders(changes, **kwargs)
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.xlsx".format(account_name, date, suffix))
        new_orders.to_excel(local_order_file, index=False)
        local_order_files.append(local_order_file)
        
    elif order_type == 'kafang_atx_scan':
        new_orders = convert_kafang_atx_scan_orders(changes, **kwargs)
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.xlsx".format(account_name, date, suffix))
        new_orders.to_excel(local_order_file, header=True, index=False)
        local_order_files.append(local_order_file)
        
    elif order_type == 'kafang_ato_scan':
        new_orders = convert_kafang_ato_scan_orders(changes, **kwargs)
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.xlsx".format(account_name, date, suffix))
        new_orders.to_excel(local_order_file, header=True, index=False)
        local_order_files.append(local_order_file)
        
    elif order_type == 'yuliang_scan':
        new_orders = convert_yuliang_scan_orders(changes, **kwargs)
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.xlsx".format(account_name, date, suffix))
        new_orders.to_excel(local_order_file, header=True, index=False)
        local_order_files.append(local_order_file)

    elif order_type == 'hxtrade_algo':
        new_orders = convert_hxtrade_algo_orders(changes, **kwargs)
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        local_order_files.append(local_order_file)
        
    elif order_type == 'hxtrade_scan':
        new_orders = convert_hxtrade_scan_orders(changes, **kwargs)
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        local_order_files.append(local_order_file)
        
    elif order_type == 'kafang_algo_scan':
        new_orders = convert_kafang_algo_scan_orders(changes, **kwargs)
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.xlsx".format(account_name, date, suffix))
        new_orders.to_excel(local_order_file, header=True, index=False)
        local_order_files.append(local_order_file)
        
    elif order_type == 'xuntou_dbf':
        new_orders = convert_xuntou_dbf_orders(changes, **kwargs)
        local_order_file = os.path.join(local_account_dir, "{}_{}_order{}.csv".format(account_name, date, suffix))
        new_orders.to_csv(local_order_file, header=True, index=False, encoding='gbk')
        local_order_files.append(local_order_file)
    
    else:
        raise ValueError('order_type not supported: {}'.format(order_type))
    
    return local_order_files

        
        