dbf_files_header_dict = {
    "yuliang_OrderAlgo": [
        ["EXTERNALID", "C(20)"],
        ["CLIENTNAME", "C(20)"],
        ["SYMB<PERSON>", "C(10)"],
        ["SIDE", "N(3,0)"],
        ["ORDERQTY", "N(10,0)"],
        ["ORDTYPE", "N(3,0)"],
        ["EFFTIME", "C(17)"],
        ["EXPTIME", "C(17)"],
        ["LIMACTION", "N(1,0)"],
        ["AFTACTION", "N(1,0)"],
        ["ALGOPARAM", "C(255)"],
        ["STOCKNUM", "N(4,0)"],
        ["SHAREHOLD", "C(12)"],
        ["CLIENTID", "C(20)"],
        ["FUNDID", "C(20)"],
        ["STRCODE", "C(20)"],
    ],
    "xuntou_dbf_order": [
        ["ORDER_TYPE", "C(20)"],
        ["STOCK_CODE", "C(20)"],
        ["PRICE_TYPE", "C(20)"],
        ["ACT_TYPE", "C(20)"],
        ["BROKERTYPE", "C(20)"],
        ["VOLUME", "C(20)"],
        ["ACCOUNT_ID", "C(100)"],
        ["TRADEPARAM", "C(255)"],
        ["COMMAND_ID", "C(100)"],
        ["INSERTTIME", "C(20)"],
    ],
}


Cicc_Ims_Short_Hold_Src_type = 'trade'
Cicc_Ims_Short_Hold_Root = "交易软件/中金场外IMS交易端/Data/FlashExport"
Cicc_Ims_Short_Hold_File = "feGlobalTradeHolding.txt"
Cicc_Client_Accounts = {
    # 'test'                      :        'test', 
    '宽辅思贤专享中性1号_中金多空'   :       '5282PT0004',      # portfolioId
    '宽辅专享6号_中金场外'         :        '5282PT0013',      # portfolioId
}



GuangDa_LS_Client_Accounts = [
    # 'test', 
    '宽辅泓涛专享2号_光大场外',
    '宽辅思贤专享中性2号_光大场外',
]

# Cicc_Ims_Short_Hold_Src_type = 'dav'
# Cicc_Ims_Short_Hold_Root = ""
# Cicc_Ims_Short_Hold_File = "feGlobalTradeHolding.txt"
# Cicc_Client_accounts = ['testaccount', '宽辅思贤专享中性1号_中金多空']

# CICC_CLIENT_NAME_ACCOUNT_DICT = {
#     # account_name 对应 客户端 client_name
#     'testaccount' : '中金场外多空'
# }