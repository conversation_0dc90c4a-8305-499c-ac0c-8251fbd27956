import pandas as pd


from misc.utils import to_datetime_formmats

# def standardize_hold_kafang_zheshang_dbf(df: pd.DataFrame):
#     df = df.rename(columns={
#         'Symbol'.upper()        : 'ticker',
#         'CurrentQ'.upper()      : 'hold_shares',
#         'EnableQty'.upper()     : 'available_shares',
#     })
#     df[['ticker', 'exchange_code']] = df['ticker'].str.split('.', expand=True)
#     df = df[['ticker', 'hold_shares']]
#     df.columns = ['代码', '持仓量']
#     return df


# def standardize_order_kafang_zheshang_dbf(df: pd.DataFrame, date, **kwargs):
#     df = df.rename(columns={
#         'TransTime'.upper()     : 'time',
#         'Symbol'.upper()        : 'ticker',
#         'Side'.upper()          : 'BS_flag',
#         'OrderQty'.upper()      : 'order_volume',
#         'OrdStatus'.upper()     : 'order_status',
#         'Price'.upper()         : 'order_price',
#         'CumQty'.upper()        : 'fill_volume',
#         'AvgPx'.upper()         : 'fill_price',
#         'OrderId'.upper()       : 'order_ref',
#         'date'.upper()          : 'date',
#         '算法'                  : 'algorithm',
#     })
#     df['time'] = to_datetime_formmats(df['time'], source_type='time')
#     df.sort_values('time', inplace=True)
#     df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
#     df[['ticker', 'exchange_code']] = df['ticker'].str.split('.', expand=True)
#     df['BS_flag'] = df['BS_flag'].apply(lambda x: _replace_bs_flag_kafangalgo(x))
#     df = df[(df['BS_flag'] == '买入') | (df['BS_flag'] == '卖出')]
#     df['order_status'] = df['order_status'].apply(lambda x : _replace_order_status_kafangalgo(x))
#     if df['order_ref'].dtype != 'object':
#         raise TypeError('委托编号/order_ref should be str type')
#     df['date'] = date
#     mask_should_be_partially_filled = (df['fill_volume'] != 0) & (df['order_status'] == '已撤')
#     df.loc[mask_should_be_partially_filled, 'order_status'] = '部成'
#     df = df.reset_index()

#     if 'algorithm' not in df.columns:
#         df['algorithm'] = ''
#     df = df[["date",
#                    "time",
#                    "order_ref",
#                    "ticker",
#                    "BS_flag",
#                    "order_price",
#                    "order_volume",
#                    "fill_price",
#                    "fill_volume",
#                    "order_status",
#                    "algorithm"]]
#     df.columns = ['委托日期',
#                     '委托时间',
#                     '合同编号',
#                     '代码',
#                     '买卖标记',
#                     '委托价格',
#                     '委托数量',
#                     '成交均价',
#                     '成交数量',
#                     '委托状态',
#                     '算法']
#     return df

# def standardize_deal_kafang_zheshang_dbf(df: pd.DataFrame, date, **kwargs):
#     df = df.rename(columns={
#         'TransTime'.upper()     : 'time',
#         'Symbol'.upper()        : 'ticker',
#         'Side'.upper()          : 'BS_flag',
#         'OrderQty'.upper()      : 'order_volume',
#         'OrdStatus'.upper()     : 'order_status',
#         'Price'.upper()         : 'order_price',
#         'CumQty'.upper()        : 'fill_volume',
#         'AvgPx'.upper()         : 'fill_price',
#         'OrderId'.upper()       : 'order_ref',
#         'date'.upper()          : 'date',
#         '算法'                  : 'algorithm',
#     })
#     df['time'] = to_datetime_formmats(df['time'], source_type='time')
#     df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
#     df[['ticker', 'exchange_code']] = df['ticker'].str.split('.', expand=True)
#     df['BS_flag'] = df['BS_flag'].apply(lambda x: _replace_bs_flag_kafangalgo(x))
#     # print(df.head(2))
#     df = df[(df['BS_flag'] == '买入') | (df['BS_flag'] == '卖出')]
#     df['order_status'] = df['order_status'].apply(lambda x : _replace_order_status_kafangalgo(x))
#     df['date'] = date
#     if df['order_ref'].dtype != 'object':
#         raise TypeError('委托编号/order_ref should be str type')
#     df.sort_values('time', inplace=True)
#     df = df[df['fill_volume'] != 0]
#     df = df.reset_index()
#     df['deal_ref'] = 'deal_' + pd.Series([str(x) for x in range(1, len(df)+1)])
#     if 'algorithm' not in df.columns:
#         df['algorithm'] = ''
#     df = df[[
#         "date",
#         "time",
#         "order_ref",
#         "deal_ref",
#         "ticker",
#         "BS_flag",
#         "fill_price",
#         "fill_volume",
#         "algorithm"
#     ]]
#     df.columns = [
#         "成交日期",
#         '成交时间',
#         '合同编号',
#         '成交编号',
#         '代码',
#         '买卖标记',
#         '成交均价',
#         '成交数量',
#         '算法'
#     ]
#     return df


# def _replace_bs_flag_kafangalgo(flag):
#     if flag in [
#         1,
#     ]:
#         flag = "买入"
#     elif flag in [
#         2,
#     ]:
#         flag = "卖出"
#     elif flag in [
#         -1,
#     ]:
#         flag = "无效"
#     else:
#         raise ValueError("买卖标记:  \n{} \n不在范围中".format(flag))
#     return flag


# def _replace_order_status_kafangalgo(status):
#     if status in [0]:
#         status = "已报"
#     elif status in [1]:
#         status = "部成"
#     elif status in [2]:
#         status = "已成"
#     elif status in [4]:
#         status = "已撤"
#     elif status in [6]:
#         status = "待撤"
#     elif status in [8, 7]:
#         status = "废单"
#     elif status in [10]:
#         status = "等待报单"
#     else:
#         raise ValueError("委托状态:  \n{} \n不在范围中".format(status))
#     return status

def standardize_hold_zhongxin_cats(df: pd.DataFrame, **kwargs):
    df['tmp_direct'] = df['longshort'].apply(lambda x: 1 if x == '多头' else -1)
    df['hold_shares'] = df['hold_shares'].mul(df['tmp_direct'])
    df['available_shares'] = df['available_shares'].mul(df['tmp_direct'])
    df['hold_value'] = df['hold_value'].mul(df['tmp_direct'])
    
    return df


def standardize_marginhold_zhongxin_cats(df: pd.DataFrame, **kwargs):
    df = df.rename(
        columns={
            "代码": "ticker",
            "持仓方向": "longshort",
            "持仓": "hold_volume",
            "可用持仓": "available_volume",
            "成本价": "hold_cost",
            "最新价": "last_price",
            "参考市值": "hold_value",
        }
    )
    return df


def standardize_margindeal_zhongxin_cats(df: pd.DataFrame, **kwargs):
    df = df.rename(
        columns={
            # '时间'           : 'date',
            "代码": "ticker",
            "交易": "BS_flag",
            "委托量": "order_volume",
            "成交量": "fill_volume",
            "委托价格": "order_price",
            "成交均价": "fill_price",
            "状态": "order_status",
            "成交额": "fill_amount",
            "订单号": "order_ref",
            "算法ID": "algorithm",
        }
    )
    df["时间"] = to_datetime_formmats(
        df["时间"], source_type="time", try_formats=["%Y/%m/%d %H:%M:%S"]
    )
    df["date"] = df["时间"].apply(lambda x: x.strftime("%Y%m%d"))
    df["time"] = df["时间"].apply(lambda x: x.strftime("%H:%M:%S"))

    if df["ticker"].dtype == "int64":
        df["ticker"] = df["ticker"].apply(lambda x: str(x).zfill(6))

    df["_买卖标记"] = df["BS_flag"].apply(lambda x: _replace_zhongxin_cats_margin_BSflag(x))
    df["longshort"] = df["_买卖标记"].str.slice(0, 2)
    df["operation"] = df["_买卖标记"].str.slice(2, 4)

    df = df[df["fill_volume"] != 0]

    df["deal_ref"] = df["order_ref"]
    df = df[
        [
            "date",
            "time",
            "order_ref",
            "deal_ref",
            "ticker",
            "operation",
            "longshort",
            "fill_price",
            "fill_volume",
            "algorithm",
        ]
    ].sort_values("time")
    return df


def _replace_zhongxin_cats_margin_BSflag(flag):
    if flag in [
        "买入",
    ]:
        flag = "多头开仓"
    elif flag in ["卖出"]:
        flag = "多头平仓"
    elif flag in ["卖空"]:
        flag = "空头开仓"
    elif flag in ["平空"]:
        flag = "空头平仓"
    else:
        raise ValueError("买卖标记:  \n{} \n不在范围中".format(flag))
    return flag

