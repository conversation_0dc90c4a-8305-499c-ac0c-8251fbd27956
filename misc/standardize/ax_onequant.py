import pandas as pd

from misc.utils import to_datetime_formmats

# standardize hold data for anxin onequant client
def standardize_hold_ax_onequant(df:pd.DataFrame):
    df = df.rename(columns={
        '代码'     :      'ticker',
        '当前数量'  :      'hold_shares',
        '持仓量'    :      'hold_shares',
        '可用数量'  :      'available_shares',
        '当前市值'  :      'hold_value',
        '市值'      :      'hold_value',
    })

    df['ticker'] = df['ticker'].astype(int)
    df = df[df['hold_shares'] != 0]
    # df = df[['ticker', 'hold_shares', 'available_shares', 'hold_value']]
    df = df[['ticker', 'hold_shares', 'hold_value']]
    df.columns = ['代码', '持仓量', '市值']
    return df

# standardize order for anxin onequant client
def standardize_order_ax_onequant(df:pd.DataFrame, date, **kwargs):
    df = df.rename(columns={
        '订单时间' :      'time',    # 改
        '代码'     :      'ticker',
        '交易方向'  :     'BS_flag',   # 改
        '委托数量'  :     'order_volume',
        '订单状态'  :     'order_status',  # 改
        
        '委托价格'  :     'order_price',
        '成交数量'  :     'fill_volume',
        '成交均价'  :     'fill_price',
        
        '成交金额'  :     'fill_amount',
        
        '订单编号'  :     'order_ref',
        '订单日期'  :     'date',   # 改
        '算法'      :     'algorithm',   # 用于自定义算法字段
        # 没有 remark
    })
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H%M%S'])
    df.sort_values('time', inplace=True)
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    df['date'] = df['date'].astype(str)
    
    if df["ticker"].dtype == "int64":
        df["ticker"] = df["ticker"].apply(lambda x: str(x).zfill(6))
    
    df = df[(df['BS_flag'] == '买入') | (df['BS_flag'] == '卖出')]
    df['order_status'] = df['order_status'].apply(lambda x : _replace_order_status_ax_onequant(x))
    if df['order_ref'].dtype != 'object':
        raise TypeError('委托编号/order_ref should be str type')
    df = df[df['order_ref'] != '']
    if 'algorithm' not in df.columns:
        df['algorithm'] = ''
    df.reset_index(drop=True, inplace=True)
    df = df[["date", 
                   "time", 
                   "order_ref", 
                   "ticker", 
                   "BS_flag", 
                   "order_price", 
                   "order_volume", 
                   "fill_price", 
                   "fill_volume",
                   "order_status", 
                   "algorithm"]]
    df.columns = ['委托日期',
                    '委托时间',
                    '合同编号',
                    '代码',
                    '买卖标记',
                    '委托价格',
                    '委托数量',
                    '成交均价',
                    '成交数量',
                    '委托状态',
                    '算法']
    return df   



# standardize deal for anxin onequant client
def standardize_deal_ax_onequant(df, date, **kwargs):
    df = df.rename(columns={
        '成交日期'      :       'date',
        '成交时间'      :       'time',
        '代码'         :       'ticker',
        '交易方向'      :     'BS_flag',   # 改
        '成交数量'  :     'fill_volume',
        '成交均价'  :     'fill_price',
        
        '成交金额'  :     'fill_amount',
        '订单编号'  :     'order_ref',
        '成交编号'  :     'deal_ref',
        
        '算法'      :     'algorithm',   # 自定义算法字段
    })
    
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H%M%S'])
    df.sort_values('time', inplace=True)
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    df['date'] = df['date'].astype(str)
    if df["ticker"].dtype == "int64":
        df["ticker"] = df["ticker"].apply(lambda x: str(x).zfill(6))
    
    df = df[(df['BS_flag'] == '买入') | (df['BS_flag'] == '卖出')]
    if df['order_ref'].dtype != 'object':
        raise TypeError('委托编号/order_ref should be str type')
    if df['deal_ref'].dtype != 'object':
        raise TypeError('成交编号/order_ref should be str type')
    df = df[df['order_ref'] != '']
    if 'algorithm' not in df.columns:
        df['algorithm'] = ''
    df.reset_index(drop=True, inplace=True)
    df = df[[
        "date", 
        "time", 
        "order_ref",
        "deal_ref", 
        "ticker",
        "BS_flag", 
        "fill_price", 
        "fill_volume",
        "algorithm"
    ]]
    df.columns = [
        "成交日期",
        '成交时间',
        '合同编号',
        '成交编号',
        '代码',
        '买卖标记',
        '成交均价',
        '成交数量',
        '算法'
    ]
    return df
        
        


def _replace_order_status_ax_onequant(flag):
    if flag == '已成':
        return '已成'
    elif flag == '全撤':
        return '已撤'
    elif flag == '已报':
        return '已报'
    elif flag == '部成部撤':
        return '部成'
    elif flag == '拒绝':
        return '废单'
    else:
        raise ValueError(f'委托状态:  \n{flag} \n不在范围中')