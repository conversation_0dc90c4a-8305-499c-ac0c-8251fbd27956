# different types of csv or excel files come as input data, they have similar formats, but some are different
# they can be identified as client type name
# the columns may have date included, the name can date/datetime/日期, or something else
# and other columns, for instance, "Buy/Sell", the enum value can be Buy/Sell/买/卖,
# I want to standardize the input dataframe, output uniformatted file for different type accounts

# 根据输出需求
# standardize_hold_kafang_atgo
# standardize_ordder_kafang_atgo
# standardize_deal_kafang_atgo

import pandas as pd

def standardize_hold_kafang_atgo(df:pd.DataFrame, **kwargs):
    df = df.rename(columns={
        'Symbol': 'ticker',
        'CurrentQ': 'hold_shares',
        'EnableQ': 'available_shares',
    })
    df[['ticker', 'exchange']] = df['ticker'].str.split('.', expand=True)
    df['ticker'] = df['ticker'].astype(int)
    df = df[['ticker', 'hold_shares', 'available_shares']]
    return df



def standardize_order_kafang_atgo(df:pd.DataFrame, date, **kwargs):
    pass



def standardize_deal_kafang_atgo(df:pd.DataFrame, date, **kwargs):
    pass