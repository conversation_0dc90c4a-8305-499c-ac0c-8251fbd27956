import pandas as pd

from misc.utils import to_datetime_formmats


def standardize_wuxianyi_deal(df: pd.DataFrame, **kwargs):
    df = df.rename(columns={
        'TradingDay'           : 'date',
        'TradeTime'       :  'time',
        'InstrumentID'     : 'ticker',
        
        # '交易'             : 'BS_flag',
        
        'Direction'       : 'BS_flag',
        
        'OffsetFlag'    :  'operation',
        
        # '委托量'         : 'order_volume',
        'TradeVolume'         : 'fill_volume',
        # '委托价格'       : 'order_price',
        'TradePrice'       : 'fill_price',
        # '状态'           : 'order_status',
        
        # '成交额'         : 'fill_amount',
        'OrderSysID'         : 'order_ref',
        'TradeID'        : 'deal_ref',
        # '算法ID'         : 'algorithm',
    })
    df['date'] = to_datetime_formmats(df['date'], source_type='date', try_formats=['%Y%m%d'])
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H:%M:%S'])
    df['date'] = df['date'].apply(lambda x: x.strftime("%Y%m%d"))
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    # df['time'] = df['时间'].apply(lambda x: x.strftime("%H:%M:%S"))

    # if df['ticker'].dtype == 'int64':
    #     df['ticker'] = df['ticker'].apply(lambda x: str(x).zfill(6))
    
    df['_开仓标记'] = df['BS_flag'].astype(str) + df['operation'].astype(str)
    df['longshort'] = df['_开仓标记'].apply(_replace_wuxianyi_longshort_flag)
    df['operation'] = df['operation'].apply(lambda x: _replace_wuxianyi_operation_flag(str(x)))
    
    df = df[df['fill_volume'] != 0]
    df['algorithm'] = ''
    
    df['deal_ref'] = df['order_ref']
    df['file_type'] = 'real'
    df = df[["date", 
             "time", 
             "order_ref",
             "deal_ref", 
             "ticker",
             "operation",
             "longshort",
             "fill_price", 
             "fill_volume",
             "file_type",
             "algorithm"]].sort_values("time")
    return df


def _replace_wuxianyi_longshort_flag(flag):
    if flag == '00' or flag == '11':
        flag = '多头'
    elif flag == '01' or flag == '10':
        flag = '空头'
    else:
        raise ValueError('买卖标记:  \n{} \n不在范围中'.format(flag))
    return flag

def _replace_wuxianyi_operation_flag(flag):
    if flag == '0':
        flag = '开仓'
    elif flag == '1':
        flag = '平仓'
    else:
        raise ValueError('开平标记:  \n{} \n不在范围中'.format(flag))
    return flag