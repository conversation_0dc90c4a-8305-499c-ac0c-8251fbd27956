import pandas as pd


from misc.utils import to_datetime_formmats

def standardize_hold_kafangalgo_dbf(df: pd.DataFrame):
    df = df.rename(columns={
        'Symbol'        : 'ticker',
        'CurrentQ'      : 'hold_shares',
        'EnableQty'     : 'available_shares',
    })
    if df['ticker'].dtype == 'object' and (not df.empty):
        if (df['ticker'].str.endswith('.SZ') | df['ticker'].str.endswith('.SH')).any():
            df[['ticker', 'exchange']] = df['ticker'].str.split('.', expand=True)
    # if df['ticker'].dtype == 'int':
    #     df['ticker'] = df['ticker'].apply(lambda x: str(x).zfill(6))
    # df[['ticker', 'exchange_code']] = df['ticker'].str.split('.', expand=True)
    df = df[['ticker', 'hold_shares']]
    df.columns = ['代码', '持仓量']
    return df
    
        
def standardize_order_kafangalgo_dbf(df: pd.DataFrame, date, **kwargs):
    df = df.rename(columns={
        'TransTime'     : 'time',
        'Symbol'        : 'ticker',
        'Side'          : 'BS_flag',
        'OrderQty'      : 'order_volume',
        'OrdStatus'     : 'order_status',
        'Price'         : 'order_price',
        'CumQty'        : 'fill_volume',
        'AvgPx'         : 'fill_price',
        'OrderId'       : 'order_ref',
        'date'          : 'date',
        '算法'                  : 'algorithm',
    })
    df['time'] = to_datetime_formmats(df['time'], source_type='time')
    df.sort_values('time', inplace=True)
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    if len(df) > 0:
        df[['ticker', 'exchange_code']] = df['ticker'].str.split('.', expand=True)
        df['BS_flag'] = df['BS_flag'].apply(lambda x: _replace_bs_flag_kafangalgo(x))
    df = df[(df['BS_flag'] == '买入') | (df['BS_flag'] == '卖出')]
    df['order_status'] = df['order_status'].apply(lambda x : _replace_order_status_kafangalgo(x))
    if df['order_ref'].dtype != 'object':
        raise TypeError('委托编号/order_ref should be str type')
    df = df[df['order_ref'] != '']
    df['date'] = date
    mask_should_be_partially_filled = (df['fill_volume'] != 0) & (df['order_status'] == '已撤')
    df.loc[mask_should_be_partially_filled, 'order_status'] = '部成'
    df = df.reset_index()

    if 'algorithm' not in df.columns:
        df['algorithm'] = ''
    df = df[["date", 
                   "time", 
                   "order_ref", 
                   "ticker", 
                   "BS_flag", 
                   "order_price", 
                   "order_volume", 
                   "fill_price", 
                   "fill_volume",
                   "order_status", 
                   "algorithm"]]
    df.columns = ['委托日期',
                    '委托时间',
                    '合同编号',
                    '代码',
                    '买卖标记',
                    '委托价格',
                    '委托数量',
                    '成交均价',
                    '成交数量',
                    '委托状态',
                    '算法']
    return df   

def standardize_deal_kafangalgo_dbf(df: pd.DataFrame, date, **kwargs):
    df = df.rename(columns={
        'TransTime'     : 'time',
        'TransactTime'     : 'time',
        'Symbol'        : 'ticker',
        'Side'          : 'BS_flag',
        'OrderQty'      : 'order_volume',
        'OrdStatus'     : 'order_status',
        'Price'         : 'order_price',
        'CumQty'        : 'fill_volume',
        'AvgPx'         : 'fill_price',
        'OrderId'       : 'order_ref',
        'date'          : 'date',
        '算法'          : 'algorithm',
    })
    df['time'] = to_datetime_formmats(df['time'], source_type='time')
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    if df['ticker'].dtype == 'object' and (not df.empty):
        df[['ticker', 'exchange_code']] = df['ticker'].str.split('.', expand=True)
    df['BS_flag'] = df['BS_flag'].apply(lambda x: _replace_bs_flag_kafangalgo(x))
    # print(df.head(2))
    df = df[(df['BS_flag'] == '买入') | (df['BS_flag'] == '卖出')]
    df['order_status'] = df['order_status'].apply(lambda x : _replace_order_status_kafangalgo(x))
    df['date'] = date
    if df['order_ref'].dtype != 'object':
        raise TypeError('委托编号/order_ref should be str type')
    df = df[df['order_ref'] != '']
    df.sort_values('time', inplace=True)
    # df['deal_ref'] = 'deal_' + pd.Series([str(x) for x in range(1, len(df)+1)])
    df['deal_ref'] = df['order_ref']
    df = df[df['fill_volume'] != 0]
    df = df.reset_index()
    if 'algorithm' not in df.columns:
        df['algorithm'] = ''
    df = df[[
        "date", 
        "time", 
        "order_ref",
        "deal_ref", 
        "ticker",
        "BS_flag", 
        "fill_price", 
        "fill_volume",
        "algorithm"
    ]]
    df.columns = [
        "成交日期",
        '成交时间',
        '合同编号',
        '成交编号',
        '代码',
        '买卖标记',
        '成交均价',
        '成交数量',
        '算法'
    ]
    return df
    
    
def _replace_bs_flag_kafangalgo(flag):
    if flag in [1,]:
        flag = '买入'
    elif flag in [2,]:
        flag = '卖出'
    elif flag in [-1,]:
        flag = '无效'
    else:
        raise ValueError('买卖标记:  \n{} \n不在范围中'.format(flag))
    return flag


def _replace_order_status_kafangalgo(status):
    if status in [0]:
        status = '已报'
    elif status in [1]:
        status = '部成'
    elif status in [2]:
        status = '已成'
    elif status in [4]:
        status = '已撤'
    elif status in [6]:
        status = '待撤'
    elif status in [8, 7]:
        status = '废单'
    elif status in [10]:
        status = '等待报单'
    else:
        raise ValueError('委托状态:  \n{} \n不在范围中'.format(status))
    return status
