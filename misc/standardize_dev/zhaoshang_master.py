from .stand_utils import std_filter_ticker_asint
from misc.utils import to_datetime_formmats

def std_hold(df):
    
    df = df.rename(columns={
        '交易对代码'      : 'ticker',
        '持仓数量'        : 'volume',
        '可平数量'        : 'available_volume',
    })
    # ticker 000001.SZ -> 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    
    if not df.empty:
        df['tmp_direct'] = df['持仓方向'].apply(lambda x: 1 if x == '多头' else -1)
        df['volume'] = df['volume'].mul(df['tmp_direct'])
        df['available_volume'] = df['available_volume'].mul(df['tmp_direct'])
    
    return df


def std_order(df, date, **kwargs):
    df = df.rename(columns={
        '交易对代码'   :     'ticker',  ###
        '委托方向'     :     'BS_flag',  # 和标准一样, 买入 卖出
        '价格'        :    'order_price',
        '委托数量'     :    'order_volume',
        '成交均价'   :    'fill_price',
        '成交数量'   :    'fill_volume',
        
        '委托日期'   :    'date',
        '委托时间'  :     'time',   ###
        '委托状态'   :    'order_status',  ###
        '委托编号'  :     'order_ref',
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y%m%d %H:%M:%S.%f']).apply(lambda x: x.strftime("%H:%M:%S"))
    # order_status
    df['order_status'] = df['order_status'].apply(_replace_order_status)
    return df

def std_deal(df, date, **kwargs):
    df = df.rename(columns={
        '交易对代码'   :     'ticker',  ###
        '委托方向'     :     'BS_flag',  # 和标准一样, 买入 卖出
        '价格'      :    'fill_price',
        '成交数量'   :    'fill_volume',
        
        '成交日期'   :    'date',
        '成交时间'  :     'time',   ###
        # order_ref      没有, 复制
        '成交编号'  :     'deal_ref',
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y%m%d %H:%M:%S.%f']).apply(lambda x: x.strftime("%H:%M:%S"))
    # order_ref
    df['order_ref'] = df['deal_ref']
    return df

def std_deal_minimal(df, date, **kwargs):
    df = df.rename(columns={
        '交易对代码'   :     'ticker',  ###
        '委托方向'     :     'BS_flag',  # 和标准一样, 买入 卖出
        '价格'      :    'fill_price',
        '成交数量'   :    'fill_volume',
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    return df

def std_account(df, date, **kwargs):
    df = df.rename(columns={
        # '日期'        :     'date',    ###
        '可用保证金'     :     'available_margin',
        # ''     :     'available_fund',
        # 没有可用资金字段, 有股票市值, 期货市值字段
    })
    df['date'] = date
    return df

def _replace_order_status(flag):
    if flag == '已成':
        return '已成'
    elif flag == '已撤':
        return '已撤'
    elif flag == '已报':
        return '已报'
    elif flag == '部撤':
        return '部成'
    elif flag == '被拒':
        return '废单'
    else:
        raise ValueError(f'委托状态:  \n{flag} \n不在范围中')
    