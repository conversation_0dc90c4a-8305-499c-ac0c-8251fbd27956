from .stand_utils import std_filter_ticker_asint
from misc.utils import to_datetime_formmats

def std_hold(df):
    
    df = df.rename(columns={
        'Symbol'        : 'ticker',
        'CurrentQ'      : 'volume',
        'EnableQty'     : 'available_volume',
    })
    
    # ticker 000001.SZ -> 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')

    return df


def std_order(df, date, **kwargs):   # SubOrderAlgo
    # 特殊处理, 华鑫极星客户端 order
    if '预估印花税' in df.columns and '预估经手费' in df.columns:
        df = df.rename(columns={
                '时间'     : 'time',    ###   ok
                '代码'        : 'ticker',  ###
                '方向'          : 'BS_flag', ###
                '状态'     : 'order_status', ###
                '委托量'      : 'order_volume',
                '委托价'         : 'order_price',
                '已成交数量'        : 'fill_volume',
                # ''         : 'fill_price',   # 转换
                '委托编号'       : 'order_ref',
                # '日期'          : 'date',  # as str
        })
        df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
        df['BS_flag'] = df['BS_flag'].apply(_replace_jixin_BS_flag)
        df['order_status'] = df['order_status'].apply(_replace_jixin_order_status)
        df['date'] = date
        deal_order = df['fill_volume'] != 0
        df.loc[deal_order, 'fill_price'] = df.loc[deal_order, '成交金额'].div(df.loc[deal_order, 'fill_volume'])
        # df.fillna({'order_ref'})
        df.fillna(0, inplace=True)
        df.replace({'order_ref': '\t'}, 'no_orderid', inplace=True)
        
        return df
    
    else:
    
        df = df.rename(columns={
                'TransTime'     : 'time',    ###
                'Symbol'        : 'ticker',  ###
                'Side'          : 'BS_flag', ###
                'OrdStatus'     : 'order_status', ###
                'OrderQty'      : 'order_volume',
                'Price'         : 'order_price',
                'CumQty'        : 'fill_volume',
                'AvgPx'         : 'fill_price',
                'ClOrdId'       : 'order_ref',
                # 'date'          : 'date',
        })
        # ticker 000001.SZ
        df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
        # time
        df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y%m%d%H%M%S%f']).apply(lambda x: x.strftime("%H:%M:%S"))
        # order_status
        df['order_status'] = df['order_status'].apply(_replace_order_status)
        # BS_flag
        df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
        # date 
        df['date'] = date
        # adjust 部成
        mask_should_be_partially_filled = (df['fill_volume'] != 0) & (df['order_status'] == '已撤')
        df.loc[mask_should_be_partially_filled, 'order_status'] = '部成'
        return df

def std_deal(df, date, **kwargs):    # SubOrderAlgo
    # 特殊处理, 华鑫极星客户端 order
    if '股东代码' in df.columns and '客户号' in df.columns:
        df = df.rename(columns={
                '代码'        : 'ticker',  ###
                '方向'          : 'BS_flag', ###

                '成交量'        : 'fill_volume',
                '成交价'         : 'fill_price',  
                '委托编号'       : 'order_ref',
                '成交编号'       : 'deal_ref',
                '时间'          : 'time',    ###   ok
                '日期'          : 'date',  # as str
        })
        df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
        df['BS_flag'] = df['BS_flag'].apply(_replace_jixin_BS_flag)
        # df['order_status'] = df['order_status'].apply(_replace_jixin_order_status)
        df = df[df['date'].astype(str) == date]
        # df['date'] = date
        # deal_order = df['fill_volume'] != 0
        # df.loc[deal_order, 'fill_price'] = df.loc[deal_order, '成交金额'].div(df.loc[deal_order, 'fill_volume'])
        # df.fillna(0, inplace=True)
        df.fillna(0, inplace=True)
        
        return df
    
    else:
        df = df.rename(columns={
                'Symbol'        : 'ticker',  ###
                'Side'          : 'BS_flag', ###
                'CumQty'        : 'fill_volume',
                'AvgPx'         : 'fill_price',
                
                # 'date'          : 'date',
                'TransTime'     : 'time',    ###
                'ClOrdId'       : 'order_ref',
                # ''     : 'deal_ref',  # 复制一份
        })
        # ticker 000001.SZ
        df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
        # time & date
        df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y%m%d%H%M%S%f'])
        df['date'] = df['time'].apply(lambda x: x.strftime("%Y%m%d"))
        df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
        # BS_flag
        df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
        # deal_ref
        df['deal_ref'] = df['order_ref']
        return df

def std_deal_minimal(df, date, **kwargs):    # SubOrderAlgo
    if '股东代码' in df.columns and '客户号' in df.columns:
        df = df.rename(columns={
                '代码'        : 'ticker',  ###
                '方向'          : 'BS_flag', ###

                '成交量'        : 'fill_volume',
                '成交价'         : 'fill_price', 
                '日期'          : 'date',  # as str
        })
        # ticker 000001.SZ
        df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
        # BS_flag
        df['BS_flag'] = df['BS_flag'].apply(_replace_jixin_BS_flag)
        # date
        df = df[df['date'].astype(str) == date]
        df.drop(columns=['date'], inplace=True)
        df.fillna(0, inplace=True)
        
        return df
    else:
        df = df.rename(columns={
                'Symbol'        : 'ticker',  ###
                'Side'          : 'BS_flag', ###
                'CumQty'        : 'fill_volume',
                'AvgPx'         : 'fill_price',
        })
        # ticker 000001.SZ
        df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
        # BS_flag
        df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
        return df

def std_account(df, date, **kwargs):
    df = df.rename(columns={
        # '日期'        :     'date',    ###
        # '多头可用保证金' :     'available_margin',
        "AssetAmt"     :     'net_asset',
        'EnBalance'     :     'available_fund',
    })
    if len(df) > 1:
        df.loc[0,'available_fund'] = df['available_fund'].sum()
        df = df.iloc[:1].fillna(0)

    if 'net_asset' not in df.columns:
        df['net_asset'] = 0
        
    df['date'] = date
    # df['net_asset'] = 
    return df
    
def _replace_BS_flag(flag):
    if flag in [1,]:
        flag = '买入'
    elif flag in [2,]:
        flag = '卖出'
    elif flag in [-1,]:
        flag = '无效'
    else:
        raise ValueError('买卖标记:  \n{} \n不在范围中'.format(flag))
    return flag

def _replace_order_status(status):
    if status in [2]:
        status = '已成'
    elif status in [1]:
        status = '部成'
    elif status in [4]:
        status = '已撤'
    elif status in [0]:
        status = '已报'
    elif status in [6]:
        status = '待撤'
    elif status in [8, 7, -1]:
        status = '废单'
    elif status in [10]:
        status = '等待报单'
    elif status in [12]:
        status = '过期'
    else:
        raise ValueError('委托状态:  \n{} \n不在范围中'.format(status))
    return status



    
def _replace_jixin_BS_flag(flag):
    if flag in ['买入', ]:
        flag = '买入'
    elif flag in ['卖出', ]:
        flag = '卖出'
    # elif flag in [-1,]:
    #     flag = '无效'
    else:
        raise ValueError('买卖标记:  \n{} \n不在范围中'.format(flag))
    return flag

def _replace_jixin_order_status(status):
    if status in ['全部成交']:
        status = '已成'
    elif status in ['部分成交', '部成', '部分撤单', '部撤', '部成部撤']:
        status = '部成'
    elif status in ['全部撤单']:
        status = '已撤'
    elif status in ['已报', '待成交', '交易所已接收']:
        status = '已报'
    # elif status in [6]:
    #     status = '待撤'
    elif status in ['废单', '交易所已拒绝']:
        status = '废单'
    # elif status in [10]:
    #     status = '等待报单'
    # elif status in [12]:
    #     status = '过期'
    else:
        raise ValueError('委托状态:  \n{} \n不在范围中'.format(status))
    return status