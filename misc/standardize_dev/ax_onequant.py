from misc.utils import to_datetime_formmats, re_find
from .stand_utils import std_filter_ticker_asint
# import pandas as pd


def std_hold(df):
    df.dropna(subset=['账号',], inplace=True)
    df.rename(columns={
        # '证券代码'     :     'ticker',
        '代码'        :      'ticker',
        
        '当前数量'     :     'volume',
        # '持仓量'       :     'volume',
        
        '可用数量'     :     'available_volume',
        # '可用余额'     :     'available_volume',
        
    }, inplace=True)
    # ticker 000001
    # print(df.dtypes)
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    df['volume'] = df['volume'].astype(int)
    df['available_volume'] = df['available_volume'].astype(int)
    # print(remainder)
    
    return df

    
def std_order(df, date, **kwargs):
    df.dropna(subset=['账号',], inplace=True)
    df.rename(columns={
        '代码'     :     'ticker',  ###
        '交易方向'  :     'BS_flag',
        '委托价格'   :    'order_price',
        '委托数量'   :    'order_volume',
        '成交均价'   :    'fill_price',
        '成交数量'   :    'fill_volume',
        
        '订单日期'   :    'date',
        '订单时间'  :     'time',   ###
        '订单状态'   :    'order_status',  ###
        '订单编号'  :     'order_ref',
    }, inplace=True)
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H%M%S']).apply(lambda x: x.strftime("%H:%M:%S"))
    # date
    # df['date'] = df['date'].astype(str)
    # order_status
    df['order_status'] = df['order_status'].apply(_replace_order_status)
    # 添加T0算法标记
    # df['策略'].fillna('', inplace=True)
    # df.loc[ df['策略'].map(lambda x: re_find(r'T0', x)).astype(bool), 'algorithm' ] = 'T0'
    # 修正数据错误
    correct_fill_price_mask = (df['fill_volume'] > 0) & (df['fill_price'] == 0)
    df.loc[correct_fill_price_mask, 'fill_price'] = df.loc[correct_fill_price_mask, 'order_price']
    return df

def std_deal(df, date, **kwargs):
    df.dropna(subset=['账号',], inplace=True)
    df.rename(columns={
        '代码'     :     'ticker',  ###
        '交易方向'  :     'BS_flag',  # 和标准一致
        '成交均价'   :    'fill_price',
        '成交数量'   :    'fill_volume',
        
        '成交日期'   :    'date',  # 和标准一致
        '成交时间'  :     'time',   ###
        '订单编号'  :     'order_ref',
        '成交编号'  :     'deal_ref',
    }, inplace=True)
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H%M%S']).apply(lambda x: x.strftime("%H:%M:%S"))
    # 添加T0算法标记
    # df['策略'].fillna('', inplace=True)
    # df.loc[ df['策略'].map(lambda x: re_find(r'T0', x)).astype(bool), 'algorithm' ] = 'T0'
    return df
    
def std_deal_minimal(df, date, **kwargs):
    df.dropna(subset=['账号',], inplace=True)
    df.rename(columns={
        '代码'     :     'ticker',  ###
        '交易方向'  :     'BS_flag',  # 和标准一致
        '成交均价'   :    'fill_price',
        '成交数量'   :    'fill_volume',
    }, inplace=True)
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    return df
    
def std_account(df, date, **kwargs):
    df.dropna(subset=['账号',], inplace=True)
    df.rename(columns={
        # '日期'        :     'date',    ###
        '总资产'       :     'net_asset',
        '股票市值'     :     'stock_value',
        '可用余额'     :     'available_fund',
        # pnl(client)   ###
    }, inplace=True)
    df['date'] = date
    return df
    


def _replace_order_status(flag):
    if flag == '已成':
        return '已成'
    elif flag == '全撤':
        return '已撤'
    elif flag == '已报':
        return '已报'
    elif flag == '部成部撤':
        return '部成'
    elif flag == '拒绝':
        return '废单'
    else:
        raise ValueError(f'委托状态:  \n{flag} \n不在范围中')
    
# def _replace_BS_flag(flag):
#     if flag == '买入':
#         return '买入'
#     elif flag == '卖出':
#         return '卖出'
#     else:
#         raise ValueError('{} 不是有效的买卖标记'.format(flag))