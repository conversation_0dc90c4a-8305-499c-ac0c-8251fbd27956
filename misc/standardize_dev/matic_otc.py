from misc.utils import to_datetime_formmats, re_find
from .stand_utils import std_filter_ticker_asint
import re


def std_hold(df):
    df = df.rename(columns={
        '证券代码'     :     'ticker',
        # '代码'        :      'ticker',
        '总持仓'     :     'volume',
        '可用持仓'     :     'available_volume',
        
    })
    # ticker 000001.SZ
    # print(df.dtypes)
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # print(remainder)
    
    return df

    
def std_order(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'  :     'ticker',  ###
        '买卖方向'  :     'BS_flag',  ###
        # '委托价格'   :    'order_price', ###
        '委托数量'   :    'order_volume',
        '成交均价'   :    'fill_price',  ### 转换 CNY
        '累计成交数量' :   'fill_volume',
        
        # '订单日期'   :    'date',   ###
        '委托时间'  :     'time',   ###
        '订单状态'   :    'order_status',  ###
        '订单编号'  :     'order_ref',
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # time
    df['time_tmp'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y/%m/%d %H:%M:%S'])
    df = df[df['time_tmp'] >= date]
    df['time'] = df['time_tmp'].map(lambda x: x.strftime("%H:%M:%S"))
    # date
    df['date'] = df['time_tmp'].map(lambda x: x.strftime("%Y%m%d"))
    df.drop(columns=['time_tmp'], inplace=True)
    # order_status
    df.loc[(df['fill_volume'] > 0) & (df['order_status'] == '停止'), 'order_status'] = '部成'
    df.loc[(df['fill_volume'] == 0) & (df['order_status'] == '停止'), 'order_status'] = '已撤'
    df['order_status'] = df['order_status'].map(_replace_order_status)
    # BS_flag
    df['BS_flag'] = df['BS_flag'].map(_replace_BS_flag)
    # 修正
    # 母单没有委托价格, 用成交价替代
    df['fill_price'] = df['fill_price'].map(_convert_to_number)
    df['order_price'] = df['fill_price']
    return df

def std_deal(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'   :    'ticker',  ###
        '买卖方向'  :     'BS_flag',  ###
        '成交均价'   :    'fill_price',  ### 转换 CNY
        '累计成交数量'  :  'fill_volume',
        
        # '日期'   :    'date',    ###
        '委托时间'  :     'time',   ###
        '订单编号'  :     'order_ref',
        # '成交编号'  :     'deal_ref',  ###
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # BS_flag
    df['BS_flag'] = df['BS_flag'].map(_replace_BS_flag)
    # time
    df['time_tmp'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y/%m/%d %H:%M:%S'])
    df = df[df['time_tmp'] >= date]
    df['time'] = df['time_tmp'].map(lambda x: x.strftime("%H:%M:%S"))
    # date
    df['date'] = df['time_tmp'].map(lambda x: x.strftime("%Y%m%d"))
    df.drop(columns=['time_tmp'], inplace=True)
    # deal_ref, 只有母单订单编号, deal_ref 用母单订单编号
    df['deal_ref'] = df['order_ref']
    # 修正 fill_price CNY
    df['fill_price'] = df['fill_price'].map(_convert_to_number)
    
    return df
    
def std_deal_minimal(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'  :     'ticker',  ###
        '买卖方向'  :     'BS_flag',  ###
        '成交均价'     :  'fill_price',
        '累计成交数量'  :  'fill_volume',

        '委托时间'  :     'time',   ###
    })
    df['time_tmp'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y/%m/%d %H:%M:%S'])
    df = df[df['time_tmp'] >= date]
    df.drop(columns=['time_tmp'], inplace=True)
    
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # BS_flag
    df['BS_flag'] = df['BS_flag'].map(_replace_BS_flag)
    # 修正 fill_price CNY
    df['fill_price'] = df['fill_price'].map(_convert_to_number)

    return df

def std_account(df, date, **kwargs):
    df = df.iloc[[0]]  # 取第一行
    # print(df)
    df = df.rename(columns={
        '总可用额度'  :  'available_fund',
    })
    df['date'] = date
    # print(df['available_fund'])
    # print(df['available_fund'].dtype)
    # print('-------------------------')
    # print(df)
    # print('-------------------------')
    df['available_fund'] = df['available_fund'].map(_convert_to_number)
    df['available_margin'] = df['available_fund']
    return df
    
    

def _replace_order_status(flag):
    if flag == '全成':
        return '已成'
    elif flag in ['全撤', '已撤']:
        return '已撤'
    elif flag == '已报':
        return '已报'
    elif flag in ['部成', '部撤']:
        return '部成'
    elif flag in ['拒绝', '废单']:
        return '废单'
    else:
        raise ValueError(f'委托状态:  \n{flag} \n不在范围中')
    
def _replace_BS_flag(flag):
    if flag == '买入':
        return '买入'
    elif flag == '卖出':
        return '卖出'
    elif flag == '卖空':
        return '卖空'
    elif flag == '平空':
        return '平空'
    else:
        raise ValueError('{} 不是有效的买卖标记'.format(flag))

def _convert_to_number(price):
    return float(re.sub(r'[^\d.]', '', price))