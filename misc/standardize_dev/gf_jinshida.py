from misc.utils import to_datetime_formmats
from .stand_utils import std_filter_ticker_asint

def std_hold(df):
    df = df.rename(columns={
        '标的代码'        : 'ticker',
        '持仓数量'        : 'volume',
        '可用持仓数量'     : 'available_volume',
    })
    
    # ticker 000001.SZ -> 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    
    if not df.empty:
        # 有IC, 在总函数中过滤
        df['tmp_direct'] = df['多空方向'].apply(lambda x: 1 if x == '看多' else -1)
        df['volume'] = df['volume'].mul(df['tmp_direct'])
        df['available_volume'] = df['available_volume'].mul(df['tmp_direct'])
    return df


def std_order(df, date, **kwargs):   # OrderList.csv
    df = df.rename(columns={
        '标的代码'     :  'ticker',  ###
        # '交易方向'     :  'BS_flag', ###  构造
        
        '意向价格'      :    'order_price',
        '意向名义数量'   :    'order_volume',
        # '成交价格'   :    'fill_price',  ###
        '成交数量'   :    'fill_volume',
        '成交金额'   :    'fill_value',

        
        '交易日期'   :    'date',    ###
        '委托时间'  :     'time',    ###
        '意向状态'   :    'order_status', ###
        '意向编号'  :     'order_ref',
    })
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # date
    df['date'] = to_datetime_formmats(df['date'], source_type='date', try_formats=['%Y/%m/%d']).apply(lambda x: x.strftime("%Y%m%d"))
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H:%M:%S.%f']).apply(lambda x: x.strftime("%H:%M:%S"))
    # fill_price
    df['fill_price'] = df['fill_value'].div(df['fill_volume'], fill_value=0).round(2)
    df['fill_price'] = df['fill_price'].fillna(0)
    # order_status
    df['order_status'] = df['order_status'].apply(_replace_order_status)
    # BS_flag
    df['tmp_direction'] = df['多空方向'] + (df['开平标志'])
    df['BS_flag'] = df['tmp_direction'].apply(_replace_BS_flag)    
    return df

def std_deal(df, date, **kwargs):   # TradeList.csv
    df = df.rename(columns={
        '标的代码'     :  'ticker',  ###
        # '交易方向'     :  'BS_flag', ### 构造       
        '成交价格'   :    'fill_price',
        '成交数量'   :    'fill_volume',
        
        # '交易日期'   :    'date',    ###
        '成交时间'  :     'time',    ###
        '意向编号'  :     'order_ref',
        '成交编号'  :     'deal_ref'
    })
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # date
    df['date'] = date
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H:%M:%S.%f']).apply(lambda x: x.strftime("%H:%M:%S"))
    # BS_flag
    df['tmp_direction'] = df['多空方向'] + (df['开平标志'])
    df['BS_flag'] = df['tmp_direction'].apply(_replace_BS_flag)    
    
    # filter today
    df['tmp_date'] = df['order_ref'].apply(lambda x: str(x)[:8])
    df = df[df['tmp_date'] == date]
    
    return df

def std_deal_minimal(df, date, **kwargs):   # TradeList.csv
    df = df.rename(columns={
        '标的代码'     :  'ticker',  ###
        # '交易方向'     :  'BS_flag', ### 构造       
        '成交价格'   :    'fill_price',
        '成交数量'   :    'fill_volume',
    })
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    df['tmp_direction'] = df['多空方向'] + (df['开平标志'])
    df['BS_flag'] = df['tmp_direction'].apply(_replace_BS_flag)    
    return df

def std_account(df, date, **kwargs):
    df = df.rename(columns={
        # '日期'        :     'date',    ###
        '多头可用保证金'   :     'available_margin',
        '多头可用额度'     :     'available_fund',
    })
    df['date'] = date
    return df
    
def _replace_BS_flag(flag):
    if flag in ['看多开仓', '看空平仓']:
        return '买入'
    elif flag in ['看多平仓', '看空开仓']:
        return '卖出'
    else:
        raise ValueError('交易方向标志 {} 不在程序范围内'.format(flag))


def _replace_order_status(flag):
    if flag in ['全部成交']:
        return '已成'
    elif flag in ['部成[终]', '部成[中]']:
        return '部成'
    elif flag in ['已撤']:
        return '已撤'
    elif flag in ['废单']:
        return '废单'
    elif flag in ['待成交']:
        return '已报'
    else:
        raise ValueError('委托状态标志 {} 不在程序范围内'.format(flag))