from .stand_utils import std_filter_ticker_asint
from ..utils import to_datetime_formmats

def std_hold(df):
    df = df.rename(columns={
        '证券代码'        : 'ticker',
        '持仓数量'        : 'volume',
        '可用数量'        : 'available_volume',
    })
    # ticker 000001
    # 合并多账号
    df = df[['ticker', 'volume', 'available_volume']].groupby(['ticker']).sum().reset_index()
    
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    return df


def std_order(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'     :  'ticker',  ###
        '交易业务'     :  'BS_flag', ####
        
        '委托价格'   :    'order_price',
        '委托数量'   :    'order_volume',
        '成交均价'   :    'fill_price',
        # '已成交数量'   :    'fill_volume',  # for AlgoSuborrder.csv 文件
        '成交数量 '   :    'fill_volume',      # for Suborder.csv 文件

        
        '委托日期'   :    'date',    
        '委托时间'  :     'time',    ###
        # '委托状态'   :    'order_status', ###  # for AlgoSuborrder.cs 
        '交易状态'   :    'order_status', ###    # for Suborder.csv 文件   
        # '委托编号'  :     'order_ref',
        '合同序号'  :     'order_ref',
    })
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # date
    df['date'] = df['date'].astype(str)
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y/%m/%d %H:%M:%S']).apply(lambda x: x.strftime("%H:%M:%S"))
    # order_status
    df['order_status'] = df['order_status'].apply(_replace_order_status)
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # fill na value order_ref
    df['order_ref'] = df['order_ref'].fillna('feidan')
    return df
    
import pandas as pd
def std_account(df:pd.DataFrame, date:str, **kwargs):
    df = df.rename(columns={
        # '日期'        :     'date',    ###
        '资产总值'       :     'net_asset',
        '参考市值'       :     'stock_value',
        '可用金额'       :     'available_fund',
        # ''   :    'pnl(client)'
    })
    # 合并多账号
    df = df.sum(axis=0).to_frame().T
    
    df['date'] = date
    return df

def std_deal(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'     :  'ticker',  ###
        '交易业务'     :  'BS_flag', ####
        '成交价格'   :    'fill_price',  ## 过滤 price 0, volume < 0 的未成交单
        '成交数量'   :    'fill_volume',
        '成交数量 '   :   'fill_volume',  # gtrde 老版本的 小bug, 字段名多了空格

        
        '委托日期'   :    'date',    
        '成交时间'  :     'time',    ###
        # '委托编号'  :     'order_ref',
        '合同序号'  :     'order_ref',
        '成交编号'  :     'deal_ref' 
    })
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # date
    df['date'] = df['date'].astype(str)
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y/%m/%d %H:%M:%S']).apply(lambda x: x.strftime("%H:%M:%S"))
    # BS_flag
    # for gtrade old version
    if 'BS_flag' not in df.columns:
        df['BS_flag'] = df['买卖标记'].apply(_replace_BS_flag)
    else:
        df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # 过滤未成交
    df = df[~((df['fill_price'] == 0) & (df['fill_volume'] < 0))]
    return df
    
def std_deal_minimal(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'     :  'ticker',  ###
        '交易业务'     :  'BS_flag', ####
        '成交价格'   :    'fill_price',  ## 过滤 price 0, volume < 0 的未成交单
        '成交数量'   :    'fill_volume',
        '成交数量 '   :   'fill_volume',  # gtrde 老版本的 小bug, 字段名多了空格
    })
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # BS_flag
    # for gtrade old version
    if 'BS_flag' not in df.columns:
        df['BS_flag'] = df['买卖标记'].apply(_replace_BS_flag)
    else:
        df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # 过滤未成交
    df = df[~((df['fill_price'] == 0) & (df['fill_volume'] < 0))]
    return df



# === futures ==================================================================
    
def std_deal_future(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'     :     'ticker',
        '买卖标记'     :     'BS_flag',
        
        '成交价格'   :    'fill_price',
        '成交数量'    :    'fill_volume',
        '成交数量 '    :    'fill_volume',    # 原数据bug
        
        '委托日期'   :    'date',
        '成交时间'  :     'time',      ###
        '合同序号'  :     'order_ref',
        '成交编号'  :     'deal_ref',
        
    })
    df['date'] = df['date'].astype(str)
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y/%m/%d %H:%M:%S']).apply(lambda x: x.strftime("%H:%M:%S"))
    # df['time'] = df['time'].astype(str)
    df['order_ref'] = df['order_ref'].astype(str)
    df['deal_ref'] = df['deal_ref'].astype(str)
    
    # df['longshort'] = df['longshort'].map(_replace_future_longshort)
    # df['BS_flag'] = df['BS_flag'].map(_replace_BS_flag)
    df['operation'] = df['BS_flag'].map(_replace_future_operation)
    df['longshort'] = df['BS_flag'].map(_replace_future_longshort)

    # 过滤未成交
    df = df[~((df['fill_price'] == 0) & (df['fill_volume'] < 0))]
    return df
    
    
def std_order_future(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'     :     'ticker',

        # '已成交数量'   :    'fill_volume',  # for AlgoSuborrder.csv 文件

        '合同序号'  :     'order_ref',

        '买卖标记'   :    'BS_flag',     # convert
        '委托时间'   :    'time',        # convert
        '交易状态'   :    'order_status', # convert
    
        '委托数量'   :    'order_volume',
        '委托价格'   :    'order_price',
        '成交数量'   :    'fill_volume',
        '成交数量 '   :   'fill_volume',      # for Suborder.csv 文件
        # '成交价格'   :    'fill_price',
    })
    
    # date, time
    df['date'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y-%m-%d %H:%M:%S']).apply(lambda x: x.strftime("%Y%m%d"))
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y-%m-%d %H:%M:%S']).apply(lambda x: x.strftime("%H:%M:%S"))
    
    # order_status
    df['order_status'] = df['order_status'].apply(_replace_order_status)
    # BS_flag
    df['longshort'] = df['BS_flag'].apply(_replace_future_longshort)
    df['operation'] = df['BS_flag'].apply(_replace_future_operation)
    
    # fill fill_price
    df['fill_price'] = df['fill_price'].fillna(0)
    
    # fill na value order_ref
    df['order_ref'] = df['order_ref'].fillna('feidan')
    return df
    
    
def std_account_future(df, date, **kwargs):
    cols_dict = {
        '期初权益'     :     'begin_rights',
        '动态权益'     :     'dynamic_rights',
        '保证金可用'    :     'available_fund',
        '保证金占用'    :    'current_margin',
        'date'       :     'date',
    }
    df = df.rename(columns=cols_dict)
    df['date'] = date
    df = df[cols_dict.values()]
    return df
    
    
    
def _replace_order_status(flag):
    if flag in ['已成交']:
        return '已成'
    elif flag in ['已撤']:
        return '已撤'
    elif flag in ['部撤(部成)', '部分成交']:
        return '部成'
    elif flag in ['废单']:
        return '废单'
    elif flag in ['已报']:
        return '已报'
    elif flag in ['正报']:
        return '等待报单'
    else:
        raise ValueError('委托状态标志 {} 不在程序范围内'.format(flag))
    
    
def _replace_BS_flag(flag):
    if flag in ['证券买入', '证券买入限价委托']:
        return '买入'
    elif flag in ['证券卖出', '证券卖出限价委托']:
        return '卖出'
    else:
        raise ValueError('委托状态标志 {} 不在程序范围内'.format(flag))
    
    
def _replace_future_operation(flag):
    if '买入开仓' in flag or '卖出开仓' in flag:
        return '开仓'
    elif '买入平仓' in flag or '卖出平仓' in flag:
        return '平仓'
    else:
        raise ValueError('unknown future operation flag: {}'.format(flag))
    
def _replace_future_longshort(flag):
    if '买入开仓' in flag or '卖出平仓' in flag:
        return '多头'
    elif '卖出开仓' in flag or '买入平仓' in flag:
        return '空头'
    else:
        raise ValueError('unknown longshort flag from: {}'.format(flag))