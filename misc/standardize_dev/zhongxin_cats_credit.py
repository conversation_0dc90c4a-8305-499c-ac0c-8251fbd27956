import numpy as np
from .stand_utils import std_filter_ticker_asint
from misc.utils import to_datetime_formmats


# def std_hold(df):
#     df = df.rename(columns={
#         '代码'        : 'ticker',
#         '持仓'        : 'volume',
#         '可用持仓'     : 'available_volume',
#         '当前余额'     : 'volume',
#         '可用数'       : 'available_volume',
#     })
#     # ticker 000001
#     df, remainder = std_filter_ticker_asint(df, src_suffix='')   
    
#     if not df.empty and '持仓方向' in df.columns:
#         # df['tmp_direct'] = df['持仓方向'].apply(lambda x: 1 if x == '多头' else -1)
#         df['tmp_direct'] = np.where(df['持仓方向']=='多头', 1, -1)
#         df['volume'] = df['volume'].mul(df['tmp_direct'])
#         df['available_volume'] = df['available_volume'].mul(df['tmp_direct'])
    
#     return df

def std_hold_credit(df):
    df = df.rename(columns={
        '代码'        : 'ticker',
        
        '当前余额'     : 'volume',  # creditPosition
        '可用数'      :  'available_volume'  # creditPosition
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    return df


def std_order(df, date, **kwargs):     # StockOrder
    df = df.rename(columns={
        '代码'     :     'ticker',  ###
        '交易'     :     'BS_flag',  ###
        '委托价'     :    'order_price',
        '委托量'     :    'order_volume',
        '成交均价'   :    'fill_price',
        '成交量'     :    'fill_volume',
        
        # '委托日期'   :    'date',  # 要构造
        '时间'  :     'time',   ###
        '状态'   :    'order_status',  ###
        '订单号'  :    'order_ref',
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # time & date
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y/%m/%d %H:%M:%S'])
    df['date'] = df['time'].apply(lambda x: x.strftime('%Y%m%d'))
    df['time'] = df['time'].apply(lambda x: x.strftime('%H:%M:%S'))
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # order_status
    df['order_status'] = df['order_status'].apply(_replace_order_status)
    return df

def std_deal(df, date, **kwargs):     # StockOrder
    df = df.rename(columns={
        '代码'     :     'ticker',  ###
        '交易'     :     'BS_flag',  ###
        '成交均价'   :    'fill_price',
        '成交量'     :    'fill_volume',
        
        '时间'  :     'time',   ###
        # date  构造
        '订单号'  :    'order_ref',
        # 'deal_ref'    要构造
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # time & date
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y/%m/%d %H:%M:%S'])
    df['date'] = df['time'].apply(lambda x: x.strftime('%Y%m%d'))
    df['time'] = df['time'].apply(lambda x: x.strftime('%H:%M:%S'))
    # 避免出现在错误日期的交易记录
    df = df[df['date'] == date]
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # deal_ref
    df['deal_ref'] = df['order_ref']
    # 过滤成交量为0的记录
    df = df[df['fill_volume'] != 0]
    return df

def std_deal_minimal(df, date, **kwargs):     # StockOrder
    df = df.rename(columns={
        '代码'     :     'ticker',  ###
        '交易'     :     'BS_flag',  ###
        '成交均价'   :    'fill_price',
        '成交量'     :    'fill_volume',
        '时间'     :     'time',   ###
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # 避免出现在错误日期的交易记录
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y/%m/%d %H:%M:%S'])
    df['date'] = df['time'].apply(lambda x: x.strftime('%Y%m%d'))
    df = df[df['date'] == date]
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # 过滤成交量为0的记录
    df = df[df['fill_volume'] != 0]
    return df

def std_account(df, date, **kwargs):
    if '买担保品可用资金' in df.columns:
        df = df.rename(columns={
            # '日期'        :     'date',    ###
            # '余额'        :     'available_margin',
            # '多头余额'     :     'available_fund',
            
            '净资产'        :     'net_asset',
            '买担保品可用资金'    :  'available_fund',
            '可用保证金'         :  'available_margin',
        })
        df.insert(0, 'date', date)
    else:
        df = df.rename(columns={
            # '日期'        :     'date',    ###
            '可用数'        :     'available_fund',
            '账户资产'        :     'net_asset',
            '证券市值'        :     'stock_value',
            '参考盈亏'        :     'pnl(client)',
        })
        df.insert(0, 'date', date)
        # df = df[['date', 'available_fund', 'net_asset', 'stock_value', 'pnl(client)']]
    return df

def _replace_BS_flag(flag):
    if flag in ['买入', '平仓买入', '平空', '买券还券', '融资买入']:
        return '买入'
    elif flag in ['卖出', '开仓卖出', '卖空',  '融券卖出', '卖券还款']:
        return '卖出'
    else:
        raise ValueError('{} 不是有效的买卖标记'.format(flag))


def _replace_order_status(flag):
    if flag in ['全成', '全成(全部成交)']:
        return '已成'
    elif flag in ['全撤', '全撤(全部撤单)']:
        return '已撤'
    elif '已报' in flag:
        return '已报'
    elif flag in ['部撤', '部撤(部成部撤)']:
        return '部成'
    elif flag in ['待报']:   # -1: 待审核
        return '待报'
    elif flag in ['待撤']:
        return '待撤'
    elif flag.startswith('拒绝'):
        return '废单'
    else:
        raise ValueError(f'委托状态:  \n{flag} \n不在范围中')