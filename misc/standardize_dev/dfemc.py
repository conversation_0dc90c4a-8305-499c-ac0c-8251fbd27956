from misc.utils import to_datetime_formmats
from .stand_utils import std_filter_ticker_asint


# 东财 EMC 终端自动导出数据

def std_hold(df):
    df = df.rename(columns={
        '证券代码'     :     'ticker',
        # '代码'        :      'ticker',
        
        '持仓数量'     :     'volume',
        # '持仓量'       :     'volume',
        
        '可用数量'     :     'available_volume',
        # '可用余额'     :     'available_volume',
        
    })
    # ticker 000001
    # print(df.dtypes)
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # print(remainder)
    
    return df

    
def std_order(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'   :    'ticker',  ###
        '委托方向'  :     'BS_flag', ###
        '委托价格'   :    'order_price',
        '委托数量'   :    'order_volume',
        '成交均价'   :    'fill_price',
        '成交数量'   :    'fill_volume',
        
        # '订单日期'   :    'date',   # no date
        '委托时间'  :     'time',   
        '委托状态'   :    'order_status',  ###
        '委托编号'  :     'order_ref',
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # date
    df['date'] = date
    # order_status
    df['order_status'] = df['order_status'].apply(_replace_order_status)

    return df

def std_deal(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'  :     'ticker',  ###
        '委托方向'  :     'BS_flag',  ###
        '成交价格'   :    'fill_price',
        '成交数量'   :    'fill_volume',
        
        # '成交日期'   :    'date',  ### no date
        '成交时间'  :     'time',
        '委托编号'  :     'order_ref',
        '成交编号'  :     'deal_ref',
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # date
    df['date'] = date
    return df
    
def std_deal_minimal(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'  :     'ticker',  ###
        '委托方向'  :     'BS_flag',  ###
        '成交价格'   :    'fill_price',
        '成交数量'   :    'fill_volume',
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    return df
    
def std_account(df, date, **kwargs):
    df = df.rename(columns={
        # '日期'        :     'date',    ###
        '总资产'       :     'net_asset',
        '总市值'       :     'stock_value',
        '可用资金'     :     'available_fund',
        '当日盈亏'     :     'pnl(client)'
    })
    df['date'] = date
    return df

def _replace_order_status(flag):
    if flag == '已成':
        return '已成'
    elif flag in ['全撤', '撤单', '已撤']:
        return '已撤'
    elif flag in ['已报', '正报']:
        return '已报'
    elif flag in ['部成', '部撤']:
        return '部成'
    elif flag == '废单':
        return '废单'
    else:
        raise ValueError(f'委托状态:  \n{flag} \n不在范围中')
    
def _replace_BS_flag(flag):
    if flag == '证券买入':
        return '买入'
    elif flag == '证券卖出':
        return '卖出'
    else:
        raise ValueError('{} 不是有效的买卖标记'.format(flag))