from .stand_utils import std_filter_ticker_asint
from misc.utils import to_datetime_formmats

import pandas as pd 

def std_hold(df):
    df = df.rename(columns={
        '证券代码'     :     'ticker',
        '代码'        :      'ticker',
        
        '当前拥股'     :     'volume',
        '持仓量'       :     'volume',
        
        '可用数量'     :     'available_volume',
        '可用余额'     :     'available_volume',
        '可用持仓'     :     'available_volume',  # gt 收益互换
        
    })
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    return df

    
def std_order(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'     :     'ticker',  ###
        '买卖标记'     :     'BS_flag',  ###
        '委托价格'     :    'order_price',
        '委托价格[人民币]'  : 'order_price',  # for 收益互换
        '委托量'       :    'order_volume',
        '成交均价'     :    'fill_price',
        '成交均价[人民币]' : 'fill_price',  # for 收益互换
        '成交数量'   :    'fill_volume',
        
        '委托日期'   :    'date',
        '委托时间'  :     'time',   ###
        '委托状态'   :    'order_status',  ###
        '订单编号'  :     'order_ref',
        # '合同编号'  :     'order_ref', # for gt
    })
    # for gt
    if 'order_ref' not in df.columns:
        df.rename(columns={'合同编号': 'order_ref'}, inplace=True)

    # for pb client
    if 'order_ref' not in df.columns:
        df.rename(columns={'合同编号': 'order_ref'}, inplace=True)
    if 'BS_flag' not in df.columns:
        df.rename(columns={'操作': 'BS_flag'}, inplace=True)
    if 'ticker' not in df.columns:
        df.rename(columns={'代码': 'ticker'}, inplace=True)
    if 'date' not in df.columns:
        df['date'] = date
    
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H:%M:%S']).apply(lambda x: x.strftime("%H:%M:%S"))
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)    
    # order_status
    df['order_status'] = df['order_status'].apply(_replace_order_status)
    return df
    
def std_deal(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'     :     'ticker',  ###
        '买卖标记'     :     'BS_flag',  ###
        '成交价格'      :    'fill_price',
        '成交价格[人民币]'  : 'fill_price',  # for 收益互换
        '成交数量'   :    'fill_volume',
        
        '成交日期'   :    'date',
        '成交时间'  :     'time',   ###
        '订单编号'  :     'order_ref',
        # '合同编号'  :     'order_ref', # for gt
        '成交编号'  :     'deal_ref',
    })
    # for gt
    if 'order_ref' not in df.columns:
        df.rename(columns={'合同编号': 'order_ref'}, inplace=True)

    # for pb client
    if 'order_ref' not in df.columns:
        df.rename(columns={'合同编号': 'order_ref'}, inplace=True)

    if 'BS_flag' not in df.columns:
        df.rename(columns={'操作': 'BS_flag'}, inplace=True)
    if 'ticker' not in df.columns:
        df.rename(columns={'代码': 'ticker'}, inplace=True)
    
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H:%M:%S']).apply(lambda x: x.strftime("%H:%M:%S"))
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)    
    return df

def std_deal_minimal(df, date, **kwargs):
    df = df.rename(columns={
        '证券代码'     :     'ticker',  ###
        '买卖标记'     :     'BS_flag',  ###
        '成交价格'   :    'fill_price',
        '成交价格[人民币]'  : 'fill_price',  # for 收益互换
        '成交数量'   :    'fill_volume',
    })
    if 'BS_flag' not in df.columns:
        df.rename(columns={'操作': 'BS_flag'}, inplace=True)
    if 'ticker' not in df.columns:
        df.rename(columns={'代码': 'ticker'}, inplace=True)
    # ticker 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)    
    return df

def std_account(df, date, **kwargs):
    df = df.rename(columns={
        # '日期'        :     'date',    ###
        '总资产'          :     'net_asset',
        '总资产[人民币]'   :     'net_asset',  # for 收益互换
        '股票总市值'       :     'stock_value',
        'A股市值'       :     'stock_value',
        '总市值[人民币]'    :     'stock_value',  # for 收益互换
        '可用金额'            :     'available_fund',
        '证券可用资金'            :     'available_fund',
        '可用金额[人民币]'     :     'available_fund',  # for 收益互换
        '今日账号盈亏'   :    'pnl(client)'
    })
    df['date'] = date
    return df




def _replace_order_status(flag):
    if flag == '已成':
        return '已成'
    elif flag == '已撤':
        return '已撤'
    elif flag in ['已报', '已报待撤']:
        return '已报'
    elif flag in ['部撤', '部成']:
        return '部成'
    elif flag == '废单':
        return '废单'
    else:
        raise ValueError(f'委托状态:  \n{flag} \n不在范围中')
    
def _replace_BS_flag(flag):
    if flag in ['限价买入',
                '买入',
                '最优五档即时成交剩余撤销买入',
                '对手方最优价格买入',
                '最优五档即时成交剩余撤销担保品买入',
                '买券还券',
                '担保品买入',
                '限价'
        ]:
        return '买入'
    elif flag in ['限价卖出',
                '卖出',
                '最优五档即时成交剩余撤销卖出',
                '对手方最优价格卖出',
                '最优五档即时成交剩余撤销担保品卖出',
                '担保品卖出',
                  ]:
        return '卖出'
    else:
        raise ValueError('{} 不是有效的买卖标记'.format(flag))




# === futures ==================================================================
def std_future_hold(df:pd.DataFrame, **kwargs):
    if '多空' in df.columns:
        df = df.rename(columns={
            '多空'        :     'longshort',
            '总持仓'       :     'volume',
        })
    elif '买卖' in df.columns:
        df = df.rename(columns={
            '买卖'        :     'longshort',
            '持仓量'      :     'volume',
        })
    
    df = df.rename(columns={
        '合约代码'     :     'ticker',
        # '多空'        :     'longshort',
        # '总持仓'       :     'volume',
        '今仓'        :     'today_volume',
    })
    # ticker ag2501
    # longshort flag
    df['longshort'] = df['longshort'].map(_replace_future_hold_longshort)
    
    if df['today_volume'].dtype == 'object':
        # print(df[['ticker','today_volume']])
        # print(df.dtypes)
        df['today_volume'] = df['today_volume'].map({'是': 1, '否': 0}).astype(int)
    df = df[['ticker', 'longshort', 'volume', 'today_volume']].groupby(['ticker', 'longshort']).sum().reset_index()
    df = df[df['volume'] != 0]
    return df


def std_deal_future(df, date, **kwargs):
    df = df.rename(columns={
        '合约代码'     :     'ticker',
        '买卖方向'     :     'BS_flag',
        # '多空'        :     'longshort',
        '开平'        :     'operation',
        
        '成交均价'   :    'fill_price',
        # '成交价格[人民币]'  : 'fill_price',  # for 收益互换
        '成交量'    :    'fill_volume',
        
        '成交日期'   :    'date',
        '成交时间'  :     'time',      # ok
        '报单编号'  :     'order_ref',
        # '编号'  :     'order_ref', # for gt
        '成交编号'  :     'deal_ref',
        
    })
    df['date'] = df['date'].astype(str)
    df['time'] = df['time'].astype(str)
    df['deal_ref'] = df['deal_ref'].astype(str)
    df['order_ref'] = df['order_ref'].astype(str)
    
    # df['longshort'] = df['longshort'].map(_replace_future_longshort)
    df['BS_flag'] = df['BS_flag'].map(_replace_BS_flag)
    df['operation'] = df['operation'].map(_replace_future_operation)
    df['longshort'] = (df['BS_flag'] + df['operation']).map(_replace_future_deal_longshort)

    return df
    
def std_order_future(df, date, **kwargs):
    df = df.rename(columns={
        '合约代码'     :     'ticker',
        '多空'        :     'longshort',
        '开平'        :     'operation',  # ok
        '委托状态'      :    'order_status',  # convert
        
        '委托号'       :     'order_ref',
        '委托量'      :     'order_volume',
        '限价'        :     'order_price',
        '成交数量'     :     'fill_volume',
        '成交均价'     :    'fill_price',
        
        '报单日期'      :    'date',
        '委托时间'     :     'time',      # ok
    })
    # longshort
    df['longshort'] = df['longshort'].map(_replace_future_hold_longshort)
    # operation
    df['operation'] = df['operation'].map(_replace_future_operation)
    # date
    df['date'] = df['date'].astype(str)
    # time
    df['time'] = df['time'].astype(str)
    # order_status
    df['order_status'] = df['order_status'].map(_replace_order_status)

    return df

    
def std_account_future(df, date, **kwargs):
    cols_dict = {
        '期初权益'     :     'begin_rights',
        '动态权益'     :     'dynamic_rights',
        '可用资金'     :     'available_fund',
        '持仓盈亏'     :     'holding_pnl',
        '平仓盈亏'     :     'settle_pnl',
        '出入金净值'     :    'transfer_fund',
        '当前保证金'     :    'current_margin',
        '交易日'       :     'date',
    }
    df = df.rename(columns=cols_dict)
    df = df[cols_dict.values()]
    return df
    
    
def _replace_future_operation(flag):
    if flag in ['开仓',]:
        return '开仓'
    elif flag in ['平仓', '平今']:
        return '平仓'
    else:
        raise ValueError('unknown future operation flag: {}'.format(flag))
    
def _replace_future_deal_longshort(flag):
    if flag in ['买入开仓', '卖出平仓']:
        return '多头'
    elif flag in ['买入平仓', '卖出开仓']:
        return '空头'
    else:
        raise ValueError('unknown longshort flag from: {}'.format(flag))


def _replace_future_hold_longshort(flag):
    if flag in ['多头', '多']:
        return '多头'
    elif flag in ['空头', '空']:
        return '空头'
    else:
        raise ValueError('unknown longshort flag from: {}'.format(flag))