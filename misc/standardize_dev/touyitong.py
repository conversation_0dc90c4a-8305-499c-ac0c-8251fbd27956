
from .stand_utils import std_filter_ticker_asint
from misc.utils import to_datetime_formmats

def std_hold(df):
    df = df.rename(columns={
        'stkcode'        : 'ticker',
        'stkholdqty'     : 'volume',
        'stkavl'         : 'available_volume',
    })
    
    # ticker 000001.SZ -> 000001
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    return df

def std_order(df, date, **kwargs):       # SubOrderAlgo
    df = df.rename(columns={
            'ordertime'     : 'time',    ###
            'stkcode'        : 'ticker',  ###
            'bsflag'          : 'BS_flag', ###
            'orderstatus'     : 'order_status', ###
            'orderqty'        : 'order_volume',
            'orderprice'       : 'order_price',
            
            'matchqty'        : 'fill_volume',
            # 'AvgPx'         : 'fill_price',
            'orderextno'       : 'order_ref',
            # 'date'          : 'date',
            'matchamt'        : 'fill_amount',
    })
    # 光大多空ATX 空头账户特殊处理
    # df.loc[df['ClientName'].str.contains('_rq'), 'BS_flag'] = df.loc[df['ClientName'].str.contains('_rq'), 'BS_flag'].map(_replace_BS_flag_GuangDa_short)
    
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H:%M:%S'])
    # .apply(lambda x: x.strftime("%H:%M:%S"))
    df['date'] = date
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    # order_status
    df['order_status'] = df['order_status'].apply(_replace_order_status)
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # avg price
    df['fill_price'] = df['fill_amount'].div(df['order_volume'], fill_value=0)
        
    # 去掉代报
    df.dropna(subset=['order_ref'], inplace=True)
    # date 
    # df['date'] = date
    # adjust 部成
    # mask_should_be_partially_filled = (df['fill_volume'] != 0) & (df['order_status'] == '已撤')
    # df.loc[mask_should_be_partially_filled, 'order_status'] = '部成'
    return df

def std_deal(df, date, **kwargs):    # SubOrderAlgo
    df = df.rename(columns={
            'stkcode'         : 'ticker',  ###
            'bsflag'          : 'BS_flag', ###
            'matchqty'        : 'fill_volume',
            'matchprice'      : 'fill_price',
            
            # 'date'          : 'date',
            'matchtime'     : 'time',    ###
            'orderextno'    : 'order_ref',
            'match_no'      : 'deal_ref',  
    })
    
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # time & date
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%H:%M:%S'])
    df['date'] = date
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # deal_ref
        
    # 去掉代报
    df.dropna(subset=['order_ref'], inplace=True)
    # 过滤撤单成交
    df = df[df['fill_price'] != 0]
    df = df[df['matchtype'].isin(['0', 0])]
  
    return df

def std_deal_minimal(df, date, **kwargs):    # SubOrderAlgo
    df = df.rename(columns={
            'stkcode'            : 'ticker',  ###
            'bsflag'             : 'BS_flag', ###
            'matchqty'           : 'fill_volume',
            'matchprice'         : 'fill_price',
    })
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='')
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # 过滤撤单成交
    df = df[df['fill_price'] != 0]
    df = df[df['matchtype'].isin(['0', 0])]
    
    return df
    
def std_account(df, date, **kwargs):
    df = df.rename(columns={
        # '日期'        :     'date',    ###
        # '多头可用保证金' :     'available_margin',
        'instravl'     :     'available_fund',
        'stkasset'     :     'market_value',
        'netasset'     :     'net_asset',
        
    })
    df['date'] = date
    
    return df
    










def _replace_BS_flag(flag):
    if flag in ['0B',]:
        flag = '买入'
    elif flag in ['0S',]:
        flag = '卖出'
    elif flag in ['1B',]:
        flag = '平空'
    elif flag in ['1S',]:
        flag = '卖空'
    elif flag in ['买入', '卖出', '平空', '卖空', '无效']:
        flag = flag
    # elif flag in [-1,]:
    #     flag = '无效'
    else:
        raise ValueError('买卖标记:  \n{} \n不在范围中'.format(flag))
    return flag


def _replace_order_status(status):
    if status in [0, '0']:
        status = '等待报单'  # 原未报
    elif status in [1, '1']:
        status = '等待报单'  # 原正报
    elif status in [2, '2']:
        status = '已报'     
    elif status in [3, '3']:
        status = '已报'      # 原已报待撤
    elif status in [4, '4']:
        status = '部成'      # 原部成待撤
    elif status in [5, '5']:
        status = '部成'      # 原部撤
    elif status in [6, '6']:
        status = '已撤'      # 原已撤
    elif status in [7, '7']:
        status = '部成'      # 原部成
    elif status in [8, '8']:
        status = '已成'      # 原已成
    elif status in [9, '9']:
        status = '废单'      # 原废单
    elif status in ['A']:
        status = '等待报单'   # 原待报
    else:
        raise ValueError('委托状态:  \n{} \n不在范围中'.format(status))
    return status
