from .stand_utils import std_filter_ticker_asint
from ..utils import to_datetime_formmats

def std_hold(df):
    df.rename(columns={
        '标的代码': 'ticker',
        '合约持仓': 'volume',
        '可平仓数量': 'available_volume',
        '买卖方向': 'direction',
    }, inplace=True)

    if not df.empty:
        df['direction'] = df['direction'].apply(lambda x: 1 if x == '买入' else -1)
        df['volume'] = (df['volume'].multiply(df['direction'])).astype(int)
        df['available_volume'] = (df['available_volume'].multiply(df['direction'])).astype(int)

        # ticker 000001
        # 合并多账号
        df = df[['ticker', 'volume', 'available_volume']].groupby(['ticker']).sum().reset_index()
    
        df, remainder = std_filter_ticker_asint(df, src_suffix='')
    else:
        df = df[['ticker', 'volume', 'available_volume']]
    return df
