from .stand_utils import std_filter_ticker_asint
from misc.utils import to_datetime_formmats

def std_hold(df):
    df = df.rename(columns={
        'Symbol'        : 'ticker',
        'CurrentQ'      : 'volume',
        'EnableQty'     : 'available_volume',
    })
    
    # ticker 000001.SZ -> 000001
    try:
        df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    except ValueError:
        # logger.warning('')
        df, remainder = std_filter_ticker_asint(df, src_suffix='')
    return df

def std_order(df, date, **kwargs):       # SubOrderAlgo
    df = df.rename(columns={
            'TransTime'     : 'time',    ###
            'Symbol'        : 'ticker',  ###
            'Side'          : 'BS_flag', ###
            'OrdStatus'     : 'order_status', ###
            'OrderQty'      : 'order_volume',
            'Price'         : 'order_price',
            'CumQty'        : 'fill_volume',
            'AvgPx'         : 'fill_price',
            'ClOrdId'       : 'order_ref',
            # 'date'          : 'date',
    })
    # 光大多空ATX 空头账户特殊处理
    df.loc[df['ClientName'].str.contains('_rq'), 'BS_flag'] = df.loc[df['ClientName'].str.contains('_rq'), 'BS_flag'].map(_replace_BS_flag_GuangDa_short)
    
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # time
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y%m%d%H%M%S%f'])
    # .apply(lambda x: x.strftime("%H:%M:%S"))
    # df['date'] = df['time'].apply(lambda x: x.strftime("%Y%m%d"))
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    # order_status
    df['order_status'] = df['order_status'].apply(_replace_order_status)
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # date 
    df['date'] = date
    # adjust 部成
    mask_should_be_partially_filled = (df['fill_volume'] != 0) & (df['order_status'] == '已撤')
    df.loc[mask_should_be_partially_filled, 'order_status'] = '部成'
    return df
    
def std_deal(df, date, **kwargs):    # SubOrderAlgo
    df = df.rename(columns={
            'Symbol'        : 'ticker',  ###
            'Side'          : 'BS_flag', ###
            'CumQty'        : 'fill_volume',
            'AvgPx'         : 'fill_price',
            
            # 'date'          : 'date',
            'TransTime'     : 'time',    ###
            'ClOrdId'       : 'order_ref',
            # ''     : 'deal_ref',  # 复制一份
    })
    
    # 光大多空ATX 空头账户特殊处理
    df.loc[df['ClientName'].str.contains('_rq'), 'BS_flag'] = df.loc[df['ClientName'].str.contains('_rq'), 'BS_flag'].map(_replace_BS_flag_GuangDa_short)
    
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # time & date
    df['time'] = to_datetime_formmats(df['time'], source_type='time', try_formats=['%Y%m%d%H%M%S%f'])
    df['date'] = df['time'].apply(lambda x: x.strftime("%Y%m%d"))
    df['time'] = df['time'].apply(lambda x: x.strftime("%H:%M:%S"))
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    # deal_ref
    df['deal_ref'] = df['order_ref']
    return df
    
def std_deal_minimal(df, date, **kwargs):    # SubOrderAlgo
    df = df.rename(columns={
            'Symbol'        : 'ticker',  ###
            'Side'          : 'BS_flag', ###
            'CumQty'        : 'fill_volume',
            'AvgPx'         : 'fill_price',
    })
    # 光大多空ATX 空头账户特殊处理
    df.loc[df['ClientName'].str.contains('_rq'), 'BS_flag'] = df.loc[df['ClientName'].str.contains('_rq'), 'BS_flag'].map(_replace_BS_flag_GuangDa_short)
    
    # ticker 000001.SZ
    df, remainder = std_filter_ticker_asint(df, src_suffix='.XX')
    # BS_flag
    df['BS_flag'] = df['BS_flag'].apply(_replace_BS_flag)
    return df
    
def std_account(df, date, **kwargs):
    df = df.rename(columns={
        # '日期'        :     'date',    ###
        # '多头可用保证金' :     'available_margin',
        'EnBalance'     :     'available_fund',
    })
    df['date'] = date
    if 'AssetAmt' in df.columns:
        df.rename(columns={'AssetAmt': 'net_asset'}, inplace=True)
    if 'MarketAmt' in df.columns:
        df.rename(columns={'MarketAmt': 'market_value'}, inplace=True)
    
    return df
    
    
def _replace_BS_flag(flag):
    if flag in ['买入', '卖出', '平空', '卖空', '无效']:
        flag = flag
    elif flag in [1,]:
        flag = '买入'
    elif flag in [2,]:
        flag = '卖出'
    elif flag in [3,]:
        flag = '平空'
    elif flag in [4,]:
        flag = '卖空'
    elif flag in [-1,]:
        flag = '无效'
    else:
        raise ValueError('买卖标记:  \n{} \n不在范围中'.format(flag))
    return flag

def _replace_BS_flag_GuangDa_short(flag):
    if flag in [1,]:
        flag = '平空'
    elif flag in [2,]:
        flag = '卖空'
    else:
        raise ValueError('买卖标记:  \n{} \n不在范围中'.format(flag))
    return flag

def _replace_order_status(status):
    if status in [1]:
        status = '部成'
    elif status in [2]:
        status = '已成'
    elif status in [4]:
        status = '已撤'
    elif status in [0]:
        status = '已报'
    elif status in [6]:
        status = '待撤'
    elif status in [8, 7]:
        status = '废单'
    elif status in [10]:
        status = '等待报单'
    else:
        raise ValueError('委托状态:  \n{} \n不在范围中'.format(status))
    return status
