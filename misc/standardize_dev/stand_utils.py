import pandas as pd
from loguru import logger
from misc import utils
# from misc.utils import re_find
# from typing import Literal
try:
    from typing import Literal
except ImportError:
    from typing_extensions import Literal

pd.set_option('display.unicode.ambiguous_as_wide', True)
pd.set_option('display.unicode.east_asian_width', True)
pd.set_option('display.width', 180)



def std_filter_ticker_asint(df: pd.DataFrame, src_prefix:Literal['']='', src_suffix:Literal['', '.XX']=''):
    if 'ticker' in df.columns:
        col = 'ticker'
    elif '代码' in df.columns:
        col = '代码'
    elif '证券代码' in df.columns:
        col = '证券代码'
    else:
        raise ValueError('匹配不到证券代码标志, 现有columns: {}'.format(df.columns))
        
    if (df[col].dtype == 'int' or df[col].dtype == 'float') and (not df.empty):
        # 过滤非股票
        mask_stock = df[col].astype(int).astype(str).str.zfill(6).apply(lambda x: utils.re_find('^0|^30|^6', x)).astype(bool)
        remainder = df[~mask_stock]
        df = df[mask_stock].copy()
        df[col] = df[col].astype(int)
    elif df[col].dtype == 'object' and (not df.empty):
        # 过滤非股票
        if src_suffix == '.XX':
            df[[col, 'tmp_col']] = df[col].str.split('.', expand=True)
            df.drop('tmp_col', axis=1, inplace=True)
            mask_stock = df[col].str.zfill(6).apply(lambda x: utils.re_find('^0|^3|^6', x)).astype(bool)
            remainder = df[~mask_stock]
            df = df[mask_stock].copy()
            df[col] = df[col].astype(int)
            
        else:
            mask_stock = df[col].str.zfill(6).apply(lambda x: utils.re_find('^0|^3|^6', x)).astype(bool)
            remainder = df[~mask_stock]
            df = df[mask_stock].copy()
            df[col] = df[col].astype(int)
            
        # df = df[ df[col].str.zfill(6).apply(lambda x: re_find('^0|^3|^6', x)).astype(bool) ]
        # df[col] = df[col].astype(int)
    elif df.empty:
        logger.info('hold df is empty')
        remainder = pd.DataFrame()
    else:
        print(df.head(2))
        print(df[col].dtype)
        raise TypeError('ticker type error')
    if len(remainder) > 0:
        logger.warning('持仓过滤出的ticker: \n{}'.format(remainder[[col]]))
    return df, remainder