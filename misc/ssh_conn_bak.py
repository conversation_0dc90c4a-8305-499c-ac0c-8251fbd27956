import os
import socket
import paramiko
import sshtunnel
from loguru import logger
# from typing import Literal
import threading

try:
    from typing import Literal
except ImportError:
    from typing_extensions import Literal

from misc.zzw_tun import tun_kf_trade_dict
from accounts_config.sys_config import SYS_USER

private_key_path = f'/home/<USER>/.ssh/id_rsa'
private_key = paramiko.RSAKey.from_private_key_file(private_key_path)



ssh_conf_dict = {
    'dav' : {
        'conf' : {
            # 'hostname': '*************',
            'hostname': '*************',
            'username': 'trade',
            'port': 2022,
            'private_key_path': private_key_path
        },
        'prefix' : '/'
    },
    'outer' : {
        'conf' : {
            'hostname': '**************',
            'username': 'root',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : '/data/ftp'
    },
    'inner' : {
        'conf' : {
            'hostname': '************',
            'username': 'root',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : '/home/<USER>/signal/product',
        # 'prefix' : '/home/<USER>/signal/account_system/product'
    },
    'ninner' : {
        'conf' : {
            'hostname': '************',
            'username': 'root',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : '/home/<USER>/signal/account_system/product'
    },
    'trade' : {
        'conf' : {
            'hostname': '************',
            'username': 'admin',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : 'C:'
    },
    'trade2' : {
        'conf' : {
            'hostname': '*************',
            'username': 'admin',
            'port': 17021,
            'private_key_path': private_key_path
        },
        'prefix' : 'C:'
    },
    'trade3' : {
        'conf' : {
            'hostname': '127.0.0.1',
            'username': 'admin',
            'port': 16061,
            'private_key_path': private_key_path
        },
        'prefix' : 'D:'
    },
    'gtapi' : {
        # old aws
        # 'conf' : {
        #     'hostname': '*************',
        #     'username': 'api',
        #     'port': 2022,
        #     'private_key_path': private_key_path
        # },
        # 'prefix' : '/product'

        # new jeo ftp
        'conf' : {
            'hostname': '**************',
            'username': 'kf_trader',
            'port': 2022,
            'private_key_path': private_key_path,
            'password': 'kf_trader@KF2024'
        },
        'prefix' : '/product'
    },
    'aws' : {
        'conf' : {
            'hostname': '**********',
            'username': 'zhanw',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : 'C:/Users/<USER>/trade'
    },
    'work' : {
        'conf' : {
            'hostname': '*************',
            'username': 'admin',
            'port': 22,
            'private_key_path': private_key_path
        },
        'prefix' : 'D:/交易生产'
    },
}



class SFTP_CLIENT:
    def __init__(self, remote: Literal[
            'dav', 
            'outer', 
            'inner', 
            'trade',
            'trade2', 
            'trade3', 
            'gtapi',
            'aws',
            'work',
            'ninner'
            ]):
        ssh_conf = ssh_conf_dict[remote]['conf']
        prefix = ssh_conf_dict[remote]['prefix']
        password = ssh_conf.get('password')

        self._lock = threading.Lock()
        self.remote_prefix = prefix
        self.remote = remote
        self.host = ssh_conf['hostname']
        self.username = ssh_conf['username']
        self.password = ssh_conf.get('password')
        self.private_key_path = ssh_conf.get('private_key_path')
        self.port = ssh_conf['port']
        self.transport = None
        self.sftp = None


    def _connect(self):
        private_key = paramiko.RSAKey(filename=self.private_key_path)
        self.transport = paramiko.Transport((self.host, self.port))
        self.transport.set_keepalive(60)
        self.transport.connect(username=self.username, pkey=private_key, password=self.password)
        self.sftp = paramiko.SFTPClient.from_transport(self.transport)

    def _reconnect(self):
        self.close()
        self._connect()

    def _is_connected(self):
        return self.transport is not None and self.transport.is_active() and self.sftp is not None

    def close(self):
        if self._is_connected():
            self.sftp.close()
            self.transport.close()
            self.sftp = None
            self.transport = None

    def put(self, local_path, remote_path, confirm=True):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        # logger.info('try copying to {} \nsource: {} \ndest : {}'.format(self.remote, local_path, remote_path))
        with self._lock:
            try:
                if not self._is_connected():
                    self._connect()

                self.sftp.put(local_path, remote_path, confirm=confirm)
            except FileNotFoundError:
                raise FileNotFoundError('remote file not found error: \nsource:{} \ndest: {}'.format(local_path, remote_path))
            except (paramiko.SSHException, socket.error):
                # Reconnect and retry
                self._reconnect()
                self.sftp.put(local_path, remote_path, confirm=confirm)
            # logger.info('copied to remote path finished: {}'.format(remote_path))


    def get(self, remote_path, local_path):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        # logger.info('try copying from {} \nsource: {} \ndest : {}'.format(self.remote, remote_path, local_path))

        with self._lock:
            try:
                if not self._is_connected():
                    self._connect()
                self.sftp.get(remote_path, local_path)
            except FileNotFoundError:
                raise FileNotFoundError('remote file not found error: \nsource:{} \ndest: {}'.format(local_path, remote_path))
            except (paramiko.SSHException, socket.error):
                # Reconnect and retry
                self._reconnect()
                self.sftp.get(remote_path, local_path)        
            # logger.info('copied from remote path finished: {}'.format(remote_path))

    def mkdir(self, remote_path):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        # logger.info('try making directory {} \ndirectory: {}'.format(self.remote, remote_path))
        with self._lock:
            try:
                if not self._is_connected():
                    self._connect()
                self.sftp.mkdir(remote_path)
            except FileNotFoundError:
                raise FileNotFoundError('mkdir remote file not found: {}'.format(remote_path))
            except (paramiko.SSHException, socket.error):
                # Reconnect and retry
                self._reconnect()
                self.sftp.mkdir(remote_path)
            logger.info('mkdir finished: {}'.format(remote_path))

    def chmod(self, remote_path, mode=511):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        # logger.info('try making directory {} \ndirectory: {}'.format(self.remote, remote_path))
        with self._lock:
            try:
                if not self._is_connected():
                    self._connect()
                self.sftp.chmod(remote_path, mode)
            except FileNotFoundError:
                raise FileNotFoundError('chmod, remote file not found: {}'.format(remote_path))
            except (paramiko.SSHException, socket.error):
                # Reconnect and retry
                self._reconnect()
                self.sftp.chmod(remote_path, mode)
            logger.info('chmod finished: {}'.format(remote_path))

    def listdir(self, remote_path=''):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        # logger.info('try listing {} \ndirectory: {}'.format(self.remote, remote_path))
        # check if lock is ready:
        with self._lock:
            try:
                if not self._is_connected():
                    self._connect()
                result = self.sftp.listdir(remote_path)
            except FileNotFoundError:
                raise FileNotFoundError('remote file not found: {}'.format(remote_path))
            except (paramiko.SSHException, socket.error):
                # Reconnect and retry
                self._reconnect()
                result = self.sftp.listdir(remote_path)
            return result

    def remove(self, remote_path=''):
        remote_path = os.path.join(self.remote_prefix, remote_path)
        # logger.info('try listing {} \ndirectory: {}'.format(self.remote, remote_path))
        with self._lock:
            try:
                if not self._is_connected():
                    self._connect()
                self.sftp.remove(remote_path)
            except FileNotFoundError:
                raise FileNotFoundError('remote file not found: {}'.format(remote_path))
            except (paramiko.SSHException, socket.error):
                # Reconnect and retry
                self._reconnect()
                self.sftp.remove(remote_path)
            logger.info('remove finished: {}'.format(remote_path))
    
    def exe_command(self, command):
        # remote_path = os.path.join(self.remote_prefix, remote_path)
        with self._lock:
            logger.info('try executing command: {} '.format(command))

            if not self._is_connected():
                self._connect()
            ssh = paramiko.SSHClient()
            ssh._transport = self.transport
            # ssh_session = self.transport.open_session()
            # Execute the command on the remote host
            stdin, stdout, stderr = ssh.exec_command(command)

            print("Output:")
            print(stdout.read())

            print("Errors:")
            print(stderr.read())

    def cp_at_remotedir(self, src, dest):
        remote_src = os.path.join(self.remote_prefix, src)
        remote_dest = os.path.join(self.remote_prefix, dest)
        logger.info('try copy at remote {}: \nsource: {} \ndest : {}'.format(self.remote, remote_src, remote_dest))
        commmand = f'cp {remote_src} {remote_dest}'
        print(commmand)

        with self._lock:
            try:
                self.exe_command(f'cp {remote_src} {remote_dest}')
            except FileNotFoundError:
                raise FileNotFoundError('remote file not found error: \nsource:{} \ndest: {}'.format(remote_src, remote_dest))
            # logger.info('copied to remote path finished: {}'.format(remote_path))
        
        
    
sftp_clent_inner = SFTP_CLIENT('inner')
sftp_clent_ninner = SFTP_CLIENT('ninner')
sftp_clent_outer = SFTP_CLIENT('outer')
sftp_clent_dav = SFTP_CLIENT('dav')
sftp_clent_trade = SFTP_CLIENT('trade')
sftp_clent_trade2 = SFTP_CLIENT('trade2')
sftp_clent_trade3 = SFTP_CLIENT('trade3')
sftp_clent_gtapi = SFTP_CLIENT('gtapi')
sftp_clent_aws = SFTP_CLIENT('aws')
sftp_clent_work = SFTP_CLIENT('work')


sftp_methods = {
    'inner': sftp_clent_inner,
    'outer': sftp_clent_outer,
    'dav'  : sftp_clent_dav,
    'trade': sftp_clent_trade,
    'trade2': sftp_clent_trade2,
    'trade3': sftp_clent_trade3,
    'gtapi': sftp_clent_gtapi,
    'aws'  : sftp_clent_aws,
    'work' : sftp_clent_work,
    'ninner': sftp_clent_ninner
}    


